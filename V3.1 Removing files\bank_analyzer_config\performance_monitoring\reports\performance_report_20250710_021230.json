{"report_period": {"start_time": "2025-07-09T02:12:30.403116", "end_time": "2025-07-10T02:12:30.403116", "days_covered": 1}, "summary_statistics": {"total_predictions": 0, "average_accuracy": 0.0, "average_processing_time_ms": 0.0, "source_distribution": {}}, "current_system_health": {"status": "critical", "manual_priority_system": "critical", "hierarchical_classifier": "warning", "standard_ml_system": "healthy", "rule_based_system": "healthy", "overall_accuracy": 1.0, "issues": ["No manual labels in priority cache", "Hierarchical classifier not trained", "High fallback rate: 50.00%"], "recommendations": ["Load manual labels into priority system", "Train hierarchical classifier with sufficient data", "Improve ML model coverage and accuracy"]}, "performance_trends": {"trend": "no_data"}, "error_analysis": {"error_rate": 0.0, "errors_by_source": {}, "low_confidence_rate": 0.5, "total_failed": 0, "patterns": ["No error patterns", "Low confidence rate: 50.00%"]}, "recommendations": ["Improve ML model coverage and accuracy", "Load manual labels into priority system", "Train hierarchical classifier with sufficient data"], "generated_at": "2025-07-10T02:12:30.415391"}