"""
Base class for bank-specific parsers
Provides common functionality for parsing bank-specific statement formats
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from decimal import Decimal
import re

from .base_parser import BaseStatementParser
from ..models.transaction import RawTransaction
from ..core.logger import get_logger


class BankSpecificParser(BaseStatementParser, ABC):
    """
    Abstract base class for bank-specific parsers
    Provides common functionality for handling bank-specific formats
    """
    
    def __init__(self, bank_name: str):
        super().__init__(bank_name)
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Bank-specific configuration
        self.date_formats = self._get_date_formats()
        self.column_mappings = self._get_column_mappings()
        self.transaction_patterns = self._get_transaction_patterns()
        
    @abstractmethod
    def _get_date_formats(self) -> List[str]:
        """
        Get list of date formats used by this bank
        
        Returns:
            List of date format strings (e.g., ['%d %b %Y', '%d/%m/%Y'])
        """
        pass
    
    @abstractmethod
    def _get_column_mappings(self) -> Dict[str, Any]:
        """
        Get column mappings for this bank's statement format
        
        Returns:
            Dictionary mapping standard fields to bank-specific column info
        """
        pass
    
    @abstractmethod
    def _get_transaction_patterns(self) -> Dict[str, str]:
        """
        Get transaction patterns specific to this bank
        
        Returns:
            Dictionary of regex patterns for transaction types
        """
        pass
    
    def parse_date(self, date_str: str) -> Optional[date]:
        """
        Parse date string using bank-specific date formats
        
        Args:
            date_str: Date string to parse
            
        Returns:
            Parsed date object or None if parsing fails
        """
        if not date_str or date_str.strip() == "":
            return None
        
        # Clean the date string
        date_str = str(date_str).strip()
        
        # Try each date format for this bank
        for date_format in self.date_formats:
            try:
                parsed_date = datetime.strptime(date_str, date_format).date()
                return parsed_date
            except ValueError:
                continue
        
        # If bank-specific formats fail, try common formats
        common_formats = [
            '%Y-%m-%d',
            '%d/%m/%Y',
            '%m/%d/%Y',
            '%d-%m-%Y',
            '%Y/%m/%d'
        ]
        
        for date_format in common_formats:
            try:
                parsed_date = datetime.strptime(date_str, date_format).date()
                return parsed_date
            except ValueError:
                continue
        
        self.logger.warning(f"Could not parse date: {date_str}")
        return None
    
    def parse_amount(self, amount_str: str) -> Optional[Decimal]:
        """
        Parse amount string to Decimal
        
        Args:
            amount_str: Amount string to parse
            
        Returns:
            Parsed amount as Decimal or None if parsing fails
        """
        if not amount_str or str(amount_str).strip() in ["", "-", "0", "0.00"]:
            return None
        
        try:
            # Clean the amount string
            amount_str = str(amount_str).strip()
            
            # Remove currency symbols and commas
            amount_str = re.sub(r'[₹$,\s]', '', amount_str)
            
            # Handle negative amounts
            is_negative = amount_str.startswith('-') or amount_str.endswith('-')
            amount_str = amount_str.replace('-', '')
            
            # Convert to decimal
            amount = Decimal(amount_str)
            
            if is_negative:
                amount = -amount
                
            return amount
            
        except (ValueError, TypeError) as e:
            self.logger.warning(f"Could not parse amount: {amount_str} - {str(e)}")
            return None
    
    def determine_transaction_type(self, debit_amount: Optional[Decimal], 
                                 credit_amount: Optional[Decimal]) -> str:
        """
        Determine transaction type based on debit/credit amounts
        
        Args:
            debit_amount: Debit amount (if any)
            credit_amount: Credit amount (if any)
            
        Returns:
            Transaction type string
        """
        if debit_amount and debit_amount > 0:
            return "DEBIT"
        elif credit_amount and credit_amount > 0:
            return "CREDIT"
        else:
            return "UNKNOWN"
    
    def clean_description(self, description: str) -> str:
        """
        Clean transaction description
        
        Args:
            description: Raw description string
            
        Returns:
            Cleaned description string
        """
        if not description:
            return ""
        
        # Convert to string and strip whitespace
        description = str(description).strip()
        
        # Replace multiple whitespaces with single space
        description = re.sub(r'\s+', ' ', description)
        
        # Remove newlines and replace with spaces
        description = description.replace('\n', ' ').replace('\r', ' ')
        
        return description
    
    def create_raw_transaction(self, 
                             transaction_date: date,
                             description: str,
                             debit_amount: Optional[Decimal],
                             credit_amount: Optional[Decimal],
                             balance: Optional[Decimal] = None,
                             reference_number: Optional[str] = None,
                             source_file: Optional[str] = None,
                             source_line: Optional[int] = None) -> RawTransaction:
        """
        Create a RawTransaction object from parsed data
        
        Args:
            transaction_date: Transaction date
            description: Transaction description
            debit_amount: Debit amount (if any)
            credit_amount: Credit amount (if any)
            balance: Account balance after transaction
            reference_number: Reference number
            source_file: Source file path
            source_line: Source line number
            
        Returns:
            RawTransaction object
        """
        # Determine final amount and transaction type
        if debit_amount and debit_amount > 0:
            final_amount = -debit_amount  # Debit is negative
            transaction_type = "DEBIT"
        elif credit_amount and credit_amount > 0:
            final_amount = credit_amount   # Credit is positive
            transaction_type = "CREDIT"
        else:
            final_amount = Decimal('0')
            transaction_type = "UNKNOWN"
        
        return RawTransaction(
            date=transaction_date,
            description=self.clean_description(description),
            amount=final_amount,
            balance=balance,
            transaction_type=transaction_type,
            reference_number=reference_number,
            source_file=source_file,
            source_line=source_line,
            bank_name=self.bank_name
        )
    
    def validate_transaction(self, transaction: RawTransaction) -> bool:
        """
        Validate a parsed transaction
        
        Args:
            transaction: Transaction to validate
            
        Returns:
            True if transaction is valid, False otherwise
        """
        if not transaction.date:
            self.add_parsing_error("Missing transaction date")
            return False
        
        if not transaction.description or transaction.description.strip() == "":
            self.add_parsing_error("Missing transaction description")
            return False
        
        if transaction.amount == 0:
            self.add_parsing_error("Zero amount transaction")
            return False
        
        return True
