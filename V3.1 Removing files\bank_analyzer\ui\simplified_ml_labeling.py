"""
Simplified ML Labeling Window without sessions or caching
Direct data access only
"""

from PySide6.QtWidgets import (QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
                               QLabel, QPushButton, QComboBox, QSpinBox, QTextEdit,
                               QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
                               QMenuBar, QStatusBar, QProgressBar, QSplitter, QGroupBox,
                               QFormLayout, QLineEdit, QDateEdit, QCheckBox, QSlider,
                               QFrame, QScrollArea, QDialog, QDialogButtonBox, QListWidget,
                               QListWidgetItem, QTabWidget, QGridLayout, QDoubleSpinBox,
                               QFileDialog, QApplication, QRadioButton, QMenu)
from PySide6.QtCore import Qt, <PERSON><PERSON>ime<PERSON>, <PERSON>, QDate, QThread
from PySide6.QtGui import QFont, QAction, QColor, QIcon

import logging
from typing import List, Optional, Dict, Any
from pathlib import Path

from ..core.direct_data_access import DirectDataAccess, ProcessedTransactionData
from ..ml.data_preparation import TransactionDataPreparator
from ..ml.category_manager import CategoryManager
from ..models.transaction import RawTransaction
from ..core.logger import get_logger
from .loading_screen import DataLoadingScreen


class ResizableHeaderView(QHeaderView):
    """Custom header view with right-click context menu for column resizing and drag-to-resize support"""

    def __init__(self, orientation, parent=None):
        super().__init__(orientation, parent)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # Enable drag-to-resize functionality
        self.setSectionsMovable(False)  # Don't allow column reordering
        self.setSectionsClickable(True)  # Allow clicking on headers
        self.setHighlightSections(True)  # Highlight sections on hover
        self.setDefaultSectionSize(100)  # Default column width

    def show_context_menu(self, position):
        """Show context menu with resize options"""
        menu = QMenu(self)

        # Get the logical index of the column at the position
        logical_index = self.logicalIndexAt(position)
        if logical_index < 0:
            return

        # Column resize options
        resize_to_contents_action = QAction("📏 Resize to Contents", self)
        resize_to_contents_action.triggered.connect(lambda: self.resizeSection(logical_index, self.sectionSizeHint(logical_index)))
        menu.addAction(resize_to_contents_action)

        stretch_action = QAction("↔️ Stretch Column", self)
        stretch_action.triggered.connect(lambda: self.setSectionResizeMode(logical_index, QHeaderView.Stretch))
        menu.addAction(stretch_action)

        fixed_action = QAction("📌 Fixed Size", self)
        fixed_action.triggered.connect(lambda: self.setSectionResizeMode(logical_index, QHeaderView.Interactive))
        menu.addAction(fixed_action)

        menu.addSeparator()

        # All columns options
        resize_all_action = QAction("📏 Resize All to Contents", self)
        resize_all_action.triggered.connect(self.resize_all_to_contents)
        menu.addAction(resize_all_action)

        reset_action = QAction("🔄 Reset Column Sizes", self)
        reset_action.triggered.connect(self.reset_column_sizes)
        menu.addAction(reset_action)

        menu.exec(self.mapToGlobal(position))

    def resize_all_to_contents(self):
        """Resize all columns to their contents"""
        for i in range(self.count()):
            self.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def reset_column_sizes(self):
        """Reset column sizes to default"""
        # Set default resize modes
        if self.count() >= 6:  # Standard table with 6 columns
            self.setSectionResizeMode(0, QHeaderView.Stretch)  # Description
            for i in range(1, self.count()):
                self.setSectionResizeMode(i, QHeaderView.ResizeToContents)


class DataLoadingWorker(QThread):
    """Background worker for data loading operations"""

    # Signals
    loading_progress = Signal(str, int, str, int, int)  # stage, progress, message, count, total
    loading_completed = Signal(list, list)  # raw_transactions, unique_transactions
    loading_error = Signal(str)

    def __init__(self, data_access, parent=None):
        super().__init__(parent)
        self.data_access = data_access
        self._should_stop = False
        self.logger = get_logger(__name__)

    def run(self):
        """Execute data loading in background"""
        try:
            self.logger.info("Starting background data loading...")

            # Stage 1: Check for processed data
            self.loading_progress.emit("loading", 10, "Checking for processed data...", 0, 0)

            if self._should_stop:
                self.logger.info("Data loading stopped by user request")
                return

            # Check if processed data is available (new stateless approach)
            self.logger.info("Checking for processed data...")
            if not self.data_access.has_processed_data():
                self.loading_error.emit("No processed data found. Please process bank statement data first.")
                return

            self.loading_progress.emit("loading", 30, "Loading processed transactions...", 0, 0)

            # Load RAW transactions for manual labeling (new stateless approach)
            self.logger.info("Loading raw transactions for manual labeling...")
            raw_transactions = self.data_access.get_latest_raw_transactions()
            if not raw_transactions:
                self.loading_error.emit("No raw transactions found. Please process bank statement data first.")
                return

            self.logger.info(f"Loaded {len(raw_transactions)} raw transactions for manual labeling")

            if self._should_stop:
                self.logger.info("Data loading stopped by user request")
                return

            # Convert raw transaction data to format expected by the UI
            self.loading_progress.emit("preparing", 90, "Preparing data for display...", len(raw_transactions), len(raw_transactions))

            if self._should_stop:
                self.logger.info("Data loading stopped by user request")
                return

            # Use proper data preparation to group similar transactions and calculate frequencies
            self.loading_progress.emit("converting", 85, "Grouping similar transactions and calculating frequencies...", 0, len(raw_transactions))

            if self._should_stop:
                self.logger.info("Data loading stopped by user request")
                return

            from ..ml.data_preparation import TransactionDataPreparator

            # Create data preparator instance
            data_preparator = TransactionDataPreparator("manual_labeling")

            # Progress callback for the data preparation
            def prep_progress_callback(current, total, message):
                if self._should_stop:
                    return
                progress = int(85 + (current / total) * 10)  # 85-95% range
                self.loading_progress.emit("converting", progress, f"Processing transactions... {message}", current, total)

            # Extract unique transactions with proper frequency and amount range calculation
            unique_transactions_dict = data_preparator.extract_unique_transactions(raw_transactions, prep_progress_callback)

            if self._should_stop:
                self.logger.info("Data loading stopped by user request")
                return

            # Convert dictionary to list for UI compatibility
            unique_transactions = list(unique_transactions_dict.values())

            # Add additional fields needed by the UI
            for unique_txn in unique_transactions:
                # Ensure category fields are empty for manual labeling
                if not hasattr(unique_txn, 'category') or unique_txn.category is None:
                    unique_txn.category = ""
                if not hasattr(unique_txn, 'sub_category') or unique_txn.sub_category is None:
                    unique_txn.sub_category = ""
                if not hasattr(unique_txn, 'confidence'):
                    unique_txn.confidence = 0.0
                if not hasattr(unique_txn, 'is_manually_labeled'):
                    unique_txn.is_manually_labeled = False
                if not hasattr(unique_txn, 'labeled_by'):
                    unique_txn.labeled_by = ""
                if not hasattr(unique_txn, 'labeled_at'):
                    unique_txn.labeled_at = None

            # Sort by frequency
            self.logger.info("Sorting transactions by frequency...")
            unique_transactions.sort(key=lambda x: x.frequency, reverse=True)

            self.loading_progress.emit("complete", 100, "Loading complete!", len(unique_transactions), len(unique_transactions))

            # Emit completion with converted data
            self.logger.info(f"Data loading completed successfully: {len(unique_transactions)} unique transactions")
            self.loading_completed.emit([], unique_transactions)  # Empty raw transactions list since we're using processed data

        except Exception as e:
            self.logger.error(f"Data loading error: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            self.loading_error.emit(f"Unexpected error during data loading: {str(e)}")

    def stop(self):
        """Request the worker to stop"""
        self._should_stop = True


class FilterWorker(QThread):
    """Background worker for filtering operations"""

    filter_completed = Signal(list)  # Emits filtered transactions
    filter_progress = Signal(int)    # Emits progress percentage
    filter_error = Signal(str)       # Emits error message

    def __init__(self, transactions, filter_criteria, parent=None):
        super().__init__(parent)
        self.transactions = transactions
        self.filter_criteria = filter_criteria
        self._should_stop = False

    def run(self):
        """Execute filtering in background"""
        try:
            filtered_transactions = []
            total = len(self.transactions)

            for i, txn in enumerate(self.transactions):
                if self._should_stop:
                    return

                if self._transaction_matches_filter(txn, self.filter_criteria):
                    filtered_transactions.append(txn)

                # Update progress every 100 transactions
                if i % 100 == 0:
                    progress = int((i + 1) / total * 100)
                    self.filter_progress.emit(progress)

            self.filter_completed.emit(filtered_transactions)

        except Exception as e:
            self.filter_error.emit(str(e))

    def stop(self):
        """Request the worker to stop"""
        self._should_stop = True

    def _transaction_matches_filter(self, transaction, filter_obj):
        """Check if a transaction matches the filter criteria"""
        try:
            # Transaction type filter
            if filter_obj.transaction_type != "All":
                # Use cached predominant type if available
                if not hasattr(transaction, '_cached_predominant_type'):
                    transaction._cached_predominant_type = transaction.get_predominant_transaction_type()
                predominant_type = transaction._cached_predominant_type
                if filter_obj.transaction_type.lower() != predominant_type:
                    return False

            # Category filter
            if filter_obj.category != "All Categories":
                txn_category = getattr(transaction, 'category', '') or ''
                if txn_category != filter_obj.category:
                    return False

            # Sub-category filter
            if filter_obj.sub_category != "All Sub-categories":
                txn_subcategory = getattr(transaction, 'sub_category', '') or ''
                if txn_subcategory != filter_obj.sub_category:
                    return False

            # Frequency filters
            if filter_obj.min_frequency and transaction.frequency < filter_obj.min_frequency:
                return False
            if filter_obj.max_frequency and transaction.frequency > filter_obj.max_frequency:
                return False

            # Amount filters
            if filter_obj.min_amount:
                min_amount = min(transaction.amount_range)
                if min_amount < filter_obj.min_amount:
                    return False
            if filter_obj.max_amount:
                max_amount = max(transaction.amount_range)
                if max_amount > filter_obj.max_amount:
                    return False

            # Date filters (check against first_seen and last_seen)
            if filter_obj.start_date and transaction.last_seen < filter_obj.start_date:
                return False
            if filter_obj.end_date and transaction.first_seen > filter_obj.end_date:
                return False

            # Labeled status filter
            if filter_obj.labeled_status != "All":
                is_labeled = bool(getattr(transaction, 'category', None))
                if filter_obj.labeled_status == "Labeled" and not is_labeled:
                    return False
                elif filter_obj.labeled_status == "Unlabeled" and is_labeled:
                    return False

            # Search filter
            if filter_obj.search_text:
                if not self._matches_search_criteria(transaction, filter_obj):
                    return False

            return True

        except Exception as e:
            return True  # Include transaction if filter check fails

    def _matches_search_criteria(self, transaction, filter_obj):
        """Check if transaction matches search criteria"""
        import re

        search_text = filter_obj.search_text
        search_mode = filter_obj.search_mode
        case_sensitive = filter_obj.case_sensitive

        # Get transaction description
        description = transaction.description

        # Apply case sensitivity
        if not case_sensitive:
            description = description.lower()
            search_text = search_text.lower()

        try:
            if search_mode == "Contains (partial match)":
                return search_text in description

            elif search_mode == "Exact word match":
                # Use word boundaries to match exact words
                pattern = r'\b' + re.escape(search_text) + r'\b'
                flags = 0 if case_sensitive else re.IGNORECASE
                return bool(re.search(pattern, transaction.description, flags))

            elif search_mode == "All words must be present":
                # Split search text into words and check if all are present
                search_words = search_text.split()
                for word in search_words:
                    word = word.strip()  # Remove any extra whitespace
                    if not word:  # Skip empty words
                        continue
                    if word not in description:
                        return False
                return len([w for w in search_words if w.strip()]) > 0  # Ensure at least one valid word

            elif search_mode == "Regex pattern":
                # Use regex pattern matching
                flags = 0 if case_sensitive else re.IGNORECASE
                return bool(re.search(search_text, transaction.description, flags))

            else:
                # Default to contains
                return search_text in description

        except re.error:
            # If regex is invalid, fall back to contains
            return search_text in description
        except Exception:
            # If any other error, include the transaction
            return True


class TransactionFilter:
    """Filter criteria for transactions"""
    def __init__(self):
        self.transaction_type = "All"
        self.category = "All Categories"
        self.sub_category = "All Sub-categories"
        self.min_frequency = None
        self.max_frequency = None
        self.min_amount = None
        self.max_amount = None
        self.start_date = None
        self.end_date = None
        self.labeled_status = "All"  # All, Labeled, Unlabeled

        # Search parameters
        self.search_text = ""
        self.search_mode = "Contains (partial match)"  # Contains, Exact word, All words, Regex
        self.case_sensitive = False


class ComprehensiveFilterPanel(QWidget):
    """Comprehensive filtering panel with all filter options"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.setup_ui()

    def setup_ui(self):
        """Setup the filter panel UI"""
        layout = QVBoxLayout(self)

        # Filter title
        title = QLabel("🔍 Transaction Filters")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 5px;")
        layout.addWidget(title)

        # Create filter groups
        self.create_search_filters(layout)
        self.create_type_filters(layout)
        self.create_category_filters(layout)
        self.create_frequency_filters(layout)
        self.create_amount_filters(layout)
        self.create_date_filters(layout)
        self.create_status_filters(layout)

        # Apply/Reset buttons
        button_layout = QHBoxLayout()

        self.apply_btn = QPushButton("🔍 Apply Filters")
        self.apply_btn.setStyleSheet("background-color: #3498db; color: white; padding: 8px; border-radius: 4px;")
        button_layout.addWidget(self.apply_btn)

        self.reset_btn = QPushButton("🔄 Reset Filters")
        self.reset_btn.setStyleSheet("background-color: #95a5a6; color: white; padding: 8px; border-radius: 4px;")
        button_layout.addWidget(self.reset_btn)

        layout.addLayout(button_layout)
        layout.addStretch()

    def create_type_filters(self, layout):
        """Create transaction type filters"""
        group = QGroupBox("Transaction Type")
        group_layout = QFormLayout(group)

        self.type_combo = QComboBox()
        self.type_combo.addItems(["All", "Debit", "Credit", "Mixed"])
        group_layout.addRow("Type:", self.type_combo)

        layout.addWidget(group)

    def create_search_filters(self, layout):
        """Create comprehensive search filters"""
        group = QGroupBox("🔍 Search Transactions")
        group_layout = QVBoxLayout(group)

        # Search input
        search_layout = QHBoxLayout()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter search terms...")
        search_layout.addWidget(self.search_input)

        # Clear search button
        clear_search_btn = QPushButton("✖")
        clear_search_btn.setMaximumWidth(30)
        clear_search_btn.setToolTip("Clear search")
        clear_search_btn.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_search_btn)

        group_layout.addLayout(search_layout)

        # Search mode selection
        mode_layout = QFormLayout()

        self.search_mode_combo = QComboBox()
        self.search_mode_combo.addItems([
            "Contains (partial match)",
            "Exact word match",
            "All words must be present",
            "Regex pattern"
        ])
        mode_layout.addRow("Search Mode:", self.search_mode_combo)

        # Case sensitivity toggle
        self.case_sensitive_checkbox = QCheckBox("Case sensitive")
        mode_layout.addRow("Options:", self.case_sensitive_checkbox)

        group_layout.addLayout(mode_layout)

        # Search help text
        help_text = QLabel("💡 Tip: Use 'Regex pattern' for advanced searches like '^UPI.*ZOMATO' or '\\d{4,}'")
        help_text.setStyleSheet("font-size: 10px; color: #7f8c8d; padding: 2px;")
        help_text.setWordWrap(True)
        group_layout.addWidget(help_text)

        layout.addWidget(group)

    def clear_search(self):
        """Clear the search input"""
        self.search_input.clear()



    def on_filter_category_changed(self, category_name):
        """Handle category selection change in filter panel"""
        try:
            # Get current transaction type filter based on active tab
            filter_type = self.get_current_transaction_type_filter()
            self.update_subcategory_list(category_name, self.subcategory_combo, filter_type)
        except Exception as e:
            self.logger.error(f"Error updating sub-categories: {e}")

    def update_subcategory_list(self, category_name, subcategory_combo, filter_by_type=None):
        """Update sub-category dropdown based on selected category"""
        try:
            # Clear current sub-categories
            subcategory_combo.clear()
            subcategory_combo.addItem("All Sub-categories")

            if category_name == "All Categories" or not category_name:
                return

            # Get sub-categories for the selected main category
            if hasattr(self, 'category_manager') and self.category_manager:
                all_categories = self.category_manager.get_all_categories()

                # Find the main category
                main_category = None
                for cat in all_categories:
                    if cat.name == category_name and cat.parent_id is None:
                        main_category = cat
                        break

                if main_category:
                    # Get sub-categories for this main category
                    sub_categories = [cat for cat in all_categories if cat.parent_id == main_category.id]

                    # Filter by category type if specified
                    if filter_by_type:
                        sub_categories = [cat for cat in sub_categories if getattr(cat, 'category_type', None) == filter_by_type]

                    sub_categories.sort(key=lambda x: x.name)

                    for sub_cat in sub_categories:
                        subcategory_combo.addItem(sub_cat.name)

        except Exception as e:
            self.logger.error(f"Error updating sub-category list: {e}")

    def on_details_category_changed(self, category_name):
        """Handle category selection change in details panel"""
        try:
            # Get current transaction type filter based on active tab
            filter_type = self.get_current_transaction_type_filter()
            self.update_subcategory_list(category_name, self.subcategory_combo, filter_type)
        except Exception as e:
            self.logger.error(f"Error updating details sub-categories: {e}")

    def get_current_transaction_type_filter(self):
        """Get the transaction type filter based on current tab"""
        try:
            if hasattr(self, 'transaction_tabs'):
                current_tab = self.transaction_tabs.currentIndex()
                if current_tab == 1:  # Credits Only tab
                    return "income"
                elif current_tab == 2:  # Debits Only tab
                    return "expense"
            return None  # All transactions tab or no filtering
        except Exception as e:
            self.logger.error(f"Error getting transaction type filter: {e}")
            return None

    def create_category_filters(self, layout):
        """Create category and sub-category filters"""
        group = QGroupBox("Categories")
        group_layout = QFormLayout(group)

        self.category_combo = QComboBox()
        self.category_combo.addItem("All Categories")
        self.category_combo.currentTextChanged.connect(self.on_filter_category_changed)
        group_layout.addRow("Category:", self.category_combo)

        self.subcategory_combo = QComboBox()
        self.subcategory_combo.addItem("All Sub-categories")
        group_layout.addRow("Sub-category:", self.subcategory_combo)

        layout.addWidget(group)

    def create_frequency_filters(self, layout):
        """Create frequency range filters"""
        group = QGroupBox("Frequency Range")
        group_layout = QFormLayout(group)

        self.min_freq_spin = QSpinBox()
        self.min_freq_spin.setMinimum(0)
        self.min_freq_spin.setMaximum(9999)
        self.min_freq_spin.setSpecialValueText("No minimum")
        group_layout.addRow("Min frequency:", self.min_freq_spin)

        self.max_freq_spin = QSpinBox()
        self.max_freq_spin.setMinimum(0)
        self.max_freq_spin.setMaximum(9999)
        self.max_freq_spin.setSpecialValueText("No maximum")
        group_layout.addRow("Max frequency:", self.max_freq_spin)

        layout.addWidget(group)

    def create_amount_filters(self, layout):
        """Create amount range filters"""
        group = QGroupBox("Amount Range (₹)")
        group_layout = QFormLayout(group)

        self.min_amount_spin = QDoubleSpinBox()
        self.min_amount_spin.setMinimum(0.0)
        self.min_amount_spin.setMaximum(999999999.99)
        self.min_amount_spin.setSpecialValueText("No minimum")
        self.min_amount_spin.setDecimals(2)
        group_layout.addRow("Min amount:", self.min_amount_spin)

        self.max_amount_spin = QDoubleSpinBox()
        self.max_amount_spin.setMinimum(0.0)
        self.max_amount_spin.setMaximum(999999999.99)
        self.max_amount_spin.setSpecialValueText("No maximum")
        self.max_amount_spin.setDecimals(2)
        group_layout.addRow("Max amount:", self.max_amount_spin)

        layout.addWidget(group)

    def create_date_filters(self, layout):
        """Create date range filters"""
        group = QGroupBox("Date Range")
        group_layout = QFormLayout(group)

        # Use checkboxes to enable/disable date filters with proper default dates
        start_date_layout = QHBoxLayout()
        self.start_date_enabled = QCheckBox("Enable")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate(2020, 2, 1))  # Set to 1/2/2020 (day/month/year)
        self.start_date_edit.setEnabled(False)  # Disabled by default
        start_date_layout.addWidget(self.start_date_enabled)
        start_date_layout.addWidget(self.start_date_edit)
        group_layout.addRow("Start date:", start_date_layout)

        # Connect checkbox to enable/disable date edit
        self.start_date_enabled.toggled.connect(self.start_date_edit.setEnabled)

        end_date_layout = QHBoxLayout()
        self.end_date_enabled = QCheckBox("Enable")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate(2030, 2, 1))  # Set to 1/2/2030 (day/month/year)
        self.end_date_edit.setEnabled(False)  # Disabled by default
        end_date_layout.addWidget(self.end_date_enabled)
        end_date_layout.addWidget(self.end_date_edit)
        group_layout.addRow("End date:", end_date_layout)

        # Connect checkbox to enable/disable date edit
        self.end_date_enabled.toggled.connect(self.end_date_edit.setEnabled)

        layout.addWidget(group)

    def create_status_filters(self, layout):
        """Create labeled status filters"""
        group = QGroupBox("Labeling Status")
        group_layout = QFormLayout(group)

        self.status_combo = QComboBox()
        self.status_combo.addItems(["All", "Labeled", "Unlabeled"])
        group_layout.addRow("Status:", self.status_combo)

        layout.addWidget(group)

    def get_filter_criteria(self):
        """Get current filter criteria"""
        filter_obj = TransactionFilter()

        filter_obj.transaction_type = self.type_combo.currentText()
        filter_obj.category = self.category_combo.currentText()
        filter_obj.sub_category = self.subcategory_combo.currentText()

        # Only apply frequency filters if value is greater than 0 (since 0 means "no limit")
        filter_obj.min_frequency = self.min_freq_spin.value() if self.min_freq_spin.value() > 0 else None
        filter_obj.max_frequency = self.max_freq_spin.value() if self.max_freq_spin.value() > 0 else None

        # Only apply amount filters if value is greater than 0 (since 0 means "no limit")
        filter_obj.min_amount = self.min_amount_spin.value() if self.min_amount_spin.value() > 0.0 else None
        filter_obj.max_amount = self.max_amount_spin.value() if self.max_amount_spin.value() > 0.0 else None

        # Use checkbox state to determine if date filters are active
        filter_obj.start_date = self.start_date_edit.date().toPython() if self.start_date_enabled.isChecked() else None
        filter_obj.end_date = self.end_date_edit.date().toPython() if self.end_date_enabled.isChecked() else None

        filter_obj.labeled_status = self.status_combo.currentText()

        # Search parameters
        filter_obj.search_text = self.search_input.text().strip()
        filter_obj.search_mode = self.search_mode_combo.currentText()
        filter_obj.case_sensitive = self.case_sensitive_checkbox.isChecked()

        return filter_obj

    def reset_filters(self):
        """Reset all filters to default values"""
        # Search filters
        self.search_input.clear()
        self.search_mode_combo.setCurrentText("Contains (partial match)")
        self.case_sensitive_checkbox.setChecked(False)

        # Other filters
        self.type_combo.setCurrentText("All")
        self.category_combo.setCurrentText("All Categories")
        self.subcategory_combo.setCurrentText("All Sub-categories")
        self.min_freq_spin.setValue(0)
        self.max_freq_spin.setValue(0)
        self.min_amount_spin.setValue(0.0)
        self.max_amount_spin.setValue(0.0)
        self.start_date_enabled.setChecked(False)  # Disable date filters
        self.end_date_enabled.setChecked(False)  # Disable date filters
        self.start_date_edit.setDate(QDate(2004, 2, 1))  # Reset to 1/2/2004
        self.end_date_edit.setDate(QDate(2030, 2, 1))  # Reset to 1/2/2030
        self.status_combo.setCurrentText("All")


class SimplifiedMLLabelingWindow(QMainWindow):
    """
    Simplified ML Labeling Window without sessions
    Uses direct data access to load the most recent processed data
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.data_access = DirectDataAccess()
        self.data_preparator = TransactionDataPreparator()
        self.category_manager = CategoryManager()
        
        # Data storage
        self.raw_transactions = []
        self.unique_transactions = []
        self.filtered_transactions = []
        self.current_index = 0
        
        # UI components
        self.transaction_table = None
        self.description_text = None
        self.category_combo = None
        self.subcategory_combo = None
        self.filter_panel = None
        self.stats_label = None

        # Current filter criteria
        self.current_filter = TransactionFilter()

        # Pagination variables for All Transactions tab
        self.current_page = 1
        self.page_size = 50
        self.total_pages = 1
        self.page_size_combo = None
        self.page_info_label = None
        self.first_page_btn = None
        self.prev_page_btn = None
        self.next_page_btn = None
        self.last_page_btn = None

        # Pagination variables for Credits tab
        self.credits_current_page = 1
        self.credits_page_size = 50
        self.credits_total_pages = 1
        self.credits_filtered_transactions = []

        # Pagination variables for Debits tab
        self.debits_current_page = 1
        self.debits_page_size = 50
        self.debits_total_pages = 1
        self.debits_filtered_transactions = []

        # Background filtering
        self.filter_worker = None

        # Background data loading
        self.data_loading_worker = None
        self.loading_screen = None

        # Debouncing timer for filter changes
        self.filter_debounce_timer = QTimer()
        self.filter_debounce_timer.setSingleShot(True)
        self.filter_debounce_timer.timeout.connect(self._apply_filters_debounced)

        self.setup_ui()
        self.setup_menus()

        # Initialize category lists even before data is loaded
        # This allows users to see available categories immediately
        QTimer.singleShot(100, self.refresh_category_lists)

        # Don't auto-load data - let user load manually
        # QTimer.singleShot(1000, self.auto_load_latest_data)

    def create_credits_tab_content(self, layout):
        """Create content for the Credits Only tab"""
        # Credits table
        self.credits_table = QTableWidget()
        self.credits_table.setColumnCount(7)
        self.credits_table.setHorizontalHeaderLabels([
            "Description", "Frequency", "Type", "Amount Range", "Category", "Sub-category", "Status"
        ])

        # Make table sortable and selectable
        self.credits_table.setSortingEnabled(True)
        self.credits_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.credits_table.itemSelectionChanged.connect(self.on_credits_transaction_selected)

        # Set custom resizable header
        resizable_header = ResizableHeaderView(Qt.Horizontal, self.credits_table)
        self.credits_table.setHorizontalHeader(resizable_header)

        # Set default column resize modes - Interactive allows drag-to-resize
        resizable_header.setSectionResizeMode(0, QHeaderView.Interactive)  # Description - draggable
        resizable_header.setSectionResizeMode(1, QHeaderView.Interactive)  # Frequency - draggable
        resizable_header.setSectionResizeMode(2, QHeaderView.Interactive)  # Type - draggable
        resizable_header.setSectionResizeMode(3, QHeaderView.Interactive)  # Amount Range - draggable
        resizable_header.setSectionResizeMode(4, QHeaderView.Interactive)  # Category - draggable
        resizable_header.setSectionResizeMode(5, QHeaderView.Interactive)  # Sub-category - draggable
        resizable_header.setSectionResizeMode(6, QHeaderView.Interactive)  # Status - draggable

        # Set initial column widths
        self.credits_table.setColumnWidth(0, 300)  # Description
        self.credits_table.setColumnWidth(1, 80)   # Frequency
        self.credits_table.setColumnWidth(2, 100)  # Type
        self.credits_table.setColumnWidth(3, 120)  # Amount Range
        self.credits_table.setColumnWidth(4, 120)  # Category
        self.credits_table.setColumnWidth(5, 120)  # Sub-category
        self.credits_table.setColumnWidth(6, 100)  # Status

        layout.addWidget(self.credits_table)

        # Credits pagination
        credits_pagination = QHBoxLayout()

        credits_pagination.addWidget(QLabel("Rows per page:"))
        self.credits_page_size_combo = QComboBox()
        self.credits_page_size_combo.addItems(["25", "50", "100", "200", "All"])
        self.credits_page_size_combo.setCurrentText("50")
        self.credits_page_size_combo.currentTextChanged.connect(self.on_credits_page_size_changed)
        credits_pagination.addWidget(self.credits_page_size_combo)

        credits_pagination.addStretch()

        self.credits_first_page_btn = QPushButton("⏮️ First")
        self.credits_first_page_btn.clicked.connect(self.go_to_credits_first_page)
        credits_pagination.addWidget(self.credits_first_page_btn)

        self.credits_prev_page_btn = QPushButton("⏪ Previous")
        self.credits_prev_page_btn.clicked.connect(self.go_to_credits_previous_page)
        credits_pagination.addWidget(self.credits_prev_page_btn)

        self.credits_page_info_label = QLabel("Page 1 of 1")
        self.credits_page_info_label.setStyleSheet("font-weight: bold; padding: 0 10px;")
        credits_pagination.addWidget(self.credits_page_info_label)

        self.credits_next_page_btn = QPushButton("Next ⏩")
        self.credits_next_page_btn.clicked.connect(self.go_to_credits_next_page)
        credits_pagination.addWidget(self.credits_next_page_btn)

        self.credits_last_page_btn = QPushButton("Last ⏭️")
        self.credits_last_page_btn.clicked.connect(self.go_to_credits_last_page)
        credits_pagination.addWidget(self.credits_last_page_btn)

        layout.addLayout(credits_pagination)

    def create_debits_tab_content(self, layout):
        """Create content for the Debits Only tab"""
        # Debits table
        self.debits_table = QTableWidget()
        self.debits_table.setColumnCount(7)
        self.debits_table.setHorizontalHeaderLabels([
            "Description", "Frequency", "Type", "Amount Range", "Category", "Sub-category", "Status"
        ])

        # Make table sortable and selectable
        self.debits_table.setSortingEnabled(True)
        self.debits_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.debits_table.itemSelectionChanged.connect(self.on_debits_transaction_selected)

        # Set custom resizable header
        resizable_header = ResizableHeaderView(Qt.Horizontal, self.debits_table)
        self.debits_table.setHorizontalHeader(resizable_header)

        # Set default column resize modes - Interactive allows drag-to-resize
        resizable_header.setSectionResizeMode(0, QHeaderView.Interactive)  # Description - draggable
        resizable_header.setSectionResizeMode(1, QHeaderView.Interactive)  # Frequency - draggable
        resizable_header.setSectionResizeMode(2, QHeaderView.Interactive)  # Type - draggable
        resizable_header.setSectionResizeMode(3, QHeaderView.Interactive)  # Amount Range - draggable
        resizable_header.setSectionResizeMode(4, QHeaderView.Interactive)  # Category - draggable
        resizable_header.setSectionResizeMode(5, QHeaderView.Interactive)  # Sub-category - draggable
        resizable_header.setSectionResizeMode(6, QHeaderView.Interactive)  # Status - draggable

        # Set initial column widths
        self.debits_table.setColumnWidth(0, 300)  # Description
        self.debits_table.setColumnWidth(1, 80)   # Frequency
        self.debits_table.setColumnWidth(2, 100)  # Type
        self.debits_table.setColumnWidth(3, 120)  # Amount Range
        self.debits_table.setColumnWidth(4, 120)  # Category
        self.debits_table.setColumnWidth(5, 120)  # Sub-category
        self.debits_table.setColumnWidth(6, 100)  # Status

        layout.addWidget(self.debits_table)

        # Debits pagination
        debits_pagination = QHBoxLayout()

        debits_pagination.addWidget(QLabel("Rows per page:"))
        self.debits_page_size_combo = QComboBox()
        self.debits_page_size_combo.addItems(["25", "50", "100", "200", "All"])
        self.debits_page_size_combo.setCurrentText("50")
        self.debits_page_size_combo.currentTextChanged.connect(self.on_debits_page_size_changed)
        debits_pagination.addWidget(self.debits_page_size_combo)

        debits_pagination.addStretch()

        self.debits_first_page_btn = QPushButton("⏮️ First")
        self.debits_first_page_btn.clicked.connect(self.go_to_debits_first_page)
        debits_pagination.addWidget(self.debits_first_page_btn)

        self.debits_prev_page_btn = QPushButton("⏪ Previous")
        self.debits_prev_page_btn.clicked.connect(self.go_to_debits_previous_page)
        debits_pagination.addWidget(self.debits_prev_page_btn)

        self.debits_page_info_label = QLabel("Page 1 of 1")
        self.debits_page_info_label.setStyleSheet("font-weight: bold; padding: 0 10px;")
        debits_pagination.addWidget(self.debits_page_info_label)

        self.debits_next_page_btn = QPushButton("Next ⏩")
        self.debits_next_page_btn.clicked.connect(self.go_to_debits_next_page)
        debits_pagination.addWidget(self.debits_next_page_btn)

        self.debits_last_page_btn = QPushButton("Last ⏭️")
        self.debits_last_page_btn.clicked.connect(self.go_to_debits_last_page)
        debits_pagination.addWidget(self.debits_last_page_btn)

        layout.addLayout(debits_pagination)

    def on_tab_changed(self, index):
        """Handle tab change event"""
        try:
            # Update filtered transactions for the new tab
            if index == 0:  # All Transactions
                # Already have filtered_transactions
                pass
            elif index == 1:  # Credits Only
                self.update_credits_filtered_transactions()
            elif index == 2:  # Debits Only
                self.update_debits_filtered_transactions()

            # Refresh category lists based on new tab (transaction type filtering)
            self.refresh_category_lists()

            # Refresh the current tab's table
            self.refresh_current_tab()

        except Exception as e:
            self.logger.error(f"Error changing tabs: {e}")

    def update_credits_filtered_transactions(self):
        """Update filtered transactions for Credits tab"""
        if not self.filtered_transactions:
            self.credits_filtered_transactions = []
            return

        # Filter for credit transactions only
        self.credits_filtered_transactions = [
            txn for txn in self.filtered_transactions
            if txn.get_predominant_transaction_type().lower() == 'credit'
        ]

        # Reset pagination
        self.credits_current_page = 1

    def update_debits_filtered_transactions(self):
        """Update filtered transactions for Debits tab"""
        if not self.filtered_transactions:
            self.debits_filtered_transactions = []
            return

        # Filter for debit transactions only
        self.debits_filtered_transactions = [
            txn for txn in self.filtered_transactions
            if txn.get_predominant_transaction_type().lower() == 'debit'
        ]

        # Reset pagination
        self.debits_current_page = 1

    def refresh_current_tab(self):
        """Refresh the currently active tab"""
        current_tab = self.transaction_tabs.currentIndex()

        if current_tab == 0:  # All Transactions
            self.populate_table()
        elif current_tab == 1:  # Credits Only
            self.populate_credits_table()
        elif current_tab == 2:  # Debits Only
            self.populate_debits_table()

    # Pagination methods
    def on_page_size_changed(self, size_text):
        """Handle page size change"""
        try:
            if size_text == "All":
                self.page_size = len(self.filtered_transactions) if self.filtered_transactions else 1000
            else:
                self.page_size = int(size_text)

            self.current_page = 1  # Reset to first page
            self.update_pagination_info()
            self.populate_table()
        except Exception as e:
            self.logger.error(f"Error changing page size: {e}")

    def go_to_first_page(self):
        """Go to first page"""
        self.current_page = 1
        self.update_pagination_info()
        self.populate_table()

    def go_to_previous_page(self):
        """Go to previous page"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_pagination_info()
            self.populate_table()

    def go_to_next_page(self):
        """Go to next page"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.update_pagination_info()
            self.populate_table()

    def go_to_last_page(self):
        """Go to last page"""
        self.current_page = self.total_pages
        self.update_pagination_info()
        self.populate_table()

    def update_pagination_info(self):
        """Update pagination controls and info"""
        if not self.filtered_transactions:
            self.total_pages = 1
            self.current_page = 1
        else:
            self.total_pages = max(1, (len(self.filtered_transactions) + self.page_size - 1) // self.page_size)
            self.current_page = min(self.current_page, self.total_pages)

        # Update page info label
        if self.page_info_label:
            total_items = len(self.filtered_transactions) if self.filtered_transactions else 0
            start_item = (self.current_page - 1) * self.page_size + 1 if total_items > 0 else 0
            end_item = min(self.current_page * self.page_size, total_items)

            self.page_info_label.setText(f"Page {self.current_page} of {self.total_pages} ({start_item}-{end_item} of {total_items})")

        # Update button states
        if self.first_page_btn:
            self.first_page_btn.setEnabled(self.current_page > 1)
        if self.prev_page_btn:
            self.prev_page_btn.setEnabled(self.current_page > 1)
        if self.next_page_btn:
            self.next_page_btn.setEnabled(self.current_page < self.total_pages)
        if self.last_page_btn:
            self.last_page_btn.setEnabled(self.current_page < self.total_pages)

    def get_current_page_transactions(self):
        """Get transactions for the current page"""
        if not self.filtered_transactions:
            return []

        start_idx = (self.current_page - 1) * self.page_size
        end_idx = start_idx + self.page_size
        return self.filtered_transactions[start_idx:end_idx]

    def get_current_page_credits_transactions(self):
        """Get credits transactions for the current page"""
        if not self.credits_filtered_transactions:
            return []

        start_idx = (self.credits_current_page - 1) * self.credits_page_size
        end_idx = start_idx + self.credits_page_size
        return self.credits_filtered_transactions[start_idx:end_idx]

    def get_current_page_debits_transactions(self):
        """Get debits transactions for the current page"""
        if not self.debits_filtered_transactions:
            return []

        start_idx = (self.debits_current_page - 1) * self.debits_page_size
        end_idx = start_idx + self.debits_page_size
        return self.debits_filtered_transactions[start_idx:end_idx]

    def populate_credits_table(self):
        """Populate the credits table with paginated filtered data"""
        try:
            import time
            start_time = time.time()

            # Update pagination info first
            self.update_credits_pagination_info()

            # Get transactions for current page
            page_transactions = self.get_current_page_credits_transactions()
            page_count = len(page_transactions)
            total_filtered = len(self.credits_filtered_transactions)

            self.logger.info(f"Populating credits table with {page_count} transactions (page {self.credits_current_page} of {self.credits_total_pages}, {total_filtered} total)")

            # Disable sorting and updates during population for better performance
            self.credits_table.setSortingEnabled(False)
            self.credits_table.setUpdatesEnabled(False)

            try:
                self.credits_table.setRowCount(page_count)

                # Process all transactions in current page (should be fast now)
                for row in range(page_count):
                    txn = page_transactions[row]

                    # Description
                    self.credits_table.setItem(row, 0, QTableWidgetItem(txn.description[:100]))

                    # Frequency
                    self.credits_table.setItem(row, 1, QTableWidgetItem(str(txn.frequency)))

                    # Type (lazy load and cache the predominant type calculation)
                    if not hasattr(txn, '_cached_predominant_type'):
                        txn._cached_predominant_type = txn.get_predominant_transaction_type()
                    predominant_type = txn._cached_predominant_type
                    type_display = f"{predominant_type} ({txn.debit_frequency}D/{txn.credit_frequency}C)"
                    self.credits_table.setItem(row, 2, QTableWidgetItem(type_display))

                    # Amount Range (Indian Rupee) - lazy load and cache formatting
                    if not hasattr(txn, '_cached_amount_display'):
                        if hasattr(txn, 'amount_range') and txn.amount_range:
                            txn._cached_amount_display = f"₹{txn.amount_range[0]:.2f} - ₹{txn.amount_range[1]:.2f}"
                        else:
                            txn._cached_amount_display = "N/A"
                    self.credits_table.setItem(row, 3, QTableWidgetItem(txn._cached_amount_display))

                    # Category
                    category = getattr(txn, 'category', '') or ''
                    self.credits_table.setItem(row, 4, QTableWidgetItem(category))

                    # Sub-category
                    subcategory = getattr(txn, 'sub_category', '') or ''
                    self.credits_table.setItem(row, 5, QTableWidgetItem(subcategory))

                    # Status
                    is_labeled = getattr(txn, 'is_manually_labeled', False) or bool(category and category.strip())
                    status = "Labelled" if is_labeled else "Unlabelled"
                    status_item = QTableWidgetItem(status)
                    if is_labeled:
                        status_item.setBackground(QColor("#d4edda"))  # Light green for labelled
                    else:
                        status_item.setBackground(QColor("#f8d7da"))  # Light red for unlabelled
                    self.credits_table.setItem(row, 6, status_item)

            finally:
                # Re-enable updates and sorting
                self.credits_table.setUpdatesEnabled(True)
                self.credits_table.setSortingEnabled(True)

            elapsed_time = time.time() - start_time
            self.logger.info(f"Credits table populated successfully with {page_count} rows in {elapsed_time:.3f}s")

        except Exception as e:
            self.logger.error(f"Error populating credits table: {e}")

    def populate_debits_table(self):
        """Populate the debits table with paginated filtered data"""
        try:
            import time
            start_time = time.time()

            # Update pagination info first
            self.update_debits_pagination_info()

            # Get transactions for current page
            page_transactions = self.get_current_page_debits_transactions()
            page_count = len(page_transactions)
            total_filtered = len(self.debits_filtered_transactions)

            self.logger.info(f"Populating debits table with {page_count} transactions (page {self.debits_current_page} of {self.debits_total_pages}, {total_filtered} total)")

            # Disable sorting and updates during population for better performance
            self.debits_table.setSortingEnabled(False)
            self.debits_table.setUpdatesEnabled(False)

            try:
                self.debits_table.setRowCount(page_count)

                # Process all transactions in current page (should be fast now)
                for row in range(page_count):
                    txn = page_transactions[row]

                    # Description
                    self.debits_table.setItem(row, 0, QTableWidgetItem(txn.description[:100]))

                    # Frequency
                    self.debits_table.setItem(row, 1, QTableWidgetItem(str(txn.frequency)))

                    # Type (lazy load and cache the predominant type calculation)
                    if not hasattr(txn, '_cached_predominant_type'):
                        txn._cached_predominant_type = txn.get_predominant_transaction_type()
                    predominant_type = txn._cached_predominant_type
                    type_display = f"{predominant_type} ({txn.debit_frequency}D/{txn.credit_frequency}C)"
                    self.debits_table.setItem(row, 2, QTableWidgetItem(type_display))

                    # Amount Range (Indian Rupee) - lazy load and cache formatting
                    if not hasattr(txn, '_cached_amount_display'):
                        if hasattr(txn, 'amount_range') and txn.amount_range:
                            txn._cached_amount_display = f"₹{txn.amount_range[0]:.2f} - ₹{txn.amount_range[1]:.2f}"
                        else:
                            txn._cached_amount_display = "N/A"
                    self.debits_table.setItem(row, 3, QTableWidgetItem(txn._cached_amount_display))

                    # Category
                    category = getattr(txn, 'category', '') or ''
                    self.debits_table.setItem(row, 4, QTableWidgetItem(category))

                    # Sub-category
                    subcategory = getattr(txn, 'sub_category', '') or ''
                    self.debits_table.setItem(row, 5, QTableWidgetItem(subcategory))

                    # Status
                    is_labeled = getattr(txn, 'is_manually_labeled', False) or bool(category and category.strip())
                    status = "Labelled" if is_labeled else "Unlabelled"
                    status_item = QTableWidgetItem(status)
                    if is_labeled:
                        status_item.setBackground(QColor("#d4edda"))  # Light green for labelled
                    else:
                        status_item.setBackground(QColor("#f8d7da"))  # Light red for unlabelled
                    self.debits_table.setItem(row, 6, status_item)

            finally:
                # Re-enable updates and sorting
                self.debits_table.setUpdatesEnabled(True)
                self.debits_table.setSortingEnabled(True)

            elapsed_time = time.time() - start_time
            self.logger.info(f"Debits table populated successfully with {page_count} rows in {elapsed_time:.3f}s")

        except Exception as e:
            self.logger.error(f"Error populating debits table: {e}")

    def update_credits_pagination_info(self):
        """Update credits pagination controls and info"""
        if not self.credits_filtered_transactions:
            self.credits_total_pages = 1
            self.credits_current_page = 1
        else:
            self.credits_total_pages = max(1, (len(self.credits_filtered_transactions) + self.credits_page_size - 1) // self.credits_page_size)
            self.credits_current_page = min(self.credits_current_page, self.credits_total_pages)

        # Update page info label
        if hasattr(self, 'credits_page_info_label') and self.credits_page_info_label:
            total_items = len(self.credits_filtered_transactions) if self.credits_filtered_transactions else 0
            start_item = (self.credits_current_page - 1) * self.credits_page_size + 1 if total_items > 0 else 0
            end_item = min(self.credits_current_page * self.credits_page_size, total_items)

            self.credits_page_info_label.setText(f"Page {self.credits_current_page} of {self.credits_total_pages} ({start_item}-{end_item} of {total_items})")

        # Update button states
        if hasattr(self, 'credits_first_page_btn') and self.credits_first_page_btn:
            self.credits_first_page_btn.setEnabled(self.credits_current_page > 1)
        if hasattr(self, 'credits_prev_page_btn') and self.credits_prev_page_btn:
            self.credits_prev_page_btn.setEnabled(self.credits_current_page > 1)
        if hasattr(self, 'credits_next_page_btn') and self.credits_next_page_btn:
            self.credits_next_page_btn.setEnabled(self.credits_current_page < self.credits_total_pages)
        if hasattr(self, 'credits_last_page_btn') and self.credits_last_page_btn:
            self.credits_last_page_btn.setEnabled(self.credits_current_page < self.credits_total_pages)

    def update_debits_pagination_info(self):
        """Update debits pagination controls and info"""
        if not self.debits_filtered_transactions:
            self.debits_total_pages = 1
            self.debits_current_page = 1
        else:
            self.debits_total_pages = max(1, (len(self.debits_filtered_transactions) + self.debits_page_size - 1) // self.debits_page_size)
            self.debits_current_page = min(self.debits_current_page, self.debits_total_pages)

        # Update page info label
        if hasattr(self, 'debits_page_info_label') and self.debits_page_info_label:
            total_items = len(self.debits_filtered_transactions) if self.debits_filtered_transactions else 0
            start_item = (self.debits_current_page - 1) * self.debits_page_size + 1 if total_items > 0 else 0
            end_item = min(self.debits_current_page * self.debits_page_size, total_items)

            self.debits_page_info_label.setText(f"Page {self.debits_current_page} of {self.debits_total_pages} ({start_item}-{end_item} of {total_items})")

        # Update button states
        if hasattr(self, 'debits_first_page_btn') and self.debits_first_page_btn:
            self.debits_first_page_btn.setEnabled(self.debits_current_page > 1)
        if hasattr(self, 'debits_prev_page_btn') and self.debits_prev_page_btn:
            self.debits_prev_page_btn.setEnabled(self.debits_current_page > 1)
        if hasattr(self, 'debits_next_page_btn') and self.debits_next_page_btn:
            self.debits_next_page_btn.setEnabled(self.debits_current_page < self.debits_total_pages)
        if hasattr(self, 'debits_last_page_btn') and self.debits_last_page_btn:
            self.debits_last_page_btn.setEnabled(self.debits_current_page < self.debits_total_pages)

    # Credits pagination handlers
    def on_credits_page_size_changed(self, size_text):
        """Handle credits page size change"""
        try:
            if size_text == "All":
                self.credits_page_size = len(self.credits_filtered_transactions) if self.credits_filtered_transactions else 1000
            else:
                self.credits_page_size = int(size_text)

            self.credits_current_page = 1  # Reset to first page
            self.update_credits_pagination_info()
            self.populate_credits_table()
        except Exception as e:
            self.logger.error(f"Error changing credits page size: {e}")

    def go_to_credits_first_page(self):
        """Go to first page of credits"""
        self.credits_current_page = 1
        self.update_credits_pagination_info()
        self.populate_credits_table()

    def go_to_credits_previous_page(self):
        """Go to previous page of credits"""
        if self.credits_current_page > 1:
            self.credits_current_page -= 1
            self.update_credits_pagination_info()
            self.populate_credits_table()

    def go_to_credits_next_page(self):
        """Go to next page of credits"""
        if self.credits_current_page < self.credits_total_pages:
            self.credits_current_page += 1
            self.update_credits_pagination_info()
            self.populate_credits_table()

    def go_to_credits_last_page(self):
        """Go to last page of credits"""
        self.credits_current_page = self.credits_total_pages
        self.update_credits_pagination_info()
        self.populate_credits_table()

    # Debits pagination handlers
    def on_debits_page_size_changed(self, size_text):
        """Handle debits page size change"""
        try:
            if size_text == "All":
                self.debits_page_size = len(self.debits_filtered_transactions) if self.debits_filtered_transactions else 1000
            else:
                self.debits_page_size = int(size_text)

            self.debits_current_page = 1  # Reset to first page
            self.update_debits_pagination_info()
            self.populate_debits_table()
        except Exception as e:
            self.logger.error(f"Error changing debits page size: {e}")

    def go_to_debits_first_page(self):
        """Go to first page of debits"""
        self.debits_current_page = 1
        self.update_debits_pagination_info()
        self.populate_debits_table()

    def go_to_debits_previous_page(self):
        """Go to previous page of debits"""
        if self.debits_current_page > 1:
            self.debits_current_page -= 1
            self.update_debits_pagination_info()
            self.populate_debits_table()

    def go_to_debits_next_page(self):
        """Go to next page of debits"""
        if self.debits_current_page < self.debits_total_pages:
            self.debits_current_page += 1
            self.update_debits_pagination_info()
            self.populate_debits_table()

    def go_to_debits_last_page(self):
        """Go to last page of debits"""
        self.debits_current_page = self.debits_total_pages
        self.update_debits_pagination_info()
        self.populate_debits_table()

    # Transaction selection handlers for tabs
    def on_credits_transaction_selected(self):
        """Handle transaction selection in the credits table"""
        try:
            current_row = self.credits_table.currentRow()
            page_transactions = self.get_current_page_credits_transactions()
            if 0 <= current_row < len(page_transactions):
                transaction = page_transactions[current_row]
                self.show_transaction_details(transaction)
        except Exception as e:
            self.logger.error(f"Error handling credits transaction selection: {e}")

    def on_debits_transaction_selected(self):
        """Handle transaction selection in the debits table"""
        try:
            current_row = self.debits_table.currentRow()
            page_transactions = self.get_current_page_debits_transactions()
            if 0 <= current_row < len(page_transactions):
                transaction = page_transactions[current_row]
                self.show_transaction_details(transaction)
        except Exception as e:
            self.logger.error(f"Error handling debits transaction selection: {e}")

    def show_transaction_details(self, transaction):
        """Show transaction details in the UI"""
        try:
            # Update description
            self.description_text.setPlainText(transaction.description)

            # Update category combos
            current_category = getattr(transaction, 'category', '') or ''
            current_subcategory = getattr(transaction, 'sub_category', '') or ''

            self.category_combo.setCurrentText(current_category)
            self.subcategory_combo.setCurrentText(current_subcategory)

        except Exception as e:
            self.logger.error(f"Error showing transaction details: {str(e)}")
    
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("ML Labeling - Simplified (No Sessions)")
        self.setGeometry(100, 100, 1400, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Compact stats display
        self.stats_label = QLabel("No data loaded - Click '🔄 Refresh Data' or use Data → Load Latest Data")
        self.stats_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 4px; background-color: #ecf0f1; border-radius: 3px; font-size: 11px;")
        self.stats_label.setMaximumHeight(25)
        main_layout.addWidget(self.stats_label)

        # Main splitter for three panels: filters, table, details
        main_splitter = QSplitter(Qt.Horizontal)

        # Left panel: Comprehensive filters
        filter_scroll = QScrollArea()
        filter_scroll.setWidgetResizable(True)
        filter_scroll.setMaximumWidth(300)
        filter_scroll.setMinimumWidth(250)

        self.filter_panel = ComprehensiveFilterPanel()
        self.filter_panel.apply_btn.clicked.connect(self.apply_comprehensive_filters)
        self.filter_panel.reset_btn.clicked.connect(self.reset_filters)

        # Connect filter controls to debounced updates
        self.setup_filter_debouncing()

        filter_scroll.setWidget(self.filter_panel)

        main_splitter.addWidget(filter_scroll)

        # Middle panel: Transaction table with tabs
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)

        # Quick controls above table
        quick_controls = QHBoxLayout()

        refresh_btn = QPushButton("🔄 Refresh Data")
        refresh_btn.clicked.connect(self.load_latest_data)
        quick_controls.addWidget(refresh_btn)

        ai_suggest_btn = QPushButton("🤖 AI Suggestions")
        ai_suggest_btn.clicked.connect(self.get_ai_suggestions)
        quick_controls.addWidget(ai_suggest_btn)

        batch_label_btn = QPushButton("📝 Batch Label")
        batch_label_btn.clicked.connect(self.open_batch_labeling)
        quick_controls.addWidget(batch_label_btn)

        quick_controls.addStretch()
        table_layout.addLayout(quick_controls)

        # Transaction view tabs
        self.transaction_tabs = QTabWidget()

        # All transactions tab
        all_tab = QWidget()
        all_layout = QVBoxLayout(all_tab)

        # Credits only tab
        credits_tab = QWidget()
        credits_layout = QVBoxLayout(credits_tab)

        # Debits only tab
        debits_tab = QWidget()
        debits_layout = QVBoxLayout(debits_tab)

        # Add tabs
        self.transaction_tabs.addTab(all_tab, "📊 All Transactions")
        self.transaction_tabs.addTab(credits_tab, "💰 Credits Only")
        self.transaction_tabs.addTab(debits_tab, "💸 Debits Only")

        # Connect tab change event
        self.transaction_tabs.currentChanged.connect(self.on_tab_changed)
        
        # Transaction table (will be placed in All Transactions tab)
        self.transaction_table = QTableWidget()
        self.transaction_table.setColumnCount(7)
        self.transaction_table.setHorizontalHeaderLabels([
            "Description", "Frequency", "Type", "Amount Range", "Category", "Sub-category", "Status"
        ])

        # Make table sortable and selectable
        self.transaction_table.setSortingEnabled(True)
        self.transaction_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.transaction_table.itemSelectionChanged.connect(self.on_transaction_selected)

        # Set custom resizable header
        resizable_header = ResizableHeaderView(Qt.Horizontal, self.transaction_table)
        self.transaction_table.setHorizontalHeader(resizable_header)

        # Set default column resize modes - Interactive allows drag-to-resize
        resizable_header.setSectionResizeMode(0, QHeaderView.Interactive)  # Description - draggable
        resizable_header.setSectionResizeMode(1, QHeaderView.Interactive)  # Frequency - draggable
        resizable_header.setSectionResizeMode(2, QHeaderView.Interactive)  # Type - draggable
        resizable_header.setSectionResizeMode(3, QHeaderView.Interactive)  # Amount Range - draggable
        resizable_header.setSectionResizeMode(4, QHeaderView.Interactive)  # Category - draggable
        resizable_header.setSectionResizeMode(5, QHeaderView.Interactive)  # Sub-category - draggable
        resizable_header.setSectionResizeMode(6, QHeaderView.Interactive)  # Status - draggable

        # Set initial column widths
        self.transaction_table.setColumnWidth(0, 300)  # Description
        self.transaction_table.setColumnWidth(1, 80)   # Frequency
        self.transaction_table.setColumnWidth(2, 100)  # Type
        self.transaction_table.setColumnWidth(3, 120)  # Amount Range
        self.transaction_table.setColumnWidth(4, 120)  # Category
        self.transaction_table.setColumnWidth(5, 120)  # Sub-category
        self.transaction_table.setColumnWidth(6, 100)  # Status

        # Add table to All Transactions tab
        all_layout.addWidget(self.transaction_table)

        # Pagination controls
        pagination_layout = QHBoxLayout()

        # Page size selector
        pagination_layout.addWidget(QLabel("Rows per page:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["25", "50", "100", "200", "All"])
        self.page_size_combo.setCurrentText("50")
        self.page_size_combo.currentTextChanged.connect(self.on_page_size_changed)
        pagination_layout.addWidget(self.page_size_combo)

        pagination_layout.addStretch()

        # Page navigation
        self.first_page_btn = QPushButton("⏮️ First")
        self.first_page_btn.clicked.connect(self.go_to_first_page)
        pagination_layout.addWidget(self.first_page_btn)

        self.prev_page_btn = QPushButton("⏪ Previous")
        self.prev_page_btn.clicked.connect(self.go_to_previous_page)
        pagination_layout.addWidget(self.prev_page_btn)

        self.page_info_label = QLabel("Page 1 of 1")
        self.page_info_label.setStyleSheet("font-weight: bold; padding: 0 10px;")
        pagination_layout.addWidget(self.page_info_label)

        self.next_page_btn = QPushButton("Next ⏩")
        self.next_page_btn.clicked.connect(self.go_to_next_page)
        pagination_layout.addWidget(self.next_page_btn)

        self.last_page_btn = QPushButton("Last ⏭️")
        self.last_page_btn.clicked.connect(self.go_to_last_page)
        pagination_layout.addWidget(self.last_page_btn)

        # Add pagination to All Transactions tab
        all_layout.addLayout(pagination_layout)

        # Create separate tables for Credits and Debits tabs
        self.create_credits_tab_content(credits_layout)
        self.create_debits_tab_content(debits_layout)

        # Add tabs to main layout
        table_layout.addWidget(self.transaction_tabs)
        main_splitter.addWidget(table_widget)

        # Right panel: Transaction details and labeling
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)

        details_layout.addWidget(QLabel("Transaction Details:"))

        self.description_text = QTextEdit()
        self.description_text.setMaximumHeight(100)
        self.description_text.setReadOnly(True)
        details_layout.addWidget(self.description_text)

        # Labeling controls
        details_layout.addWidget(QLabel("Category:"))
        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        self.category_combo.currentTextChanged.connect(self.on_details_category_changed)
        details_layout.addWidget(self.category_combo)

        details_layout.addWidget(QLabel("Sub-category:"))
        self.subcategory_combo = QComboBox()
        self.subcategory_combo.setEditable(True)
        details_layout.addWidget(self.subcategory_combo)

        # Action buttons
        action_layout = QVBoxLayout()

        save_btn = QPushButton("💾 Save Label")
        save_btn.clicked.connect(self.save_current_label)
        action_layout.addWidget(save_btn)

        create_category_btn = QPushButton("➕ Create Category")
        create_category_btn.clicked.connect(self.create_new_category)
        action_layout.addWidget(create_category_btn)

        details_layout.addLayout(action_layout)
        details_layout.addStretch()

        main_splitter.addWidget(details_widget)
        main_splitter.setSizes([300, 700, 300])

        main_layout.addWidget(main_splitter)
        
        # Status bar
        self.statusBar().showMessage("Ready - Load data to begin labeling")
    
    def setup_menus(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # Data menu
        data_menu = menubar.addMenu("📊 Data")
        
        load_action = QAction("📥 Load Latest Data", self)
        load_action.triggered.connect(self.load_latest_data)
        load_action.setToolTip("Load data from the most recent processed session")
        data_menu.addAction(load_action)

        import_csv_action = QAction("📂 Import CSV Data", self)
        import_csv_action.triggered.connect(self.import_csv_data)
        import_csv_action.setToolTip("Import transaction data from CSV file")
        data_menu.addAction(import_csv_action)

        data_menu.addSeparator()
        
        export_action = QAction("📤 Export Labels", self)
        export_action.triggered.connect(self.export_labels)
        export_action.setToolTip("Export labeled data")
        data_menu.addAction(export_action)
        
        # View menu
        view_menu = menubar.addMenu("👁️ View")
        
        refresh_action = QAction("🔄 Refresh", self)
        refresh_action.triggered.connect(self.refresh_view)
        view_menu.addAction(refresh_action)
        
        # Help menu
        help_menu = menubar.addMenu("❓ Help")
        
        about_action = QAction("ℹ️ About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def auto_load_latest_data(self):
        """Automatically load the latest data on startup"""
        try:
            session_info = self.data_access.get_session_info()
            if session_info["session_count"] > 0:
                self.load_latest_data()
            else:
                self.statusBar().showMessage("No processed data found - process bank statements first")
        except Exception as e:
            self.logger.error(f"Error auto-loading data: {str(e)}")
    
    def load_latest_data(self):
        """Load data from the most recent processed session using background loading"""
        try:
            # Check if session exists first
            session_info = self.data_access.get_session_info()

            if session_info["session_count"] == 0:
                QMessageBox.information(
                    self, "No Data Found",
                    "No processed sessions found. Please process some bank statements first."
                )
                return

            # Stop any existing loading worker
            if self.data_loading_worker and self.data_loading_worker.isRunning():
                self.logger.info("Stopping existing data loading worker...")
                self.data_loading_worker.stop()
                self.data_loading_worker.wait(1000)

            # Create and show loading screen
            self.loading_screen = DataLoadingScreen(self)
            self.loading_screen.cancel_requested.connect(self.on_loading_cancelled)

            # Create and start background loading worker
            self.data_loading_worker = DataLoadingWorker(self.data_access)
            self.data_loading_worker.loading_progress.connect(self.on_loading_progress)
            self.data_loading_worker.loading_completed.connect(self.on_loading_completed)
            self.data_loading_worker.loading_error.connect(self.on_loading_error)

            # Add timeout timer to prevent infinite loading
            self.loading_timeout_timer = QTimer()
            self.loading_timeout_timer.setSingleShot(True)
            self.loading_timeout_timer.timeout.connect(self.on_loading_timeout)
            self.loading_timeout_timer.start(30000)  # 30 second timeout

            # Show loading screen and start worker
            self.logger.info("Starting data loading with background worker...")
            self.loading_screen.show_loading()
            self.data_loading_worker.start()

        except Exception as e:
            self.logger.error(f"Error starting data loading: {str(e)}")
            QMessageBox.critical(self, "Load Error", f"Failed to start data loading:\n{str(e)}")

    def on_loading_progress(self, stage, progress, message, count, total):
        """Handle loading progress updates"""
        if self.loading_screen:
            self.loading_screen.update_data_progress(stage, progress, message, count, total)

    def on_loading_completed(self, raw_transactions, unique_transactions):
        """Handle completion of background data loading"""
        try:
            # Stop timeout timer
            if hasattr(self, 'loading_timeout_timer'):
                self.loading_timeout_timer.stop()

            # Store the loaded data (conversion already done in background thread)
            self.raw_transactions = raw_transactions
            self.unique_transactions = unique_transactions

            # Initialize filtered transactions to show all data initially
            self.filtered_transactions = self.unique_transactions.copy()

            # Initialize filters and categories
            self.refresh_category_lists()

            # Update UI first, then apply filters
            self.populate_table()
            self.update_stats()

            # Apply default filters (this will refresh the display)
            self.apply_comprehensive_filters()

            # Calculate totals for display
            total_debit_freq = sum(txn.debit_frequency for txn in self.unique_transactions)
            total_credit_freq = sum(txn.credit_frequency for txn in self.unique_transactions)

            self.statusBar().showMessage(
                f"Loaded {len(self.unique_transactions)} transactions ({total_debit_freq}D/{total_credit_freq}C)",
                5000
            )

            self.logger.info(f"Data loading completed successfully: {len(self.unique_transactions)} unique transactions with {total_debit_freq} debit freq, {total_credit_freq} credit freq")

            # Hide loading screen
            if self.loading_screen:
                self.loading_screen.hide_loading()
                self.loading_screen = None

        except Exception as e:
            self.logger.error(f"Error handling loading completion: {str(e)}")
            self.on_loading_error(str(e))

    def on_loading_error(self, error_message):
        """Handle loading errors"""
        self.logger.error(f"Data loading error: {error_message}")

        # Stop timeout timer
        if hasattr(self, 'loading_timeout_timer'):
            self.loading_timeout_timer.stop()

        # Hide loading screen
        if self.loading_screen:
            self.loading_screen.hide_loading()
            self.loading_screen = None

        # Show error message
        QMessageBox.critical(self, "Loading Error", f"Failed to load data:\n{error_message}")
        self.statusBar().showMessage("Data loading failed", 5000)

    def on_loading_cancelled(self):
        """Handle loading cancellation"""
        # Stop timeout timer
        if hasattr(self, 'loading_timeout_timer'):
            self.loading_timeout_timer.stop()

        if self.data_loading_worker and self.data_loading_worker.isRunning():
            self.data_loading_worker.stop()
            self.data_loading_worker.wait(2000)

        if self.loading_screen:
            self.loading_screen.hide_loading()
            self.loading_screen = None

        self.statusBar().showMessage("Data loading cancelled", 3000)

    def on_loading_timeout(self):
        """Handle loading timeout"""
        self.logger.warning("Data loading timed out after 30 seconds")

        # Stop the worker
        if self.data_loading_worker and self.data_loading_worker.isRunning():
            self.data_loading_worker.stop()
            self.data_loading_worker.wait(2000)

        # Hide loading screen
        if self.loading_screen:
            self.loading_screen.hide_loading()
            self.loading_screen = None

        # Show timeout message with fallback option
        reply = QMessageBox.question(
            self, "Loading Timeout",
            "Data loading timed out after 30 seconds.\n\n"
            "This might indicate:\n"
            "• Large dataset requiring more time\n"
            "• System performance issues\n"
            "• Data corruption\n\n"
            "Would you like to try loading data synchronously (may cause temporary UI freeze)?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.load_data_synchronously_fallback()
        else:
            self.statusBar().showMessage("Data loading timed out", 5000)

    def load_data_synchronously_fallback(self):
        """Fallback method to load data synchronously if background loading fails"""
        try:
            self.logger.info("Attempting synchronous data loading as fallback...")
            self.statusBar().showMessage("Loading data (fallback mode)...", 0)

            # Get session info
            session_info = self.data_access.get_session_info()
            if session_info["session_count"] == 0:
                QMessageBox.information(
                    self, "No Data Found",
                    "No processed sessions found. Please process some bank statements first."
                )
                return

            # Load raw transactions
            raw_transactions = self.data_access.get_latest_raw_transactions()
            if not raw_transactions:
                QMessageBox.warning(self, "No Transactions", "No transactions found in the latest session.")
                return

            # Extract unique transactions
            from ..ml.data_preparation import TransactionDataPreparator
            preparator = TransactionDataPreparator()
            unique_dict = preparator.extract_unique_transactions(raw_transactions)
            unique_transactions = list(unique_dict.values())

            # Sort by frequency
            unique_transactions.sort(key=lambda x: x.frequency, reverse=True)

            # Store the data
            self.raw_transactions = raw_transactions
            self.unique_transactions = unique_transactions
            self.filtered_transactions = self.unique_transactions.copy()

            # Initialize filters and categories
            self.refresh_category_lists()

            # Update UI
            self.populate_table()
            self.update_stats()
            self.apply_comprehensive_filters()

            # Calculate totals for display
            total_debit_freq = sum(txn.debit_frequency for txn in self.unique_transactions)
            total_credit_freq = sum(txn.credit_frequency for txn in self.unique_transactions)

            self.statusBar().showMessage(
                f"Loaded {len(self.raw_transactions)} raw → {len(self.unique_transactions)} unique transactions ({total_debit_freq}D/{total_credit_freq}C)",
                5000
            )

            self.logger.info(f"Synchronous fallback loading completed: {len(self.unique_transactions)} unique transactions")

        except Exception as e:
            self.logger.error(f"Synchronous fallback loading failed: {str(e)}")
            QMessageBox.critical(self, "Loading Failed", f"Failed to load data even with fallback method:\n{str(e)}")
            self.statusBar().showMessage("Data loading failed", 5000)

    def import_csv_data(self):
        """Import transaction data from CSV file"""
        try:
            # Open file dialog to select CSV file
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Import Transaction CSV",
                "",
                "CSV Files (*.csv);;All Files (*)"
            )

            if not file_path:
                return  # User cancelled

            self.logger.info(f"Importing CSV data from: {file_path}")
            self.statusBar().showMessage("Importing CSV data...", 0)

            # Load CSV data using pandas
            import pandas as pd

            # Read CSV file
            df = pd.read_csv(file_path)
            self.logger.info(f"Read {len(df)} rows from CSV file")

            # Process UI events to keep window responsive
            from PySide6.QtWidgets import QApplication
            QApplication.processEvents()

            # Convert CSV to raw transactions
            raw_transactions = []
            for _, row in df.iterrows():
                # Create a simple transaction object from CSV row
                # Adjust these field names based on your CSV structure
                txn = type('Transaction', (), {})()
                txn.date = pd.to_datetime(row.get('date', row.get('Date', ''))).date() if 'date' in row or 'Date' in row else None
                txn.description = str(row.get('description', row.get('Description', row.get('details', ''))))
                txn.amount = float(row.get('amount', row.get('Amount', 0)))
                txn.transaction_type = str(row.get('type', row.get('Type', 'unknown')))
                raw_transactions.append(txn)

            self.raw_transactions = raw_transactions
            self.logger.info(f"Converted {len(raw_transactions)} CSV rows to transactions")

            # Process transactions to create unique transactions
            self.statusBar().showMessage("Processing unique transactions...", 0)
            QApplication.processEvents()  # Keep UI responsive

            # Extract unique transactions
            unique_dict = self.data_preparator.extract_unique_transactions(raw_transactions)
            self.unique_transactions = list(unique_dict.values())
            self.logger.info(f"Created {len(self.unique_transactions)} unique transactions")

            # Initialize filtered transactions to show all data initially
            self.filtered_transactions = self.unique_transactions.copy()

            # Initialize filters and categories
            self.refresh_category_lists()

            # Update UI first, then apply filters
            self.populate_table()
            self.update_stats()

            # Apply default filters (this will refresh the display)
            self.apply_comprehensive_filters()

            # Show success message
            self.statusBar().showMessage(
                f"Imported {len(self.raw_transactions)} raw → {len(self.unique_transactions)} unique transactions from CSV",
                5000
            )

            self.logger.info(f"CSV import completed successfully")

        except Exception as e:
            self.logger.error(f"Error importing CSV data: {str(e)}")
            QMessageBox.critical(self, "Import Error", f"Failed to import CSV data:\n{str(e)}")
            self.statusBar().showMessage("CSV import failed", 5000)

    def apply_comprehensive_filters(self):
        """Apply comprehensive filters to the transaction list using background processing"""
        try:
            # Stop any existing filter worker
            if self.filter_worker and self.filter_worker.isRunning():
                self.filter_worker.stop()
                self.filter_worker.wait(1000)  # Wait up to 1 second

            self.current_filter = self.filter_panel.get_filter_criteria()

            # Clear stats cache when filters change
            if hasattr(self, '_stats_cache'):
                self._stats_cache.clear()

            # Debug filter criteria
            self.logger.info(f"Applying filters - Type: {self.current_filter.transaction_type}, "
                           f"Category: {self.current_filter.category}, "
                           f"Start Date: {self.current_filter.start_date}, "
                           f"End Date: {self.current_filter.end_date}")

            # Show progress and disable UI during filtering
            self.statusBar().showMessage("Filtering transactions...")
            self.filter_panel.apply_btn.setEnabled(False)
            self.filter_panel.apply_btn.setText("🔄 Filtering...")

            # Start background filtering
            self.filter_worker = FilterWorker(self.unique_transactions, self.current_filter)
            self.filter_worker.filter_completed.connect(self.on_filter_completed)
            self.filter_worker.filter_progress.connect(self.on_filter_progress)
            self.filter_worker.filter_error.connect(self.on_filter_error)
            self.filter_worker.start()

        except Exception as e:
            self.logger.error(f"Error starting comprehensive filters: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            self.on_filter_error(str(e))

    def on_filter_completed(self, filtered_transactions):
        """Handle completion of background filtering"""
        try:
            self.filtered_transactions = filtered_transactions

            # Reset pagination to first page when filters change
            self.current_page = 1

            # Update tab-specific filtered transactions
            self.update_credits_filtered_transactions()
            self.update_debits_filtered_transactions()

            # Refresh the current tab
            self.refresh_current_tab()
            self.update_stats()

            # Force UI refresh
            self.transaction_table.clearSelection()
            if len(self.filtered_transactions) > 0:
                self.transaction_table.selectRow(0)

            # Re-enable UI
            self.filter_panel.apply_btn.setEnabled(True)
            self.filter_panel.apply_btn.setText("🔍 Apply Filters")

            self.statusBar().showMessage(
                f"Applied filters: {len(self.filtered_transactions)} of {len(self.unique_transactions)} transactions match",
                3000
            )

            self.logger.info(f"Applied filters: {len(self.filtered_transactions)} of {len(self.unique_transactions)} transactions match")

        except Exception as e:
            self.logger.error(f"Error handling filter completion: {str(e)}")
            self.on_filter_error(str(e))

    def on_filter_progress(self, progress):
        """Handle filter progress updates"""
        self.statusBar().showMessage(f"Filtering transactions... {progress}%")

    def on_filter_error(self, error_msg):
        """Handle filter errors"""
        self.logger.error(f"Filter error: {error_msg}")

        # Re-enable UI
        self.filter_panel.apply_btn.setEnabled(True)
        self.filter_panel.apply_btn.setText("🔍 Apply Filters")

        self.statusBar().showMessage(f"Filter error: {error_msg}", 5000)
        QMessageBox.warning(self, "Filter Error", f"An error occurred while filtering:\n{error_msg}")

    def setup_filter_debouncing(self):
        """Setup debounced filter updates for better performance"""
        try:
            # Connect filter controls to debounced updates (500ms delay)
            self.filter_panel.type_combo.currentTextChanged.connect(self.request_filter_update)
            self.filter_panel.category_combo.currentTextChanged.connect(self.request_filter_update)
            self.filter_panel.subcategory_combo.currentTextChanged.connect(self.request_filter_update)
            self.filter_panel.min_freq_spin.valueChanged.connect(self.request_filter_update)
            self.filter_panel.max_freq_spin.valueChanged.connect(self.request_filter_update)
            self.filter_panel.min_amount_spin.valueChanged.connect(self.request_filter_update)
            self.filter_panel.max_amount_spin.valueChanged.connect(self.request_filter_update)
            self.filter_panel.start_date_enabled.toggled.connect(self.request_filter_update)
            self.filter_panel.end_date_enabled.toggled.connect(self.request_filter_update)
            self.filter_panel.start_date_edit.dateChanged.connect(self.request_filter_update)
            self.filter_panel.end_date_edit.dateChanged.connect(self.request_filter_update)
            self.filter_panel.status_combo.currentTextChanged.connect(self.request_filter_update)

        except Exception as e:
            self.logger.error(f"Error setting up filter debouncing: {str(e)}")

    def request_filter_update(self):
        """Request a debounced filter update"""
        self.filter_debounce_timer.start(500)  # 500ms delay

    def _apply_filters_debounced(self):
        """Apply filters after debounce delay"""
        self.apply_comprehensive_filters()

    def reset_filters(self):
        """Reset all filters to default values"""
        try:
            self.filter_panel.reset_filters()
            self.apply_comprehensive_filters()
        except Exception as e:
            self.logger.error(f"Error resetting filters: {str(e)}")

    def closeEvent(self, event):
        """Handle window close event"""
        try:
            # Stop any running background workers
            if self.filter_worker and self.filter_worker.isRunning():
                self.filter_worker.stop()
                self.filter_worker.wait(2000)  # Wait up to 2 seconds

            if self.data_loading_worker and self.data_loading_worker.isRunning():
                self.data_loading_worker.stop()
                self.data_loading_worker.wait(2000)  # Wait up to 2 seconds

            # Stop timeout timer
            if hasattr(self, 'loading_timeout_timer'):
                self.loading_timeout_timer.stop()

            # Hide loading screen if visible
            if self.loading_screen:
                self.loading_screen.hide_loading()
                self.loading_screen = None

            # Stop debounce timer
            if hasattr(self, 'filter_debounce_timer'):
                self.filter_debounce_timer.stop()

            # Call parent close event
            super().closeEvent(event)

        except Exception as e:
            self.logger.error(f"Error during window close: {str(e)}")
            super().closeEvent(event)

    def get_ai_suggestions(self):
        """Get AI suggestions for selected transactions"""
        try:
            selected_rows = self.get_selected_table_rows()
            if not selected_rows:
                QMessageBox.information(self, "No Selection", "Please select transactions to get AI suggestions.")
                return

            selected_transactions = [self.filtered_transactions[row] for row in selected_rows]

            # Convert UniqueTransaction objects to RawTransaction objects for AI processing
            raw_transactions = []
            for txn in selected_transactions:
                try:
                    # Create a RawTransaction from UniqueTransaction
                    from datetime import date
                    from decimal import Decimal
                    from ..models.transaction import RawTransaction

                    # Use the first seen date or today if not available
                    txn_date = txn.first_seen if txn.first_seen else date.today()

                    # Use the average amount from the amount range
                    if txn.amount_range and len(txn.amount_range) == 2:
                        avg_amount = (txn.amount_range[0] + txn.amount_range[1]) / 2
                    else:
                        avg_amount = 0.0

                    raw_txn = RawTransaction(
                        date=txn_date,
                        description=txn.description,
                        amount=Decimal(str(avg_amount)),
                        transaction_type="DEBIT" if txn.get_predominant_transaction_type() == "debit" else "CREDIT"
                    )
                    raw_transactions.append(raw_txn)
                except Exception as e:
                    self.logger.warning(f"Failed to convert transaction {txn.description[:50]}: {str(e)}")

            if not raw_transactions:
                QMessageBox.information(self, "No Valid Transactions", "No valid transactions could be processed for AI suggestions.")
                return

            # Use hybrid categorizer to get suggestions for manual labeling
            suggestions = self.hybrid_categorizer.get_ai_suggestions_for_manual_labeling(raw_transactions)

            if suggestions:
                # Convert suggestions format to match expected format (using hash_id as key)
                formatted_suggestions = {}
                for i, txn in enumerate(selected_transactions):
                    if i < len(raw_transactions):
                        raw_txn = raw_transactions[i]
                        if raw_txn.description in suggestions:
                            suggestion = suggestions[raw_txn.description]
                            formatted_suggestions[txn.hash_id] = {
                                'category': suggestion.get('category', ''),
                                'sub_category': suggestion.get('subcategory', ''),
                                'confidence': suggestion.get('confidence', 0.0),
                                'source': suggestion.get('source', 'ai'),
                                'notes': suggestion.get('notes', ''),
                                'recommendation': suggestion.get('recommendation', '')
                            }

                if formatted_suggestions:
                    self.show_ai_suggestions_dialog(formatted_suggestions, selected_transactions)
                else:
                    QMessageBox.information(self, "No Suggestions", "No AI suggestions could be generated for the selected transactions.")
            else:
                QMessageBox.information(self, "No Suggestions", "No AI suggestions could be generated for the selected transactions.")

        except Exception as e:
            self.logger.error(f"Error getting AI suggestions: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to get AI suggestions:\n{str(e)}")

    def open_batch_labeling(self):
        """Open batch labeling dialog"""
        try:
            selected_rows = self.get_selected_table_rows()
            if not selected_rows:
                QMessageBox.information(self, "No Selection", "Please select transactions for batch labeling.")
                return

            selected_transactions = [self.filtered_transactions[row] for row in selected_rows]
            self.show_batch_labeling_dialog(selected_transactions)

        except Exception as e:
            self.logger.error(f"Error opening batch labeling: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open batch labeling:\n{str(e)}")

    def create_new_category(self):
        """Create a new category"""
        try:
            self.show_category_creation_dialog()
        except Exception as e:
            self.logger.error(f"Error creating new category: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to create new category:\n{str(e)}")
    
    def populate_table(self):
        """Populate the transaction table with paginated filtered data"""
        try:
            import time
            start_time = time.time()

            # Update pagination info first
            self.update_pagination_info()

            # Get transactions for current page
            page_transactions = self.get_current_page_transactions()
            page_count = len(page_transactions)
            total_filtered = len(self.filtered_transactions)

            self.logger.info(f"Populating table with {page_count} transactions (page {self.current_page} of {self.total_pages}, {total_filtered} total)")

            # Disable sorting and updates during population for better performance
            self.transaction_table.setSortingEnabled(False)
            self.transaction_table.setUpdatesEnabled(False)

            try:
                self.transaction_table.setRowCount(page_count)

                # Process all transactions in current page (should be fast now)
                for row in range(page_count):
                    txn = page_transactions[row]

                    # Description
                    self.transaction_table.setItem(row, 0, QTableWidgetItem(txn.description[:100]))

                    # Frequency
                    self.transaction_table.setItem(row, 1, QTableWidgetItem(str(txn.frequency)))

                    # Type (lazy load and cache the predominant type calculation)
                    if not hasattr(txn, '_cached_predominant_type'):
                        txn._cached_predominant_type = txn.get_predominant_transaction_type()
                    predominant_type = txn._cached_predominant_type
                    type_display = f"{predominant_type} ({txn.debit_frequency}D/{txn.credit_frequency}C)"
                    self.transaction_table.setItem(row, 2, QTableWidgetItem(type_display))

                    # Amount Range (Indian Rupee) - lazy load and cache formatting
                    if not hasattr(txn, '_cached_amount_display'):
                        if hasattr(txn, 'amount_range') and txn.amount_range:
                            txn._cached_amount_display = f"₹{txn.amount_range[0]:.2f} - ₹{txn.amount_range[1]:.2f}"
                        else:
                            txn._cached_amount_display = "N/A"
                    self.transaction_table.setItem(row, 3, QTableWidgetItem(txn._cached_amount_display))

                    # Category
                    category = getattr(txn, 'category', '') or ''
                    self.transaction_table.setItem(row, 4, QTableWidgetItem(category))

                    # Sub-category
                    subcategory = getattr(txn, 'sub_category', '') or ''
                    self.transaction_table.setItem(row, 5, QTableWidgetItem(subcategory))

                    # Status
                    is_labeled = getattr(txn, 'is_manually_labeled', False) or bool(category and category.strip())
                    status = "Labelled" if is_labeled else "Unlabelled"
                    status_item = QTableWidgetItem(status)
                    if is_labeled:
                        status_item.setBackground(QColor("#d4edda"))  # Light green for labelled
                    else:
                        status_item.setBackground(QColor("#f8d7da"))  # Light red for unlabelled
                    self.transaction_table.setItem(row, 6, status_item)

            finally:
                # Re-enable updates and sorting
                self.transaction_table.setUpdatesEnabled(True)
                self.transaction_table.setSortingEnabled(True)

            elapsed_time = time.time() - start_time
            self.logger.info(f"Table populated successfully with {page_count} rows in {elapsed_time:.3f}s")

        except Exception as e:
            self.logger.error(f"Error populating table: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    def on_transaction_selected(self):
        """Handle transaction selection in the table"""
        try:
            current_row = self.transaction_table.currentRow()
            page_transactions = self.get_current_page_transactions()
            if 0 <= current_row < len(page_transactions):
                transaction = page_transactions[current_row]

                # Update description text
                self.description_text.setPlainText(transaction.description)

                # Update category combos
                current_category = getattr(transaction, 'category', '') or ''
                current_subcategory = getattr(transaction, 'sub_category', '') or ''

                # Set category
                category_index = self.category_combo.findText(current_category)
                if category_index >= 0:
                    self.category_combo.setCurrentIndex(category_index)
                else:
                    self.category_combo.setCurrentText(current_category)

                # Set sub-category
                subcategory_index = self.subcategory_combo.findText(current_subcategory)
                if subcategory_index >= 0:
                    self.subcategory_combo.setCurrentIndex(subcategory_index)
                else:
                    self.subcategory_combo.setCurrentText(current_subcategory)

        except Exception as e:
            self.logger.error(f"Error handling transaction selection: {str(e)}")


    
    def update_stats(self):
        """Update the statistics display with cached calculations"""
        try:
            # In stateless approach, we don't have raw transactions count
            # Show processed transactions count instead
            raw_count = len(self.unique_transactions)  # Show total processed as "raw"
            total_unique = len(self.unique_transactions)
            filtered_count = len(self.filtered_transactions)

            # Use cached statistics if available and valid
            cache_key = f"stats_{len(self.filtered_transactions)}_{hash(str(self.current_filter.__dict__))}"
            if hasattr(self, '_stats_cache') and cache_key in self._stats_cache:
                cached_stats = self._stats_cache[cache_key]
                debit_count = cached_stats['debit_count']
                credit_count = cached_stats['credit_count']
                mixed_count = cached_stats['mixed_count']
                labeled_count = cached_stats['labeled_count']
                total_debit_freq = cached_stats['total_debit_freq']
                total_credit_freq = cached_stats['total_credit_freq']
            else:
                # Calculate statistics and cache them
                debit_count = 0
                credit_count = 0
                labeled_count = 0
                total_debit_freq = 0
                total_credit_freq = 0

                for txn in self.filtered_transactions:
                    # Use cached predominant type if available
                    if not hasattr(txn, '_cached_predominant_type'):
                        txn._cached_predominant_type = txn.get_predominant_transaction_type()

                    if txn._cached_predominant_type == "debit":
                        debit_count += 1
                    elif txn._cached_predominant_type == "credit":
                        credit_count += 1

                    if getattr(txn, 'category', None):
                        labeled_count += 1

                    total_debit_freq += txn.debit_frequency
                    total_credit_freq += txn.credit_frequency

                mixed_count = filtered_count - debit_count - credit_count

                # Cache the results
                if not hasattr(self, '_stats_cache'):
                    self._stats_cache = {}
                self._stats_cache[cache_key] = {
                    'debit_count': debit_count,
                    'credit_count': credit_count,
                    'mixed_count': mixed_count,
                    'labeled_count': labeled_count,
                    'total_debit_freq': total_debit_freq,
                    'total_credit_freq': total_credit_freq
                }

                # Limit cache size to prevent memory issues
                if len(self._stats_cache) > 10:
                    # Remove oldest entries
                    oldest_keys = list(self._stats_cache.keys())[:-5]
                    for key in oldest_keys:
                        del self._stats_cache[key]

            # Get current tab info
            current_tab = self.transaction_tabs.currentIndex() if hasattr(self, 'transaction_tabs') else 0
            tab_names = ["All", "Credits", "Debits"]
            current_tab_name = tab_names[current_tab] if current_tab < len(tab_names) else "All"

            # Get tab-specific counts
            credits_filtered_count = len(self.credits_filtered_transactions) if hasattr(self, 'credits_filtered_transactions') else 0
            debits_filtered_count = len(self.debits_filtered_transactions) if hasattr(self, 'debits_filtered_transactions') else 0

            # Get current filter info
            filter_info = "All" if self.current_filter.transaction_type == "All" else self.current_filter.transaction_type

            if filter_info == "All":
                stats_text = (
                    f"Raw: {raw_count} | Unique: {total_unique} | Tab: {current_tab_name} | "
                    f"Showing: {filtered_count} (💰{credits_filtered_count} | 💸{debits_filtered_count}) | "
                    f"D: {debit_count}({total_debit_freq}) | C: {credit_count}({total_credit_freq}) | "
                    f"Mixed: {mixed_count} | Labeled: {labeled_count}"
                )
            else:
                stats_text = (
                    f"Raw: {raw_count} | Filter: {filter_info} | Tab: {current_tab_name} | "
                    f"Showing: {filtered_count}/{total_unique} (💰{credits_filtered_count} | 💸{debits_filtered_count}) | "
                    f"D: {debit_count}({total_debit_freq}) | C: {credit_count}({total_credit_freq}) | "
                    f"Mixed: {mixed_count} | Labeled: {labeled_count}"
                )

            self.stats_label.setText(stats_text)

        except Exception as e:
            self.logger.error(f"Error updating stats: {str(e)}")
    

    
    def save_current_label(self):
        """Save the current label for the selected transaction"""
        try:
            current_row = self.transaction_table.currentRow()

            # Get the correct transaction from the current page, not the entire filtered list
            page_transactions = self.get_current_page_transactions()
            if current_row < 0 or current_row >= len(page_transactions):
                QMessageBox.warning(self, "No Selection", "Please select a transaction to label.")
                return

            txn = page_transactions[current_row]
            category = self.category_combo.currentText().strip()
            subcategory = self.subcategory_combo.currentText().strip()

            if not category:
                QMessageBox.warning(self, "Missing Category", "Please enter a category.")
                return

            # Log which transaction is being labeled for debugging
            self.logger.info(f"Labeling transaction: '{txn.description[:50]}...' with category: {category} / {subcategory}")

            # Update transaction
            txn.category = category
            txn.sub_category = subcategory
            txn.is_manually_labeled = True

            # Update table display immediately
            self.transaction_table.setItem(current_row, 4, QTableWidgetItem(category))
            self.transaction_table.setItem(current_row, 5, QTableWidgetItem(subcategory))

            # Update Status column
            status_item = QTableWidgetItem("Labelled")
            status_item.setBackground(QColor("#d4edda"))  # Light green for labelled
            self.transaction_table.setItem(current_row, 6, status_item)

            # Save the updated transactions to persistent storage
            try:
                # Convert list back to dictionary for saving
                unique_transactions_dict = {txn.hash_id: txn for txn in self.unique_transactions}

                # Save to persistent storage
                if self.data_preparator.save_unique_transactions(unique_transactions_dict):
                    self.logger.info(f"Successfully saved transaction label: {category} / {subcategory}")
                else:
                    self.logger.warning(f"Failed to save transaction label to storage")

            except Exception as save_error:
                self.logger.error(f"Error saving transaction label: {save_error}")
                # Continue anyway - the UI update is more important than persistence

            # Refresh all tables to show updated status
            current_tab = self.transaction_tabs.currentIndex()
            if current_tab == 0:  # All Transactions
                # Already updated above
                pass
            elif current_tab == 1:  # Credits Only
                self.populate_credits_table()
            elif current_tab == 2:  # Debits Only
                self.populate_debits_table()

            # Update stats
            self.update_stats()

            # Move to next transaction in the current page
            if current_row + 1 < len(page_transactions):
                self.transaction_table.selectRow(current_row + 1)

            self.statusBar().showMessage(f"Saved label: {category} / {subcategory}", 2000)

        except Exception as e:
            self.logger.error(f"Error saving label: {str(e)}")
            QMessageBox.critical(self, "Save Error", f"Failed to save label:\n{str(e)}")
    
    def export_labels(self):
        """Export labeled data for ML training"""
        try:
            # Get manually labeled transactions
            labeled_transactions = [txn for txn in self.unique_transactions
                                  if getattr(txn, 'category', None) and getattr(txn, 'is_manually_labeled', False)]

            if not labeled_transactions:
                QMessageBox.information(self, "No Labels", "No manually labeled transactions to export.")
                return

            # Create export data
            from datetime import datetime
            import csv
            import os

            # Create export directory if it doesn't exist
            export_dir = Path("bank_analyzer_config/ml_training_data")
            export_dir.mkdir(parents=True, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"manual_labels_{timestamp}.csv"
            filepath = export_dir / filename

            # Export to CSV
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['hash_id', 'description', 'normalized_description', 'category', 'sub_category',
                             'amount', 'date', 'transaction_type', 'frequency', 'is_manually_labeled', 'labeled_at']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for txn in labeled_transactions:
                    writer.writerow({
                        'hash_id': getattr(txn, 'hash_id', ''),
                        'description': getattr(txn, 'description', ''),
                        'normalized_description': getattr(txn, 'normalized_description', ''),
                        'category': getattr(txn, 'category', ''),
                        'sub_category': getattr(txn, 'sub_category', ''),
                        'amount': getattr(txn, 'amount', 0),
                        'date': getattr(txn, 'date', ''),
                        'transaction_type': getattr(txn, 'transaction_type', ''),
                        'frequency': getattr(txn, 'frequency', 1),
                        'is_manually_labeled': True,
                        'labeled_at': datetime.now().isoformat()
                    })

            QMessageBox.information(
                self, "Export Successful",
                f"Exported {len(labeled_transactions)} manually labeled transactions to:\n{filepath}\n\n"
                "This data can be used for ML model training."
            )
            
        except Exception as e:
            self.logger.error(f"Error exporting labels: {str(e)}")
    
    def refresh_view(self):
        """Refresh the current view"""
        self.apply_comprehensive_filters()
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self, "About",
            "Enhanced ML Labeling Window\n\n"
            "Comprehensive filtering and AI-assisted categorization.\n"
            "Direct data access without sessions or caching.\n"
            "Loads data from the most recent processed session."
        )

    def get_selected_table_rows(self):
        """Get selected rows from the transaction table"""
        try:
            selected_items = self.transaction_table.selectedItems()
            if not selected_items:
                return []

            # Get unique row numbers
            selected_rows = list(set(item.row() for item in selected_items))
            return sorted(selected_rows)
        except Exception as e:
            self.logger.error(f"Error getting selected table rows: {str(e)}")
            return []

    def show_ai_suggestions_dialog(self, suggestions, transactions):
        """Show AI suggestions dialog"""
        try:
            dialog = AISuggestionsDialog(suggestions, transactions, self)
            if dialog.exec() == QDialog.Accepted:
                # Apply accepted suggestions
                accepted_suggestions = dialog.get_accepted_suggestions()
                self.apply_ai_suggestions(accepted_suggestions)
        except Exception as e:
            self.logger.error(f"Error showing AI suggestions dialog: {str(e)}")

    def show_batch_labeling_dialog(self, transactions):
        """Show batch labeling dialog"""
        try:
            dialog = BatchLabelingDialog(transactions, self)
            if dialog.exec() == QDialog.Accepted:
                # Apply batch labels
                labels = dialog.get_batch_labels()
                self.apply_batch_labels(labels)
        except Exception as e:
            self.logger.error(f"Error showing batch labeling dialog: {str(e)}")

    def show_category_creation_dialog(self):
        """Show category creation dialog"""
        try:
            dialog = CategoryCreationDialog(self.category_manager, self)
            if dialog.exec() == QDialog.Accepted:
                # Refresh category lists
                self.refresh_category_lists()
        except Exception as e:
            self.logger.error(f"Error showing category creation dialog: {str(e)}")

    def apply_ai_suggestions(self, suggestions):
        """Apply accepted AI suggestions"""
        try:
            for hash_id, suggestion in suggestions.items():
                # Find transaction and apply suggestion
                for txn in self.unique_transactions:
                    if txn.hash_id == hash_id:
                        txn.category = suggestion.get('category', '')
                        txn.sub_category = suggestion.get('sub_category', '')
                        txn.is_manually_labeled = True
                        break

            # Save the updated transactions to persistent storage
            try:
                unique_transactions_dict = {txn.hash_id: txn for txn in self.unique_transactions}
                if self.data_preparator.save_unique_transactions(unique_transactions_dict):
                    self.logger.info(f"Successfully saved {len(suggestions)} AI suggestions")
                else:
                    self.logger.warning(f"Failed to save AI suggestions to storage")
            except Exception as save_error:
                self.logger.error(f"Error saving AI suggestions: {save_error}")

            # Refresh all tables to show updated status
            self.populate_table()
            self.populate_credits_table()
            self.populate_debits_table()
            self.update_stats()

            self.statusBar().showMessage(f"Applied {len(suggestions)} AI suggestions", 3000)
        except Exception as e:
            self.logger.error(f"Error applying AI suggestions: {str(e)}")

    def apply_batch_labels(self, labels):
        """Apply batch labels to transactions"""
        try:
            for hash_id, label_info in labels.items():
                # Find transaction and apply label
                for txn in self.unique_transactions:
                    if txn.hash_id == hash_id:
                        txn.category = label_info.get('category', '')
                        txn.sub_category = label_info.get('sub_category', '')
                        txn.is_manually_labeled = True
                        break

            # Save the updated transactions to persistent storage
            try:
                unique_transactions_dict = {txn.hash_id: txn for txn in self.unique_transactions}
                if self.data_preparator.save_unique_transactions(unique_transactions_dict):
                    self.logger.info(f"Successfully saved {len(labels)} batch labels")
                else:
                    self.logger.warning(f"Failed to save batch labels to storage")
            except Exception as save_error:
                self.logger.error(f"Error saving batch labels: {save_error}")

            # Refresh all tables to show updated status
            self.populate_table()
            self.populate_credits_table()
            self.populate_debits_table()
            self.update_stats()

            self.statusBar().showMessage(f"Applied batch labels to {len(labels)} transactions", 3000)
        except Exception as e:
            self.logger.error(f"Error applying batch labels: {str(e)}")



    def refresh_category_lists(self):
        """Refresh category dropdown lists with transaction type filtering"""
        try:
            # Get current transaction type filter
            filter_type = self.get_current_transaction_type_filter()

            # Get categories from category manager
            all_categories = self.category_manager.get_main_categories()

            # Filter categories by transaction type if needed
            if filter_type:
                categories = [cat for cat in all_categories if getattr(cat, 'category_type', None) == filter_type]
            else:
                categories = all_categories

            # Update filter panel categories
            self.filter_panel.category_combo.clear()
            self.filter_panel.category_combo.addItem("All Categories")
            for category in categories:
                self.filter_panel.category_combo.addItem(category.name)

            # Update details panel categories
            self.category_combo.clear()
            for category in categories:
                self.category_combo.addItem(category.name)

            # Initialize sub-categories for filter panel
            self.update_subcategory_list("All Categories", self.filter_panel.subcategory_combo, filter_type)

            # Initialize sub-categories for details panel
            self.update_subcategory_list("", self.subcategory_combo, filter_type)

        except Exception as e:
            self.logger.error(f"Error refreshing category lists: {str(e)}")

    def on_details_category_changed(self, category_name):
        """Handle category selection change in details panel"""
        try:
            # Get current transaction type filter based on active tab
            filter_type = self.get_current_transaction_type_filter()
            self.update_subcategory_list(category_name, self.subcategory_combo, filter_type)
        except Exception as e:
            self.logger.error(f"Error updating details sub-categories: {e}")

    def update_subcategory_list(self, category_name, subcategory_combo, filter_by_type=None):
        """Update sub-category dropdown based on selected category"""
        try:
            # Clear current sub-categories
            subcategory_combo.clear()
            subcategory_combo.addItem("All Sub-categories")

            if category_name == "All Categories" or not category_name:
                return

            # Get sub-categories for the selected main category
            if hasattr(self, 'category_manager') and self.category_manager:
                all_categories = self.category_manager.get_all_categories()

                # Find the main category
                main_category = None
                for cat in all_categories:
                    if cat.name == category_name and cat.parent_id is None:
                        main_category = cat
                        break

                if main_category:
                    # Get sub-categories for this main category
                    sub_categories = [cat for cat in all_categories if cat.parent_id == main_category.id]

                    # Filter by category type if specified
                    if filter_by_type:
                        sub_categories = [cat for cat in sub_categories if getattr(cat, 'category_type', None) == filter_by_type]

                    sub_categories.sort(key=lambda x: x.name)

                    for sub_cat in sub_categories:
                        subcategory_combo.addItem(sub_cat.name)

        except Exception as e:
            self.logger.error(f"Error updating sub-category list: {e}")

    def get_current_transaction_type_filter(self):
        """Get the transaction type filter based on current tab"""
        try:
            if hasattr(self, 'transaction_tabs'):
                current_tab = self.transaction_tabs.currentIndex()
                if current_tab == 1:  # Credits Only tab
                    return "income"
                elif current_tab == 2:  # Debits Only tab
                    return "expense"
            return None  # All transactions tab or no filtering
        except Exception as e:
            self.logger.error(f"Error getting transaction type filter: {e}")
            return None


# Dialog classes for enhanced features
class AISuggestionsDialog(QDialog):
    """Dialog for reviewing and accepting AI suggestions"""

    def __init__(self, suggestions, transactions, parent=None):
        super().__init__(parent)
        self.suggestions = suggestions
        self.transactions = transactions
        self.accepted_suggestions = {}
        self.setup_ui()

    def setup_ui(self):
        """Setup the AI suggestions dialog UI"""
        self.setWindowTitle("🤖 AI Categorization Suggestions")
        self.setGeometry(200, 200, 800, 600)

        layout = QVBoxLayout(self)

        # Instructions
        instructions = QLabel("Review AI suggestions and select which ones to accept:")
        instructions.setStyleSheet("font-weight: bold; padding: 10px;")
        layout.addWidget(instructions)

        # Suggestions table
        self.suggestions_table = QTableWidget()
        self.suggestions_table.setColumnCount(5)
        self.suggestions_table.setHorizontalHeaderLabels([
            "Accept", "Transaction", "Suggested Category", "Suggested Sub-category", "Confidence"
        ])

        # Set custom resizable header
        resizable_header = ResizableHeaderView(Qt.Horizontal, self.suggestions_table)
        self.suggestions_table.setHorizontalHeader(resizable_header)

        # Set default column resize modes
        resizable_header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Accept
        resizable_header.setSectionResizeMode(1, QHeaderView.Stretch)  # Transaction
        resizable_header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Category
        resizable_header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Sub-category
        resizable_header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Confidence

        # Populate suggestions
        self.populate_suggestions()

        layout.addWidget(self.suggestions_table)

        # Buttons
        button_layout = QHBoxLayout()

        accept_all_btn = QPushButton("✅ Accept All")
        accept_all_btn.clicked.connect(self.accept_all_suggestions)
        button_layout.addWidget(accept_all_btn)

        reject_all_btn = QPushButton("❌ Reject All")
        reject_all_btn.clicked.connect(self.reject_all_suggestions)
        button_layout.addWidget(reject_all_btn)

        button_layout.addStretch()

        ok_btn = QPushButton("Apply Selected")
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

    def populate_suggestions(self):
        """Populate the suggestions table"""
        self.suggestions_table.setRowCount(len(self.suggestions))

        for row, (hash_id, suggestion) in enumerate(self.suggestions.items()):
            # Find corresponding transaction
            transaction = next((t for t in self.transactions if t.hash_id == hash_id), None)
            if not transaction:
                continue

            # Accept checkbox
            accept_checkbox = QCheckBox()
            accept_checkbox.setChecked(True)  # Default to accepting suggestions
            self.suggestions_table.setCellWidget(row, 0, accept_checkbox)

            # Transaction description
            self.suggestions_table.setItem(row, 1, QTableWidgetItem(transaction.description[:100]))

            # Suggested category
            category = suggestion.get('category', 'Unknown')
            self.suggestions_table.setItem(row, 2, QTableWidgetItem(category))

            # Suggested sub-category
            sub_category = suggestion.get('sub_category', '')
            self.suggestions_table.setItem(row, 3, QTableWidgetItem(sub_category))

            # Confidence
            confidence = suggestion.get('confidence', 0.0)
            self.suggestions_table.setItem(row, 4, QTableWidgetItem(f"{confidence:.2%}"))

    def accept_all_suggestions(self):
        """Accept all suggestions"""
        for row in range(self.suggestions_table.rowCount()):
            checkbox = self.suggestions_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def reject_all_suggestions(self):
        """Reject all suggestions"""
        for row in range(self.suggestions_table.rowCount()):
            checkbox = self.suggestions_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def get_accepted_suggestions(self):
        """Get the accepted suggestions"""
        accepted = {}

        for row, (hash_id, suggestion) in enumerate(self.suggestions.items()):
            checkbox = self.suggestions_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                accepted[hash_id] = suggestion

        return accepted


class BatchLabelingDialog(QDialog):
    """Dialog for batch labeling transactions"""

    def __init__(self, transactions, parent=None):
        super().__init__(parent)
        self.transactions = transactions
        self.batch_labels = {}
        self.setup_ui()

    def setup_ui(self):
        """Setup the batch labeling dialog UI"""
        self.setWindowTitle("📝 Batch Labeling")
        self.setGeometry(200, 200, 600, 400)

        layout = QVBoxLayout(self)

        # Instructions
        instructions = QLabel(f"Apply the same label to {len(self.transactions)} selected transactions:")
        instructions.setStyleSheet("font-weight: bold; padding: 10px;")
        layout.addWidget(instructions)

        # Label inputs
        form_layout = QFormLayout()

        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        form_layout.addRow("Category:", self.category_combo)

        self.subcategory_combo = QComboBox()
        self.subcategory_combo.setEditable(True)
        form_layout.addRow("Sub-category:", self.subcategory_combo)

        layout.addLayout(form_layout)

        # Transaction list
        self.transaction_list = QListWidget()
        for txn in self.transactions:
            self.transaction_list.addItem(txn.description[:100])
        layout.addWidget(self.transaction_list)

        # Buttons
        button_layout = QHBoxLayout()

        ok_btn = QPushButton("Apply Labels")
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

    def get_batch_labels(self):
        """Get the batch labels to apply"""
        category = self.category_combo.currentText().strip()
        sub_category = self.subcategory_combo.currentText().strip()

        if not category:
            return {}

        labels = {}
        for txn in self.transactions:
            labels[txn.hash_id] = {
                'category': category,
                'sub_category': sub_category
            }

        return labels


class CategoryCreationDialog(QDialog):
    """Enhanced dialog for creating new categories and sub-categories"""

    def __init__(self, category_manager, parent=None):
        super().__init__(parent)
        self.category_manager = category_manager
        self.setup_ui()

    def setup_ui(self):
        """Setup the enhanced category creation dialog UI"""
        self.setWindowTitle("➕ Create New Category/Sub-Category")
        self.setGeometry(300, 300, 500, 400)

        layout = QVBoxLayout(self)

        # Mode selection
        mode_group = QGroupBox("Creation Mode")
        mode_layout = QVBoxLayout(mode_group)

        self.main_category_radio = QRadioButton("Create Main Category with Sub-Categories")
        self.main_category_radio.setChecked(True)
        self.main_category_radio.toggled.connect(self.on_mode_changed)
        mode_layout.addWidget(self.main_category_radio)

        self.sub_category_radio = QRadioButton("Create Sub-Category for Existing Main Category")
        self.sub_category_radio.toggled.connect(self.on_mode_changed)
        mode_layout.addWidget(self.sub_category_radio)

        layout.addWidget(mode_group)

        # Main category section
        self.main_category_section = QWidget()
        main_layout = QFormLayout(self.main_category_section)

        self.category_input = QLineEdit()
        main_layout.addRow("Main Category Name:", self.category_input)

        # Category type selection
        self.category_type_combo = QComboBox()
        self.category_type_combo.addItems(["expense", "income", "both"])
        main_layout.addRow("Category Type:", self.category_type_combo)

        layout.addWidget(self.main_category_section)

        # Sub-category section for main category mode
        self.bulk_subcategory_section = QWidget()
        bulk_layout = QVBoxLayout(self.bulk_subcategory_section)

        subcategory_label = QLabel("Sub-categories (one per line, optional):")
        bulk_layout.addWidget(subcategory_label)

        self.subcategory_input = QTextEdit()
        self.subcategory_input.setMaximumHeight(120)
        bulk_layout.addWidget(self.subcategory_input)

        layout.addWidget(self.bulk_subcategory_section)

        # Sub-category section for sub-category mode
        self.single_subcategory_section = QWidget()
        single_layout = QFormLayout(self.single_subcategory_section)

        # Parent category selection
        self.parent_category_combo = QComboBox()
        self.populate_parent_categories()
        single_layout.addRow("Parent Category:", self.parent_category_combo)

        # Sub-category name
        self.subcategory_name_input = QLineEdit()
        single_layout.addRow("Sub-Category Name:", self.subcategory_name_input)

        # Sub-category description
        self.subcategory_desc_input = QLineEdit()
        single_layout.addRow("Description (optional):", self.subcategory_desc_input)

        layout.addWidget(self.single_subcategory_section)

        # Buttons
        button_layout = QHBoxLayout()

        create_btn = QPushButton("Create")
        create_btn.clicked.connect(self.create_category)
        button_layout.addWidget(create_btn)

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

        # Set initial mode
        self.on_mode_changed()

    def populate_parent_categories(self):
        """Populate the parent category dropdown with existing main categories"""
        try:
            self.parent_category_combo.clear()

            # Get all main categories (categories without parent_id)
            categories = self.category_manager.get_all_categories()
            main_categories = [cat for cat in categories if cat.parent_id is None]

            # Sort by name
            main_categories.sort(key=lambda x: x.name)

            for category in main_categories:
                self.parent_category_combo.addItem(category.name, category.id)

        except Exception as e:
            print(f"Error populating parent categories: {e}")

    def on_mode_changed(self):
        """Handle mode change between main category and sub-category creation"""
        is_main_category_mode = self.main_category_radio.isChecked()

        # Show/hide sections based on mode
        self.main_category_section.setVisible(is_main_category_mode)
        self.bulk_subcategory_section.setVisible(is_main_category_mode)
        self.single_subcategory_section.setVisible(not is_main_category_mode)

        # Update window title
        if is_main_category_mode:
            self.setWindowTitle("➕ Create New Main Category")
        else:
            self.setWindowTitle("➕ Create New Sub-Category")

    def create_category(self):
        """Create the new category or sub-category based on selected mode"""
        try:
            if self.main_category_radio.isChecked():
                self.create_main_category_with_subs()
            else:
                self.create_single_subcategory()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to create category:\n{str(e)}")

    def create_main_category_with_subs(self):
        """Create a main category with optional sub-categories"""
        category_name = self.category_input.text().strip()
        if not category_name:
            QMessageBox.warning(self, "Invalid Input", "Please enter a category name.")
            return

        # Check for duplicate main category names
        existing_categories = self.category_manager.get_all_categories()
        main_categories = [cat for cat in existing_categories if cat.parent_id is None]

        if any(cat.name.lower() == category_name.lower() for cat in main_categories):
            QMessageBox.warning(self, "Duplicate Category",
                              f"A main category named '{category_name}' already exists.")
            return

        # Get category type
        category_type = self.category_type_combo.currentText()

        # Create main category
        main_category = self.category_manager.create_main_category(
            category_name,
            category_type=category_type
        )

        # Create sub-categories
        subcategory_text = self.subcategory_input.toPlainText().strip()
        created_subcategories = []

        if subcategory_text:
            subcategories = [line.strip() for line in subcategory_text.split('\n') if line.strip()]
            for subcategory_name in subcategories:
                # Check for duplicate sub-category names within this main category
                if subcategory_name.lower() in [s.lower() for s in created_subcategories]:
                    continue  # Skip duplicates

                self.category_manager.create_sub_category(main_category.id, subcategory_name)
                created_subcategories.append(subcategory_name)

        success_msg = f"Main category '{category_name}' created successfully!"
        if created_subcategories:
            success_msg += f"\nCreated {len(created_subcategories)} sub-categories."

        QMessageBox.information(self, "Success", success_msg)
        self.accept()

    def create_single_subcategory(self):
        """Create a single sub-category for an existing main category"""
        subcategory_name = self.subcategory_name_input.text().strip()
        if not subcategory_name:
            QMessageBox.warning(self, "Invalid Input", "Please enter a sub-category name.")
            return

        # Get selected parent category
        parent_category_id = self.parent_category_combo.currentData()
        if not parent_category_id:
            QMessageBox.warning(self, "Invalid Selection", "Please select a parent category.")
            return

        # Check for duplicate sub-category names within the same parent category
        existing_categories = self.category_manager.get_all_categories()
        existing_subcategories = [cat for cat in existing_categories if cat.parent_id == parent_category_id]

        if any(cat.name.lower() == subcategory_name.lower() for cat in existing_subcategories):
            parent_name = self.parent_category_combo.currentText()
            QMessageBox.warning(self, "Duplicate Sub-Category",
                              f"A sub-category named '{subcategory_name}' already exists under '{parent_name}'.")
            return

        # Get description
        description = self.subcategory_desc_input.text().strip()

        # Create sub-category
        self.category_manager.create_sub_category(
            parent_category_id,
            subcategory_name,
            description=description
        )

        parent_name = self.parent_category_combo.currentText()
        QMessageBox.information(self, "Success",
                              f"Sub-category '{subcategory_name}' created successfully under '{parent_name}'!")
        self.accept()
