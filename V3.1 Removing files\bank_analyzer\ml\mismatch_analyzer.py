"""
ML Model Mismatch Analysis Tool

This module provides comprehensive analysis of mismatches between ML predictions
and manual labels to identify patterns and improve model performance.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging
from pathlib import Path
import json
import re
from collections import defaultdict, Counter

from .data_preparation import TransactionDataPreparator
from .ml_categorizer import MLTransactionCategorizer
from .training_data_manager import TrainingDataManager
from ..core.logger import get_logger


@dataclass
class MismatchPattern:
    """Represents a pattern in ML vs manual label mismatches"""
    pattern_type: str  # "merchant", "amount_range", "temporal", "description_pattern"
    pattern_value: str
    ml_category: str
    ml_subcategory: str
    manual_category: str
    manual_subcategory: str
    frequency: int
    confidence_scores: List[float]
    sample_descriptions: List[str]
    avg_amount: float
    amount_range: Tuple[float, float]
    transaction_types: List[str]


@dataclass
class MismatchAnalysisResult:
    """Results of mismatch analysis"""
    total_transactions: int
    total_mismatches: int
    category_mismatches: int
    subcategory_mismatches: int
    mismatch_patterns: List[MismatchPattern]
    category_confusion_matrix: Dict[str, Dict[str, int]]
    subcategory_confusion_matrix: Dict[str, Dict[str, int]]
    merchant_specific_issues: Dict[str, List[Dict[str, Any]]]
    confidence_distribution: Dict[str, int]
    recommendations: List[str]


class MLMismatchAnalyzer:
    """
    Analyzes mismatches between ML predictions and manual labels
    to identify patterns and improvement opportunities
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        
        # Initialize components
        self.data_preparator = TransactionDataPreparator()
        self.ml_categorizer = MLTransactionCategorizer()
        self.training_manager = TrainingDataManager()
        
        # Analysis configuration
        self.config = {
            "min_pattern_frequency": 2,
            "confidence_threshold": 0.6,
            "similarity_threshold": 0.8,
            "merchant_extraction_patterns": [
                r'UPI-(.+?)-',
                r'UPI/(.+?)/',
                r'IMPS-(.+?)-',
                r'NEFT-(.+?)-',
                r'(.+?)\s+ATM',
                r'POS\s+(.+?)(?:\s+\d|$)',
            ]
        }
        
        # Results storage
        self.analysis_results: Optional[MismatchAnalysisResult] = None
        
    def analyze_mismatches(self, force_refresh: bool = False) -> MismatchAnalysisResult:
        """
        Perform comprehensive mismatch analysis
        
        Args:
            force_refresh: Force re-analysis even if cached results exist
            
        Returns:
            Comprehensive mismatch analysis results
        """
        self.logger.info("Starting ML model mismatch analysis...")
        
        try:
            # Load training data with manual labels
            training_data = self._load_labeled_training_data()
            if training_data.empty:
                raise ValueError("No labeled training data available for analysis")
            
            # Generate ML predictions for all manually labeled transactions
            predictions = self._generate_ml_predictions(training_data)
            
            # Identify mismatches
            mismatches = self._identify_mismatches(training_data, predictions)
            
            # Analyze patterns in mismatches
            patterns = self._analyze_mismatch_patterns(mismatches)
            
            # Create confusion matrices
            category_confusion = self._create_confusion_matrix(mismatches, 'category')
            subcategory_confusion = self._create_confusion_matrix(mismatches, 'subcategory')
            
            # Analyze merchant-specific issues
            merchant_issues = self._analyze_merchant_issues(mismatches)
            
            # Analyze confidence distribution
            confidence_dist = self._analyze_confidence_distribution(predictions)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(patterns, mismatches)
            
            # Create analysis result
            self.analysis_results = MismatchAnalysisResult(
                total_transactions=len(training_data),
                total_mismatches=len(mismatches),
                category_mismatches=len(mismatches[mismatches['category_mismatch']]),
                subcategory_mismatches=len(mismatches[mismatches['subcategory_mismatch']]),
                mismatch_patterns=patterns,
                category_confusion_matrix=category_confusion,
                subcategory_confusion_matrix=subcategory_confusion,
                merchant_specific_issues=merchant_issues,
                confidence_distribution=confidence_dist,
                recommendations=recommendations
            )
            
            # Save analysis results
            self._save_analysis_results()
            
            self.logger.info(f"Mismatch analysis completed. Found {len(mismatches)} mismatches out of {len(training_data)} transactions")
            return self.analysis_results
            
        except Exception as e:
            self.logger.error(f"Error in mismatch analysis: {str(e)}")
            raise
    
    def _load_labeled_training_data(self) -> pd.DataFrame:
        """Load training data with manual labels"""
        try:
            # Get unique transactions with manual labels
            unique_transactions = self.data_preparator.load_unique_transactions()
            
            # Filter for manually labeled transactions only
            manually_labeled = {
                hash_id: txn for hash_id, txn in unique_transactions.items()
                if txn.is_manually_labeled and txn.category and txn.sub_category
            }
            
            if not manually_labeled:
                raise ValueError("No manually labeled transactions found")
            
            # Convert to DataFrame for analysis
            training_data = []
            for hash_id, txn in manually_labeled.items():
                training_data.append({
                    'hash_id': hash_id,
                    'description': txn.description,
                    'normalized_description': txn.normalized_description,
                    'category': txn.category,
                    'sub_category': txn.sub_category,
                    'confidence': txn.confidence,
                    'frequency': txn.frequency,
                    'min_amount': txn.amount_range[0],
                    'max_amount': txn.amount_range[1],
                    'avg_amount': sum(txn.sample_amounts) / len(txn.sample_amounts) if txn.sample_amounts else 0,
                    'transaction_types': list(txn.transaction_types),
                    'labeled_by': txn.labeled_by,
                    'labeled_at': txn.labeled_at
                })
            
            return pd.DataFrame(training_data)
            
        except Exception as e:
            self.logger.error(f"Error loading labeled training data: {str(e)}")
            raise
    
    def _generate_ml_predictions(self, training_data: pd.DataFrame) -> pd.DataFrame:
        """Generate ML predictions for all training data"""
        try:
            predictions = []
            
            for _, row in training_data.iterrows():
                # Get ML prediction
                ml_prediction = self.ml_categorizer.predict_category(row['description'])
                
                if ml_prediction:
                    predictions.append({
                        'hash_id': row['hash_id'],
                        'ml_category': ml_prediction.category,
                        'ml_subcategory': ml_prediction.sub_category,
                        'ml_confidence': ml_prediction.confidence,
                        'model_version': ml_prediction.model_version
                    })
                else:
                    # No ML prediction available
                    predictions.append({
                        'hash_id': row['hash_id'],
                        'ml_category': None,
                        'ml_subcategory': None,
                        'ml_confidence': 0.0,
                        'model_version': None
                    })
            
            return pd.DataFrame(predictions)
            
        except Exception as e:
            self.logger.error(f"Error generating ML predictions: {str(e)}")
            raise
    
    def _identify_mismatches(self, training_data: pd.DataFrame, predictions: pd.DataFrame) -> pd.DataFrame:
        """Identify mismatches between manual labels and ML predictions"""
        try:
            # Merge training data with predictions
            merged = training_data.merge(predictions, on='hash_id', how='inner')
            
            # Identify mismatches
            merged['category_mismatch'] = (
                (merged['category'] != merged['ml_category']) & 
                (merged['ml_category'].notna())
            )
            
            merged['subcategory_mismatch'] = (
                (merged['sub_category'] != merged['ml_subcategory']) & 
                (merged['ml_subcategory'].notna())
            )
            
            merged['any_mismatch'] = merged['category_mismatch'] | merged['subcategory_mismatch']
            
            # Filter to only mismatches
            mismatches = merged[merged['any_mismatch']].copy()
            
            return mismatches
            
        except Exception as e:
            self.logger.error(f"Error identifying mismatches: {str(e)}")
            raise

    def _analyze_mismatch_patterns(self, mismatches: pd.DataFrame) -> List[MismatchPattern]:
        """Analyze patterns in mismatches"""
        try:
            patterns = []

            # Group mismatches by various criteria
            pattern_groups = [
                ('merchant', self._group_by_merchant),
                ('amount_range', self._group_by_amount_range),
                ('description_pattern', self._group_by_description_pattern),
                ('transaction_type', self._group_by_transaction_type)
            ]

            for pattern_type, grouping_func in pattern_groups:
                groups = grouping_func(mismatches)

                for pattern_value, group_data in groups.items():
                    if len(group_data) >= self.config['min_pattern_frequency']:
                        # Calculate pattern statistics
                        confidence_scores = [row['ml_confidence'] for row in group_data]
                        amounts = [row['avg_amount'] for row in group_data]

                        # Find most common mismatch for this pattern
                        mismatch_pairs = [(row['category'], row['sub_category'],
                                         row['ml_category'], row['ml_subcategory'])
                                        for row in group_data]
                        most_common = Counter(mismatch_pairs).most_common(1)[0]

                        manual_cat, manual_subcat, ml_cat, ml_subcat = most_common[0]

                        pattern = MismatchPattern(
                            pattern_type=pattern_type,
                            pattern_value=pattern_value,
                            ml_category=ml_cat or "Unknown",
                            ml_subcategory=ml_subcat or "Unknown",
                            manual_category=manual_cat,
                            manual_subcategory=manual_subcat,
                            frequency=len(group_data),
                            confidence_scores=confidence_scores,
                            sample_descriptions=[row['description'][:100] for row in group_data[:5]],
                            avg_amount=np.mean(amounts) if amounts else 0.0,
                            amount_range=(min(amounts), max(amounts)) if amounts else (0.0, 0.0),
                            transaction_types=list(set(row['transaction_types'][0] if row['transaction_types'] else 'unknown'
                                                     for row in group_data))
                        )
                        patterns.append(pattern)

            # Sort patterns by frequency (most common first)
            patterns.sort(key=lambda x: x.frequency, reverse=True)

            return patterns

        except Exception as e:
            self.logger.error(f"Error analyzing mismatch patterns: {str(e)}")
            return []

    def _group_by_merchant(self, mismatches: pd.DataFrame) -> Dict[str, List[Dict]]:
        """Group mismatches by extracted merchant name"""
        groups = defaultdict(list)

        for _, row in mismatches.iterrows():
            merchant = self._extract_merchant_name(row['description'])
            if merchant:
                groups[merchant].append(row.to_dict())

        return dict(groups)

    def _group_by_amount_range(self, mismatches: pd.DataFrame) -> Dict[str, List[Dict]]:
        """Group mismatches by amount range"""
        groups = defaultdict(list)

        # Define amount ranges
        ranges = [
            (0, 100, "0-100"),
            (100, 500, "100-500"),
            (500, 1000, "500-1000"),
            (1000, 5000, "1000-5000"),
            (5000, 10000, "5000-10000"),
            (10000, float('inf'), "10000+")
        ]

        for _, row in mismatches.iterrows():
            amount = row['avg_amount']
            for min_amt, max_amt, range_label in ranges:
                if min_amt <= amount < max_amt:
                    groups[range_label].append(row.to_dict())
                    break

        return dict(groups)

    def _group_by_description_pattern(self, mismatches: pd.DataFrame) -> Dict[str, List[Dict]]:
        """Group mismatches by description patterns"""
        groups = defaultdict(list)

        # Common patterns in transaction descriptions
        patterns = [
            (r'UPI.*', 'UPI_TRANSACTIONS'),
            (r'ATM.*', 'ATM_TRANSACTIONS'),
            (r'POS.*', 'POS_TRANSACTIONS'),
            (r'NEFT.*', 'NEFT_TRANSACTIONS'),
            (r'IMPS.*', 'IMPS_TRANSACTIONS'),
            (r'.*SALARY.*', 'SALARY_TRANSACTIONS'),
            (r'.*INTEREST.*', 'INTEREST_TRANSACTIONS'),
            (r'.*DIVIDEND.*', 'DIVIDEND_TRANSACTIONS'),
            (r'.*REFUND.*', 'REFUND_TRANSACTIONS')
        ]

        for _, row in mismatches.iterrows():
            description = row['description'].upper()
            matched = False

            for pattern, pattern_name in patterns:
                if re.match(pattern, description):
                    groups[pattern_name].append(row.to_dict())
                    matched = True
                    break

            if not matched:
                groups['OTHER_PATTERNS'].append(row.to_dict())

        return dict(groups)

    def _group_by_transaction_type(self, mismatches: pd.DataFrame) -> Dict[str, List[Dict]]:
        """Group mismatches by transaction type"""
        groups = defaultdict(list)

        for _, row in mismatches.iterrows():
            txn_types = row['transaction_types']
            if txn_types:
                primary_type = txn_types[0] if isinstance(txn_types, list) else str(txn_types)
                groups[primary_type].append(row.to_dict())
            else:
                groups['unknown'].append(row.to_dict())

        return dict(groups)

    def _extract_merchant_name(self, description: str) -> Optional[str]:
        """Extract merchant name from transaction description"""
        try:
            description = description.upper().strip()

            # Try each extraction pattern
            for pattern in self.config['merchant_extraction_patterns']:
                match = re.search(pattern, description)
                if match:
                    merchant = match.group(1).strip()
                    # Clean up merchant name
                    merchant = re.sub(r'[^A-Z0-9\s]', '', merchant)
                    merchant = ' '.join(merchant.split())  # Normalize whitespace
                    if len(merchant) > 3:  # Minimum length check
                        return merchant

            # Fallback: extract first meaningful word
            words = description.split()
            for word in words:
                if len(word) > 3 and word.isalpha():
                    return word

            return None

        except Exception as e:
            self.logger.debug(f"Error extracting merchant from '{description}': {str(e)}")
            return None

    def _create_confusion_matrix(self, mismatches: pd.DataFrame, label_type: str) -> Dict[str, Dict[str, int]]:
        """Create confusion matrix for category or subcategory mismatches"""
        try:
            if label_type == 'category':
                manual_col = 'category'
                ml_col = 'ml_category'
                filter_col = 'category_mismatch'
            else:
                manual_col = 'sub_category'
                ml_col = 'ml_subcategory'
                filter_col = 'subcategory_mismatch'

            # Filter relevant mismatches
            relevant_mismatches = mismatches[mismatches[filter_col] & mismatches[ml_col].notna()]

            # Create confusion matrix
            confusion_matrix = defaultdict(lambda: defaultdict(int))

            for _, row in relevant_mismatches.iterrows():
                manual_label = row[manual_col]
                ml_label = row[ml_col]
                confusion_matrix[manual_label][ml_label] += 1

            # Convert to regular dict
            return {k: dict(v) for k, v in confusion_matrix.items()}

        except Exception as e:
            self.logger.error(f"Error creating confusion matrix: {str(e)}")
            return {}

    def _analyze_merchant_issues(self, mismatches: pd.DataFrame) -> Dict[str, List[Dict[str, Any]]]:
        """Analyze merchant-specific categorization issues"""
        try:
            merchant_issues = defaultdict(list)

            for _, row in mismatches.iterrows():
                merchant = self._extract_merchant_name(row['description'])
                if merchant:
                    issue = {
                        'description': row['description'],
                        'manual_category': row['category'],
                        'manual_subcategory': row['sub_category'],
                        'ml_category': row['ml_category'],
                        'ml_subcategory': row['ml_subcategory'],
                        'confidence': row['ml_confidence'],
                        'amount': row['avg_amount'],
                        'frequency': row['frequency']
                    }
                    merchant_issues[merchant].append(issue)

            # Sort merchants by number of issues
            sorted_issues = dict(sorted(merchant_issues.items(),
                                      key=lambda x: len(x[1]), reverse=True))

            return sorted_issues

        except Exception as e:
            self.logger.error(f"Error analyzing merchant issues: {str(e)}")
            return {}

    def _analyze_confidence_distribution(self, predictions: pd.DataFrame) -> Dict[str, int]:
        """Analyze distribution of ML prediction confidence scores"""
        try:
            # Define confidence ranges
            ranges = [
                (0.0, 0.2, "very_low"),
                (0.2, 0.4, "low"),
                (0.4, 0.6, "medium"),
                (0.6, 0.8, "high"),
                (0.8, 1.0, "very_high")
            ]

            distribution = {range_name: 0 for _, _, range_name in ranges}

            for _, row in predictions.iterrows():
                confidence = row['ml_confidence']
                for min_conf, max_conf, range_name in ranges:
                    if min_conf <= confidence < max_conf:
                        distribution[range_name] += 1
                        break

            return distribution

        except Exception as e:
            self.logger.error(f"Error analyzing confidence distribution: {str(e)}")
            return {}

    def _generate_recommendations(self, patterns: List[MismatchPattern],
                                mismatches: pd.DataFrame) -> List[str]:
        """Generate recommendations based on mismatch analysis"""
        try:
            recommendations = []

            # Analyze most common patterns
            if patterns:
                top_pattern = patterns[0]
                recommendations.append(
                    f"Priority Fix: Address {top_pattern.pattern_type} pattern '{top_pattern.pattern_value}' "
                    f"which causes {top_pattern.frequency} mismatches. "
                    f"ML predicts {top_pattern.ml_category}/{top_pattern.ml_subcategory} "
                    f"but manual label is {top_pattern.manual_category}/{top_pattern.manual_subcategory}."
                )

            # Analyze confidence issues
            low_confidence_mismatches = mismatches[mismatches['ml_confidence'] < 0.6]
            if len(low_confidence_mismatches) > 0:
                recommendations.append(
                    f"Low Confidence Issues: {len(low_confidence_mismatches)} mismatches have ML confidence < 0.6. "
                    f"Consider improving feature engineering for these cases."
                )

            # Analyze merchant-specific issues
            merchant_groups = self._group_by_merchant(mismatches)
            if merchant_groups:
                top_merchant = max(merchant_groups.items(), key=lambda x: len(x[1]))
                recommendations.append(
                    f"Merchant-Specific Fix: '{top_merchant[0]}' has {len(top_merchant[1])} mismatches. "
                    f"Consider creating specific rules or improving merchant extraction."
                )

            # Analyze category vs subcategory mismatches
            category_only = mismatches[mismatches['category_mismatch'] & ~mismatches['subcategory_mismatch']]
            subcategory_only = mismatches[~mismatches['category_mismatch'] & mismatches['subcategory_mismatch']]

            if len(category_only) > len(subcategory_only):
                recommendations.append(
                    f"Category Focus: {len(category_only)} transactions have correct subcategory but wrong category. "
                    f"Focus on improving category-level classification."
                )
            else:
                recommendations.append(
                    f"Subcategory Focus: {len(subcategory_only)} transactions have correct category but wrong subcategory. "
                    f"Focus on improving subcategory-level classification."
                )

            # Feature engineering recommendations
            upi_mismatches = mismatches[mismatches['description'].str.contains('UPI', case=False, na=False)]
            if len(upi_mismatches) > 0:
                recommendations.append(
                    f"UPI Enhancement: {len(upi_mismatches)} UPI transaction mismatches detected. "
                    f"Improve UPI merchant extraction and pattern recognition."
                )

            return recommendations

        except Exception as e:
            self.logger.error(f"Error generating recommendations: {str(e)}")
            return ["Error generating recommendations"]

    def _save_analysis_results(self):
        """Save analysis results to file"""
        try:
            if not self.analysis_results:
                return

            # Create output directory
            output_dir = self.data_dir / "mismatch_analysis"
            output_dir.mkdir(parents=True, exist_ok=True)

            # Save detailed results as JSON
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = output_dir / f"mismatch_analysis_{timestamp}.json"

            # Convert results to serializable format
            results_dict = {
                "analysis_timestamp": datetime.now().isoformat(),
                "total_transactions": self.analysis_results.total_transactions,
                "total_mismatches": self.analysis_results.total_mismatches,
                "category_mismatches": self.analysis_results.category_mismatches,
                "subcategory_mismatches": self.analysis_results.subcategory_mismatches,
                "mismatch_patterns": [
                    {
                        "pattern_type": p.pattern_type,
                        "pattern_value": p.pattern_value,
                        "ml_category": p.ml_category,
                        "ml_subcategory": p.ml_subcategory,
                        "manual_category": p.manual_category,
                        "manual_subcategory": p.manual_subcategory,
                        "frequency": p.frequency,
                        "avg_confidence": np.mean(p.confidence_scores) if p.confidence_scores else 0.0,
                        "sample_descriptions": p.sample_descriptions,
                        "avg_amount": p.avg_amount,
                        "amount_range": p.amount_range,
                        "transaction_types": p.transaction_types
                    }
                    for p in self.analysis_results.mismatch_patterns
                ],
                "category_confusion_matrix": self.analysis_results.category_confusion_matrix,
                "subcategory_confusion_matrix": self.analysis_results.subcategory_confusion_matrix,
                "merchant_specific_issues": self.analysis_results.merchant_specific_issues,
                "confidence_distribution": self.analysis_results.confidence_distribution,
                "recommendations": self.analysis_results.recommendations
            }

            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results_dict, f, indent=2, ensure_ascii=False)

            # Save summary report
            summary_file = output_dir / f"mismatch_summary_{timestamp}.txt"
            self._save_summary_report(summary_file)

            self.logger.info(f"Analysis results saved to {results_file}")

        except Exception as e:
            self.logger.error(f"Error saving analysis results: {str(e)}")

    def _save_summary_report(self, summary_file: Path):
        """Save human-readable summary report"""
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("ML MODEL MISMATCH ANALYSIS REPORT\n")
                f.write("=" * 50 + "\n\n")

                # Overview
                f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total Transactions Analyzed: {self.analysis_results.total_transactions}\n")
                f.write(f"Total Mismatches Found: {self.analysis_results.total_mismatches}\n")
                f.write(f"Category Mismatches: {self.analysis_results.category_mismatches}\n")
                f.write(f"Subcategory Mismatches: {self.analysis_results.subcategory_mismatches}\n")
                f.write(f"Overall Accuracy: {((self.analysis_results.total_transactions - self.analysis_results.total_mismatches) / self.analysis_results.total_transactions * 100):.2f}%\n\n")

                # Top mismatch patterns
                f.write("TOP MISMATCH PATTERNS\n")
                f.write("-" * 30 + "\n")
                for i, pattern in enumerate(self.analysis_results.mismatch_patterns[:10], 1):
                    f.write(f"{i}. {pattern.pattern_type.upper()}: {pattern.pattern_value}\n")
                    f.write(f"   Frequency: {pattern.frequency}\n")
                    f.write(f"   ML: {pattern.ml_category}/{pattern.ml_subcategory}\n")
                    f.write(f"   Manual: {pattern.manual_category}/{pattern.manual_subcategory}\n")
                    f.write(f"   Avg Confidence: {np.mean(pattern.confidence_scores):.3f}\n")
                    f.write(f"   Sample: {pattern.sample_descriptions[0] if pattern.sample_descriptions else 'N/A'}\n\n")

                # Recommendations
                f.write("RECOMMENDATIONS\n")
                f.write("-" * 20 + "\n")
                for i, rec in enumerate(self.analysis_results.recommendations, 1):
                    f.write(f"{i}. {rec}\n\n")

                # Confidence distribution
                f.write("CONFIDENCE DISTRIBUTION\n")
                f.write("-" * 25 + "\n")
                for conf_range, count in self.analysis_results.confidence_distribution.items():
                    f.write(f"{conf_range}: {count}\n")

        except Exception as e:
            self.logger.error(f"Error saving summary report: {str(e)}")

    def get_mismatch_details(self, pattern_type: str = None,
                           min_frequency: int = 1) -> List[MismatchPattern]:
        """
        Get detailed mismatch information

        Args:
            pattern_type: Filter by pattern type
            min_frequency: Minimum frequency threshold

        Returns:
            List of mismatch patterns matching criteria
        """
        if not self.analysis_results:
            return []

        patterns = self.analysis_results.mismatch_patterns

        # Apply filters
        if pattern_type:
            patterns = [p for p in patterns if p.pattern_type == pattern_type]

        if min_frequency > 1:
            patterns = [p for p in patterns if p.frequency >= min_frequency]

        return patterns

    def export_mismatches_for_review(self, output_file: str = None) -> str:
        """
        Export mismatches in a format suitable for manual review

        Args:
            output_file: Output file path (optional)

        Returns:
            Path to exported file
        """
        try:
            if not self.analysis_results:
                raise ValueError("No analysis results available. Run analyze_mismatches() first.")

            # Generate output file path if not provided
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"bank_analyzer_config/mismatch_analysis/mismatches_for_review_{timestamp}.csv"

            # Create output directory
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Load original mismatch data
            training_data = self._load_labeled_training_data()
            predictions = self._generate_ml_predictions(training_data)
            mismatches = self._identify_mismatches(training_data, predictions)

            # Prepare export data
            export_data = []
            for _, row in mismatches.iterrows():
                merchant = self._extract_merchant_name(row['description'])
                export_data.append({
                    'hash_id': row['hash_id'],
                    'description': row['description'],
                    'extracted_merchant': merchant or 'N/A',
                    'amount': row['avg_amount'],
                    'frequency': row['frequency'],
                    'manual_category': row['category'],
                    'manual_subcategory': row['sub_category'],
                    'ml_category': row['ml_category'] or 'N/A',
                    'ml_subcategory': row['ml_subcategory'] or 'N/A',
                    'ml_confidence': row['ml_confidence'],
                    'category_mismatch': row['category_mismatch'],
                    'subcategory_mismatch': row['subcategory_mismatch'],
                    'transaction_types': ','.join(row['transaction_types']) if row['transaction_types'] else 'unknown'
                })

            # Save to CSV
            df = pd.DataFrame(export_data)
            df.to_csv(output_path, index=False, encoding='utf-8')

            self.logger.info(f"Mismatches exported to {output_path}")
            return str(output_path)

        except Exception as e:
            self.logger.error(f"Error exporting mismatches: {str(e)}")
            raise
