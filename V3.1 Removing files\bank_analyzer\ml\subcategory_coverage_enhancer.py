"""
Subcategory Coverage Enhancer

This module addresses subcategory model coverage issues identified in the analysis:
1. Savings (54 samples, 1 subcategory) → Savings/None (53 times)
2. Transportation (26 samples, 1 subcategory) → Transportation/None (25 times)  
3. Rewards (22 samples, 1 subcategory) → Rewards/None (17 times)

It implements default subcategory assignment rules and single-class handling.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

@dataclass
class SubcategoryRule:
    """Rule for subcategory assignment"""
    category: str
    subcategory: str
    patterns: List[str]
    amount_range: Optional[Tuple[float, float]] = None
    confidence: float = 0.8
    description: str = ""

class SubcategoryCoverageEnhancer:
    """Enhances subcategory coverage for categories with insufficient diversity"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._initialize_default_subcategory_rules()
        self._initialize_pattern_based_rules()
        self._initialize_amount_based_rules()
    
    def _initialize_default_subcategory_rules(self):
        """Initialize default subcategory assignments for single-subcategory categories"""
        
        # Default subcategories for categories with insufficient diversity
        self.default_subcategories = {
            'Savings': '1 Year Savings',  # Most common pattern from analysis
            'Transportation': 'Petrol',   # Most common pattern from analysis
            'Rewards': 'Gpay Tasks',      # Most common pattern from analysis
            'Bank Charges': 'Service Charges',
            'House Hold Expenses': 'Daily Expenses',
            'Bike Maintence': 'Bike Spares',
            'Cash Limit': 'Zomato Cash Limit',
            'College': 'BTECH Fees',
            'Grooming': 'HairCut'
        }
    
    def _initialize_pattern_based_rules(self):
        """Initialize pattern-based subcategory rules"""
        
        self.pattern_based_rules = [
            # Savings subcategories
            SubcategoryRule(
                category="Savings",
                subcategory="1 Year Savings",
                patterns=[r'year', r'annual', r'yearly', r'12\s*month'],
                description="Long-term savings"
            ),
            SubcategoryRule(
                category="Savings",
                subcategory="Emergency Fund",
                patterns=[r'emergency', r'urgent', r'backup'],
                description="Emergency savings"
            ),
            SubcategoryRule(
                category="Savings",
                subcategory="Short Term Savings",
                patterns=[r'short', r'temporary', r'quick'],
                description="Short-term savings"
            ),
            
            # Transportation subcategories
            SubcategoryRule(
                category="Transportation",
                subcategory="Petrol",
                patterns=[
                    r'petrol', r'fuel', r'gas', r'bunk', r'station',
                    r'dharmaa', r'service\s*station', r'balaji\s*agencies'
                ],
                amount_range=(50, 2000),
                description="Fuel expenses"
            ),
            SubcategoryRule(
                category="Transportation",
                subcategory="Public Transport",
                patterns=[r'bus', r'metro', r'train', r'public'],
                amount_range=(10, 100),
                description="Public transportation"
            ),
            SubcategoryRule(
                category="Transportation",
                subcategory="Taxi/Uber",
                patterns=[r'uber', r'ola', r'taxi', r'cab', r'auto'],
                amount_range=(50, 500),
                description="Taxi and ride-sharing"
            ),
            
            # Rewards subcategories
            SubcategoryRule(
                category="Rewards",
                subcategory="Gpay Tasks",
                patterns=[
                    r'gpay', r'google\s*pay', r'tasks', r'rewards',
                    r'bundl\s*tec', r'authblue'
                ],
                amount_range=(1, 50),
                description="Google Pay rewards and tasks"
            ),
            SubcategoryRule(
                category="Rewards",
                subcategory="Cashback",
                patterns=[r'cashback', r'cash\s*back', r'refund'],
                description="Cashback rewards"
            ),
            SubcategoryRule(
                category="Rewards",
                subcategory="Points Redemption",
                patterns=[r'points', r'redeem', r'loyalty'],
                description="Loyalty points redemption"
            ),
            
            # Bank Charges subcategories
            SubcategoryRule(
                category="Bank Charges",
                subcategory="Service Charges",
                patterns=[r'service', r'maintenance', r'annual'],
                description="Bank service charges"
            ),
            SubcategoryRule(
                category="Bank Charges",
                subcategory="ATM Charges",
                patterns=[r'atm', r'withdrawal', r'cash'],
                description="ATM usage charges"
            ),
            
            # House Hold Expenses subcategories
            SubcategoryRule(
                category="House Hold Expenses",
                subcategory="Daily Expenses",
                patterns=[r'daily', r'routine', r'regular'],
                amount_range=(10, 500),
                description="Daily household expenses"
            ),
            SubcategoryRule(
                category="House Hold Expenses",
                subcategory="Groceries",
                patterns=[r'grocery', r'vegetables', r'fruits', r'market'],
                amount_range=(100, 2000),
                description="Grocery shopping"
            ),
            
            # Bike Maintenance subcategories
            SubcategoryRule(
                category="Bike Maintence",
                subcategory="Bike Spares",
                patterns=[
                    r'spares', r'parts', r'auto\s*spares',
                    r'suba\s*auto', r'srinivasa\s*auto'
                ],
                description="Bike spare parts"
            ),
            SubcategoryRule(
                category="Bike Maintence",
                subcategory="Service",
                patterns=[r'service', r'maintenance', r'repair'],
                description="Bike servicing"
            ),
            
            # College subcategories
            SubcategoryRule(
                category="College",
                subcategory="BTECH Fees",
                patterns=[r'btech', r'engineering', r'fees', r'tuition'],
                amount_range=(10000, 100000),
                description="Engineering college fees"
            ),
            SubcategoryRule(
                category="College",
                subcategory="Hostel Fees",
                patterns=[r'hostel', r'accommodation', r'room'],
                description="Hostel accommodation fees"
            ),
            
            # Grooming subcategories
            SubcategoryRule(
                category="Grooming",
                subcategory="HairCut",
                patterns=[r'haircut', r'hair', r'barber', r'salon'],
                amount_range=(50, 300),
                description="Hair cutting services"
            ),
            SubcategoryRule(
                category="Grooming",
                subcategory="Beauty Services",
                patterns=[r'beauty', r'facial', r'spa', r'massage'],
                description="Beauty and spa services"
            )
        ]
    
    def _initialize_amount_based_rules(self):
        """Initialize amount-based subcategory rules"""
        
        self.amount_based_rules = {
            'Savings': [
                (1, 1000, 'Short Term Savings'),
                (1000, 10000, '1 Year Savings'),
                (10000, float('inf'), 'Long Term Investment')
            ],
            'Transportation': [
                (1, 50, 'Public Transport'),
                (50, 200, 'Auto Rickshaw'),
                (200, 500, 'Taxi/Uber'),
                (500, 2000, 'Petrol'),
                (2000, float('inf'), 'Vehicle Purchase')
            ],
            'Rewards': [
                (1, 10, 'Gpay Tasks'),
                (10, 100, 'Cashback'),
                (100, float('inf'), 'Bonus Rewards')
            ],
            'Bank Charges': [
                (1, 50, 'ATM Charges'),
                (50, 200, 'Service Charges'),
                (200, float('inf'), 'Penalty Charges')
            ]
        }
    
    def get_default_subcategory(self, category: str) -> Optional[str]:
        """
        Get default subcategory for a category
        
        Args:
            category: Category name
            
        Returns:
            Default subcategory or None
        """
        return self.default_subcategories.get(category)
    
    def predict_subcategory_with_rules(self, category: str, description: str, 
                                     amount: float) -> Tuple[Optional[str], float]:
        """
        Predict subcategory using pattern and amount-based rules
        
        Args:
            category: Transaction category
            description: Transaction description
            amount: Transaction amount
            
        Returns:
            Tuple of (subcategory, confidence)
        """
        desc_lower = description.lower()
        
        # Try pattern-based rules first
        for rule in self.pattern_based_rules:
            if rule.category == category:
                # Check pattern match
                pattern_match = any(re.search(pattern, desc_lower) for pattern in rule.patterns)
                
                # Check amount range if specified
                amount_match = True
                if rule.amount_range:
                    min_amt, max_amt = rule.amount_range
                    amount_match = min_amt <= amount <= max_amt
                
                if pattern_match and amount_match:
                    self.logger.debug(f"Pattern rule matched: {category} → {rule.subcategory}")
                    return rule.subcategory, rule.confidence
        
        # Try amount-based rules
        if category in self.amount_based_rules:
            for min_amt, max_amt, subcategory in self.amount_based_rules[category]:
                if min_amt <= amount <= max_amt:
                    self.logger.debug(f"Amount rule matched: {category} → {subcategory}")
                    return subcategory, 0.6  # Lower confidence for amount-only rules
        
        # Fall back to default subcategory
        default_subcategory = self.get_default_subcategory(category)
        if default_subcategory:
            self.logger.debug(f"Default subcategory: {category} → {default_subcategory}")
            return default_subcategory, 0.5  # Lowest confidence for defaults
        
        return None, 0.0
    
    def enhance_subcategory_predictions(self, predictions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enhance subcategory predictions for categories with poor coverage
        
        Args:
            predictions: List of prediction dictionaries
            
        Returns:
            Enhanced predictions
        """
        enhanced_predictions = []
        
        for pred in predictions:
            enhanced_pred = pred.copy()
            
            category = pred.get('category', '')
            subcategory = pred.get('sub_category')
            description = pred.get('description', '')
            amount = pred.get('amount', 0.0)
            
            # If no subcategory predicted or low confidence, try rule-based prediction
            if not subcategory or pred.get('subcategory_confidence', 0) < 0.5:
                rule_subcategory, rule_confidence = self.predict_subcategory_with_rules(
                    category, description, amount
                )
                
                if rule_subcategory:
                    enhanced_pred['sub_category'] = rule_subcategory
                    enhanced_pred['subcategory_confidence'] = rule_confidence
                    enhanced_pred['subcategory_source'] = 'rule_based'
                    
                    # Adjust combined confidence
                    category_conf = pred.get('category_confidence', 0.7)
                    enhanced_pred['confidence'] = (category_conf * 0.7) + (rule_confidence * 0.3)
            
            enhanced_predictions.append(enhanced_pred)
        
        return enhanced_predictions
    
    def analyze_subcategory_coverage(self, training_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze subcategory coverage in training data
        
        Args:
            training_data: Training data
            
        Returns:
            Coverage analysis
        """
        analysis = {
            'total_transactions': len(training_data),
            'categories_with_single_subcategory': [],
            'categories_with_poor_coverage': [],
            'subcategory_distribution': {},
            'recommendations': []
        }
        
        # Group by category
        category_subcategories = {}
        for transaction in training_data:
            category = transaction.get('category', '')
            subcategory = transaction.get('sub_category', '')
            
            if category not in category_subcategories:
                category_subcategories[category] = {}
            
            if subcategory not in category_subcategories[category]:
                category_subcategories[category][subcategory] = 0
            
            category_subcategories[category][subcategory] += 1
        
        # Analyze each category
        for category, subcategories in category_subcategories.items():
            subcategory_count = len(subcategories)
            total_samples = sum(subcategories.values())
            
            analysis['subcategory_distribution'][category] = {
                'total_samples': total_samples,
                'subcategory_count': subcategory_count,
                'subcategories': subcategories
            }
            
            # Identify problematic categories
            if subcategory_count == 1:
                analysis['categories_with_single_subcategory'].append({
                    'category': category,
                    'subcategory': list(subcategories.keys())[0],
                    'samples': total_samples
                })
            elif subcategory_count < 3 and total_samples > 20:
                analysis['categories_with_poor_coverage'].append({
                    'category': category,
                    'subcategory_count': subcategory_count,
                    'samples': total_samples
                })
        
        # Generate recommendations
        for item in analysis['categories_with_single_subcategory']:
            analysis['recommendations'].append(
                f"Add subcategory rules for {item['category']} "
                f"({item['samples']} samples, 1 subcategory)"
            )
        
        for item in analysis['categories_with_poor_coverage']:
            analysis['recommendations'].append(
                f"Improve subcategory diversity for {item['category']} "
                f"({item['samples']} samples, {item['subcategory_count']} subcategories)"
            )
        
        self.logger.info(f"Subcategory coverage analysis:")
        self.logger.info(f"  Categories with single subcategory: {len(analysis['categories_with_single_subcategory'])}")
        self.logger.info(f"  Categories with poor coverage: {len(analysis['categories_with_poor_coverage'])}")
        
        return analysis
    
    def create_subcategory_training_data(self, original_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create enhanced training data with improved subcategory coverage
        
        Args:
            original_data: Original training data
            
        Returns:
            Enhanced training data with better subcategory coverage
        """
        enhanced_data = []
        
        for transaction in original_data:
            enhanced_transaction = transaction.copy()
            
            category = transaction.get('category', '')
            subcategory = transaction.get('sub_category', '')
            description = transaction.get('description', '')
            amount = transaction.get('amount', 0.0)
            
            # If subcategory is missing or generic, try to improve it
            if not subcategory or subcategory in ['General', 'Miscellaneous', 'Other']:
                rule_subcategory, rule_confidence = self.predict_subcategory_with_rules(
                    category, description, amount
                )
                
                if rule_subcategory:
                    enhanced_transaction['sub_category'] = rule_subcategory
                    enhanced_transaction['subcategory_source'] = 'rule_enhanced'
            
            enhanced_data.append(enhanced_transaction)
        
        return enhanced_data
