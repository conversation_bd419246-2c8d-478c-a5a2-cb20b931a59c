"""
Improved Training Process with Class Balancing and Data Augmentation

This module implements enhanced training techniques to address:
1. Class imbalance issues
2. Data augmentation for rare categories
3. Optimized training parameters
4. Better generalization techniques
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from collections import Counter
from datetime import datetime
import random

# ML imports with availability checking
try:
    from sklearn.model_selection import StratifiedKFold, GridSearchCV
    from sklearn.utils.class_weight import compute_class_weight
    from sklearn.metrics import classification_report, confusion_matrix
    from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
    from imblearn.under_sampling import RandomUnderSampler, EditedNearestNeighbours
    from imblearn.combine import SMOTETomek, SMOTEENN
    IMBALANCED_LEARN_AVAILABLE = True
except ImportError:
    IMBALANCED_LEARN_AVAILABLE = False

from .enhanced_ml_categorizer import EnhancedMLCategorizer
from .category_confusion_resolver import CategoryConfusionResolver

class ImprovedTrainingProcess:
    """Improved training process with advanced techniques"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.confusion_resolver = CategoryConfusionResolver()
        self._initialize_training_config()
    
    def _initialize_training_config(self):
        """Initialize training configuration"""
        self.training_config = {
            # Class balancing
            'enable_class_balancing': True,
            'balancing_strategy': 'smote',  # 'smote', 'adasyn', 'borderline_smote', 'combined'
            'min_samples_per_class': 3,
            'max_samples_per_class': 100,
            
            # Data augmentation
            'enable_data_augmentation': True,
            'augmentation_factor': 2,  # Multiply rare class samples by this factor
            'rare_class_threshold': 10,  # Classes with fewer samples are considered rare
            
            # Training optimization
            'use_stratified_cv': True,
            'cv_folds': 5,
            'enable_hyperparameter_tuning': True,
            'random_state': 42,
            
            # Model selection
            'test_multiple_algorithms': True,
            'algorithms_to_test': ['random_forest', 'svm', 'gradient_boosting'],
            
            # Performance thresholds
            'min_accuracy_threshold': 0.85,
            'min_f1_threshold': 0.80,
        }
    
    def analyze_class_distribution(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze class distribution to identify imbalance issues
        
        Args:
            training_data: Training dataset
            
        Returns:
            Analysis results
        """
        analysis = {
            'total_samples': len(training_data),
            'category_distribution': {},
            'subcategory_distribution': {},
            'imbalance_ratio': 0.0,
            'rare_categories': [],
            'dominant_categories': [],
            'needs_balancing': False
        }
        
        # Category distribution
        category_counts = training_data['category'].value_counts()
        analysis['category_distribution'] = category_counts.to_dict()
        
        # Subcategory distribution
        subcategory_counts = training_data.groupby(['category', 'sub_category']).size()
        analysis['subcategory_distribution'] = subcategory_counts.to_dict()
        
        # Calculate imbalance ratio
        max_count = category_counts.max()
        min_count = category_counts.min()
        analysis['imbalance_ratio'] = max_count / min_count if min_count > 0 else float('inf')
        
        # Identify rare and dominant categories
        rare_threshold = self.training_config['rare_class_threshold']
        for category, count in category_counts.items():
            if count < rare_threshold:
                analysis['rare_categories'].append((category, count))
            elif count > max_count * 0.3:  # More than 30% of max
                analysis['dominant_categories'].append((category, count))
        
        # Determine if balancing is needed
        analysis['needs_balancing'] = (
            analysis['imbalance_ratio'] > 3.0 or
            len(analysis['rare_categories']) > 0
        )
        
        self.logger.info(f"Class distribution analysis:")
        self.logger.info(f"  Total samples: {analysis['total_samples']}")
        self.logger.info(f"  Categories: {len(category_counts)}")
        self.logger.info(f"  Imbalance ratio: {analysis['imbalance_ratio']:.2f}")
        self.logger.info(f"  Rare categories: {len(analysis['rare_categories'])}")
        self.logger.info(f"  Needs balancing: {analysis['needs_balancing']}")
        
        return analysis
    
    def augment_rare_categories(self, training_data: pd.DataFrame, 
                              class_analysis: Dict[str, Any]) -> pd.DataFrame:
        """
        Augment data for rare categories using text variation techniques
        
        Args:
            training_data: Original training data
            class_analysis: Class distribution analysis
            
        Returns:
            Augmented training data
        """
        if not self.training_config['enable_data_augmentation']:
            return training_data
        
        augmented_data = training_data.copy()
        augmentation_factor = self.training_config['augmentation_factor']
        
        for category, count in class_analysis['rare_categories']:
            if count < self.training_config['min_samples_per_class']:
                continue
            
            category_data = training_data[training_data['category'] == category]
            
            # Generate augmented samples
            augmented_samples = []
            target_samples = min(count * augmentation_factor, 
                               self.training_config['max_samples_per_class'])
            
            for _ in range(target_samples - count):
                # Select random sample from category
                base_sample = category_data.sample(1).iloc[0].copy()
                
                # Apply augmentation techniques
                augmented_sample = self._augment_transaction_sample(base_sample)
                augmented_samples.append(augmented_sample)
            
            if augmented_samples:
                augmented_df = pd.DataFrame(augmented_samples)
                augmented_data = pd.concat([augmented_data, augmented_df], ignore_index=True)
                
                self.logger.info(f"Augmented {category}: {count} → {len(augmented_samples) + count} samples")
        
        return augmented_data
    
    def _augment_transaction_sample(self, sample: pd.Series) -> Dict[str, Any]:
        """
        Augment a single transaction sample
        
        Args:
            sample: Original sample
            
        Returns:
            Augmented sample
        """
        augmented = sample.to_dict()
        
        # Vary amount slightly (±10%)
        if 'amount' in augmented and augmented['amount'] > 0:
            variation = random.uniform(0.9, 1.1)
            augmented['amount'] = augmented['amount'] * variation
        
        # Add slight variations to description (preserve key information)
        if 'normalized_description' in augmented:
            desc = augmented['normalized_description']
            # Simple augmentation: add/remove common words
            common_additions = ['payment', 'transaction', 'transfer']
            if random.random() < 0.3:  # 30% chance to add word
                desc += f" {random.choice(common_additions)}"
            augmented['normalized_description'] = desc
        
        # Vary temporal features slightly
        if 'day_of_week' in augmented:
            # Keep same day type (weekday/weekend) but vary slightly
            current_day = augmented['day_of_week']
            if current_day < 5:  # Weekday
                augmented['day_of_week'] = random.choice([0, 1, 2, 3, 4])
            else:  # Weekend
                augmented['day_of_week'] = random.choice([5, 6])
        
        return augmented
    
    def apply_class_balancing(self, X: np.ndarray, y: np.ndarray, 
                            strategy: str = 'smote') -> Tuple[np.ndarray, np.ndarray]:
        """
        Apply class balancing techniques
        
        Args:
            X: Feature matrix
            y: Target labels
            strategy: Balancing strategy
            
        Returns:
            Balanced X and y
        """
        if not IMBALANCED_LEARN_AVAILABLE:
            self.logger.warning("imbalanced-learn not available, skipping class balancing")
            return X, y
        
        try:
            if strategy == 'smote':
                sampler = SMOTE(random_state=self.training_config['random_state'], k_neighbors=1)
            elif strategy == 'adasyn':
                sampler = ADASYN(random_state=self.training_config['random_state'])
            elif strategy == 'borderline_smote':
                sampler = BorderlineSMOTE(random_state=self.training_config['random_state'])
            elif strategy == 'combined':
                sampler = SMOTETomek(random_state=self.training_config['random_state'])
            else:
                self.logger.warning(f"Unknown balancing strategy: {strategy}")
                return X, y
            
            X_balanced, y_balanced = sampler.fit_resample(X, y)
            
            self.logger.info(f"Class balancing applied ({strategy}):")
            self.logger.info(f"  Original samples: {len(X)}")
            self.logger.info(f"  Balanced samples: {len(X_balanced)}")
            
            return X_balanced, y_balanced
            
        except Exception as e:
            self.logger.error(f"Error in class balancing: {str(e)}")
            return X, y
    
    def optimize_hyperparameters(self, model, X: np.ndarray, y: np.ndarray) -> Any:
        """
        Optimize model hyperparameters using grid search
        
        Args:
            model: Base model to optimize
            X: Feature matrix
            y: Target labels
            
        Returns:
            Optimized model
        """
        if not self.training_config['enable_hyperparameter_tuning']:
            return model
        
        try:
            # Define parameter grids for different models
            param_grids = {
                'RandomForestClassifier': {
                    'classifier__n_estimators': [100, 200, 300],
                    'classifier__max_depth': [10, 15, 20, None],
                    'classifier__min_samples_split': [2, 5, 10],
                    'classifier__min_samples_leaf': [1, 2, 4]
                },
                'SVC': {
                    'classifier__C': [0.1, 1, 10, 100],
                    'classifier__gamma': ['scale', 'auto', 0.001, 0.01],
                    'classifier__kernel': ['rbf', 'linear']
                }
            }
            
            model_name = type(model.named_steps['classifier']).__name__
            param_grid = param_grids.get(model_name, {})
            
            if not param_grid:
                self.logger.info(f"No parameter grid defined for {model_name}")
                return model
            
            # Perform grid search
            cv_folds = min(self.training_config['cv_folds'], len(np.unique(y)))
            if cv_folds < 2:
                self.logger.warning("Insufficient data for cross-validation")
                return model
            
            grid_search = GridSearchCV(
                model, param_grid,
                cv=StratifiedKFold(n_splits=cv_folds, shuffle=True, 
                                 random_state=self.training_config['random_state']),
                scoring='accuracy',
                n_jobs=-1,
                verbose=1
            )
            
            grid_search.fit(X, y)
            
            self.logger.info(f"Hyperparameter optimization completed:")
            self.logger.info(f"  Best score: {grid_search.best_score_:.3f}")
            self.logger.info(f"  Best params: {grid_search.best_params_}")
            
            return grid_search.best_estimator_
            
        except Exception as e:
            self.logger.error(f"Error in hyperparameter optimization: {str(e)}")
            return model
    
    def train_with_improved_process(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Train models using improved process with all enhancements
        
        Args:
            training_data: Training dataset
            
        Returns:
            Training results
        """
        results = {
            'success': False,
            'models_trained': {},
            'class_analysis': {},
            'performance_metrics': {},
            'errors': []
        }
        
        try:
            # Analyze class distribution
            class_analysis = self.analyze_class_distribution(training_data)
            results['class_analysis'] = class_analysis
            
            # Augment rare categories
            if class_analysis['needs_balancing']:
                training_data = self.augment_rare_categories(training_data, class_analysis)
                self.logger.info(f"Training data after augmentation: {len(training_data)} samples")
            
            # Initialize enhanced categorizer
            categorizer = EnhancedMLCategorizer()
            
            # Prepare enhanced training data
            enhanced_data, has_sufficient_data = categorizer.prepare_enhanced_training_data(training_data)
            
            if not has_sufficient_data:
                results['errors'].append("Insufficient training data after enhancement")
                return results
            
            # Train models with improved process
            training_result = categorizer.train_enhanced_models(enhanced_data, force_retrain=True)
            
            if training_result['success']:
                results['success'] = True
                results['models_trained'] = {
                    'category_model': training_result['category_model_trained'],
                    'subcategory_models': training_result['subcategory_models_trained']
                }
                results['performance_metrics'] = training_result['metrics']
                
                # Evaluate model performance
                evaluation_result = categorizer.evaluate_enhanced_model(enhanced_data)
                results['performance_metrics'].update(evaluation_result)
                
                self.logger.info("Improved training process completed successfully")
            else:
                results['errors'].extend(training_result.get('errors', []))
            
        except Exception as e:
            error_msg = f"Error in improved training process: {str(e)}"
            self.logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def validate_model_performance(self, model_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate model performance against thresholds
        
        Args:
            model_results: Training results
            
        Returns:
            Validation results
        """
        validation = {
            'meets_accuracy_threshold': False,
            'meets_f1_threshold': False,
            'overall_validation': False,
            'recommendations': []
        }
        
        metrics = model_results.get('performance_metrics', {})
        
        # Check accuracy threshold
        category_accuracy = metrics.get('category_accuracy', 0.0)
        if category_accuracy >= self.training_config['min_accuracy_threshold']:
            validation['meets_accuracy_threshold'] = True
        else:
            validation['recommendations'].append(
                f"Category accuracy ({category_accuracy:.3f}) below threshold "
                f"({self.training_config['min_accuracy_threshold']:.3f})"
            )
        
        # Check F1 threshold (if available)
        category_report = metrics.get('category_report', {})
        if category_report and 'weighted avg' in category_report:
            f1_score = category_report['weighted avg'].get('f1-score', 0.0)
            if f1_score >= self.training_config['min_f1_threshold']:
                validation['meets_f1_threshold'] = True
            else:
                validation['recommendations'].append(
                    f"F1 score ({f1_score:.3f}) below threshold "
                    f"({self.training_config['min_f1_threshold']:.3f})"
                )
        
        # Overall validation
        validation['overall_validation'] = (
            validation['meets_accuracy_threshold'] and
            validation['meets_f1_threshold']
        )
        
        if not validation['overall_validation']:
            validation['recommendations'].extend([
                "Consider increasing training data",
                "Review feature engineering",
                "Adjust class balancing strategy",
                "Try different algorithms"
            ])
        
        return validation
