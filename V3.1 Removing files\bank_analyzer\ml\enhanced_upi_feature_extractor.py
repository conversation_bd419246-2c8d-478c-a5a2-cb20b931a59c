"""
Enhanced UPI Feature Extractor for improved ML model performance

This module provides advanced feature extraction specifically for UPI transactions,
addressing the poor performance on complex UPI strings and merchant identification.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum

@dataclass
class UPIComponents:
    """Structured representation of UPI transaction components"""
    bank_code: Optional[str] = None
    merchant_name: Optional[str] = None
    upi_id: Optional[str] = None
    transaction_type: Optional[str] = None
    reference_number: Optional[str] = None
    amount_hint: Optional[str] = None
    service_provider: Optional[str] = None
    is_person_to_person: bool = False
    is_merchant_payment: bool = False
    confidence_score: float = 0.0

class UPIServiceProvider(Enum):
    """Known UPI service providers"""
    PAYTM = "paytm"
    PHONEPE = "phonepe"
    GPAY = "gpay"
    BHIM = "bhim"
    AMAZON_PAY = "amazonpay"
    ZOMATO = "zomato"
    SWIGGY = "swiggy"
    MEESHO = "meesho"
    UNKNOWN = "unknown"

class EnhancedUPIFeatureExtractor:
    """Enhanced feature extractor for UPI transactions"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._initialize_patterns()
        self._initialize_merchant_mappings()
    
    def _initialize_patterns(self):
        """Initialize regex patterns for UPI parsing"""
        # Bank code patterns
        self.bank_patterns = [
            r'^([A-Z]{4}\d{7})',  # Standard bank IFSC pattern
            r'^([A-Z]{4}\d{4})',  # Alternative bank code
        ]
        
        # Merchant name extraction patterns
        self.merchant_patterns = [
            # Standard UPI format: BANK/MERCHANT_NAME/XXXXX/upi_id
            r'/([^/]+)/XXXXX',
            r'/([^/]+)\s*/XXXXX',
            # PayTM specific patterns
            r'paytmqr[^@]*@paytm',
            r'paytm-\d+@paytm',
            # Service-specific patterns
            r'zomato[^@]*@[^/]+',
            r'swiggy[^@]*@[^/]+',
            r'meesho[^@]*@[^/]+',
            r'google[^@]*@[^/]+',
        ]
        
        # UPI ID patterns
        self.upi_id_patterns = [
            r'([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+)',
            r'/([^/\s]+@[^/\s]+)/',
        ]
        
        # Transaction type patterns
        self.transaction_type_patterns = {
            'food_ordering': [r'FoodOrdering', r'zomato', r'swiggy', r'food'],
            'recharge': [r'recharge', r'rch', r'mobile', r'prepaid'],
            'bill_payment': [r'billpay', r'bill', r'electricity', r'gas'],
            'transfer': [r'transfer', r'p2p', r'person'],
            'shopping': [r'shopping', r'ecommerce', r'amazon', r'flipkart'],
        }
    
    def _initialize_merchant_mappings(self):
        """Initialize known merchant mappings for better categorization"""
        self.merchant_category_hints = {
            # Food & Dining
            'zomato': ('Food & Dining', 'Food Delivery'),
            'swiggy': ('Food & Dining', 'Food Delivery'),
            'dominos': ('Food & Dining', 'Fast Food'),
            'kfc': ('Food & Dining', 'Fast Food'),
            'mcdonalds': ('Food & Dining', 'Fast Food'),
            
            # Transportation
            'uber': ('Transportation', 'Taxi/Uber'),
            'ola': ('Transportation', 'Taxi/Uber'),
            'rapido': ('Transportation', 'Taxi/Uber'),
            
            # Shopping
            'amazon': ('Online Stores', 'Amazon'),
            'flipkart': ('Online Stores', 'Flipkart'),
            'meesho': ('Online Stores', 'Meesho'),
            'myntra': ('Online Stores', 'Fashion'),
            'ajio': ('Online Stores', 'Fashion'),
            
            # Recharge & Bills
            'airtel': ('Recharge', 'Mobile'),
            'jio': ('Recharge', 'Mobile'),
            'vodafone': ('Recharge', 'Mobile'),
            'bsnl': ('Recharge', 'Mobile'),
            
            # Entertainment
            'netflix': ('Entertainment', 'Streaming'),
            'spotify': ('Entertainment', 'Music'),
            'hotstar': ('Entertainment', 'Streaming'),
            'prime': ('Entertainment', 'Streaming'),
            
            # Fuel
            'petrol': ('Transportation', 'Petrol'),
            'diesel': ('Transportation', 'Petrol'),
            'fuel': ('Transportation', 'Petrol'),
        }
        
        # Person-to-person indicators
        self.p2p_indicators = [
            'mr ', 'mrs ', 'ms ', 'dr ', 'prof ',
            'kumar', 'singh', 'sharma', 'gupta', 'agarwal',
            'personal', 'friend', 'family'
        ]
    
    def extract_upi_components(self, description: str) -> UPIComponents:
        """
        Extract structured components from UPI transaction description
        
        Args:
            description: Raw UPI transaction description
            
        Returns:
            UPIComponents object with extracted information
        """
        if not description:
            return UPIComponents()
        
        components = UPIComponents()
        desc_upper = description.upper()
        desc_lower = description.lower()
        
        # Extract bank code
        components.bank_code = self._extract_bank_code(desc_upper)
        
        # Extract merchant name
        components.merchant_name = self._extract_merchant_name(description)
        
        # Extract UPI ID
        components.upi_id = self._extract_upi_id(description)
        
        # Determine transaction type
        components.transaction_type = self._determine_transaction_type(desc_lower)
        
        # Extract reference number
        components.reference_number = self._extract_reference_number(description)
        
        # Determine service provider
        components.service_provider = self._determine_service_provider(desc_lower)
        
        # Classify as P2P or merchant payment
        components.is_person_to_person = self._is_person_to_person(components.merchant_name or "")
        components.is_merchant_payment = not components.is_person_to_person
        
        # Calculate confidence score
        components.confidence_score = self._calculate_confidence_score(components)
        
        return components

    def _extract_bank_code(self, description: str) -> Optional[str]:
        """Extract bank code from UPI description"""
        for pattern in self.bank_patterns:
            match = re.search(pattern, description)
            if match:
                return match.group(1)
        return None

    def _extract_merchant_name(self, description: str) -> Optional[str]:
        """Extract merchant name from UPI description"""
        # Try standard UPI format first
        for pattern in self.merchant_patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                merchant = match.group(1) if match.groups() else match.group(0)
                # Clean up merchant name
                merchant = re.sub(r'[^a-zA-Z0-9\s]', ' ', merchant)
                merchant = re.sub(r'\s+', ' ', merchant).strip()
                if len(merchant) > 2:  # Valid merchant name
                    return merchant
        return None

    def _extract_upi_id(self, description: str) -> Optional[str]:
        """Extract UPI ID from description"""
        for pattern in self.upi_id_patterns:
            match = re.search(pattern, description)
            if match:
                upi_id = match.group(1)
                if '@' in upi_id and len(upi_id) > 5:
                    return upi_id
        return None

    def _determine_transaction_type(self, description: str) -> Optional[str]:
        """Determine transaction type from description"""
        for txn_type, patterns in self.transaction_type_patterns.items():
            for pattern in patterns:
                if re.search(pattern, description, re.IGNORECASE):
                    return txn_type
        return None

    def _extract_reference_number(self, description: str) -> Optional[str]:
        """Extract reference number from description"""
        # Look for UPI reference patterns
        patterns = [
            r'UPI/(\d+)',
            r'REF[:\s]*(\w+)',
            r'TXN[:\s]*(\w+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, description)
            if match:
                return match.group(1)
        return None

    def _determine_service_provider(self, description: str) -> str:
        """Determine UPI service provider"""
        if 'paytm' in description:
            return UPIServiceProvider.PAYTM.value
        elif 'phonepe' in description:
            return UPIServiceProvider.PHONEPE.value
        elif 'gpay' in description or 'google' in description:
            return UPIServiceProvider.GPAY.value
        elif 'bhim' in description:
            return UPIServiceProvider.BHIM.value
        elif 'amazon' in description:
            return UPIServiceProvider.AMAZON_PAY.value
        elif 'zomato' in description:
            return UPIServiceProvider.ZOMATO.value
        elif 'swiggy' in description:
            return UPIServiceProvider.SWIGGY.value
        elif 'meesho' in description:
            return UPIServiceProvider.MEESHO.value
        else:
            return UPIServiceProvider.UNKNOWN.value

    def _is_person_to_person(self, merchant_name: str) -> bool:
        """Determine if transaction is person-to-person"""
        if not merchant_name:
            return False

        merchant_lower = merchant_name.lower()

        # Check for person indicators
        for indicator in self.p2p_indicators:
            if indicator in merchant_lower:
                return True

        # Check if it looks like a person's name (has common name patterns)
        # Simple heuristic: contains common titles or name patterns
        name_patterns = [
            r'\b(mr|mrs|ms|dr|prof)\s+[a-z]+',
            r'\b[a-z]+\s+(kumar|singh|sharma|gupta|agarwal|reddy|rao)\b',
            r'^[a-z]+\s+[a-z]+$',  # Simple first name last name pattern
        ]

        for pattern in name_patterns:
            if re.search(pattern, merchant_lower):
                return True

        return False

    def _calculate_confidence_score(self, components: UPIComponents) -> float:
        """Calculate confidence score for extracted components"""
        score = 0.0

        # Bank code found
        if components.bank_code:
            score += 0.2

        # Merchant name found and cleaned
        if components.merchant_name:
            score += 0.3
            # Bonus for known merchants
            if components.merchant_name.lower() in self.merchant_category_hints:
                score += 0.2

        # UPI ID found
        if components.upi_id:
            score += 0.2

        # Transaction type determined
        if components.transaction_type:
            score += 0.1

        # Service provider identified
        if components.service_provider != UPIServiceProvider.UNKNOWN.value:
            score += 0.1

        # Reference number found
        if components.reference_number:
            score += 0.1

        return min(score, 1.0)

    def get_category_hint(self, components: UPIComponents) -> Optional[Tuple[str, str]]:
        """Get category hint based on extracted components"""
        if not components.merchant_name:
            return None

        merchant_lower = components.merchant_name.lower()

        # Direct merchant mapping
        for merchant, (category, subcategory) in self.merchant_category_hints.items():
            if merchant in merchant_lower:
                return (category, subcategory)

        # Transaction type based hints
        if components.transaction_type:
            type_hints = {
                'food_ordering': ('Food & Dining', 'Food Delivery'),
                'recharge': ('Recharge', 'Mobile'),
                'bill_payment': ('Bills', 'Utilities'),
                'shopping': ('Online Stores', 'General'),
            }
            return type_hints.get(components.transaction_type)

        return None

    def generate_enhanced_features(self, description: str, amount: float = 0.0) -> Dict[str, any]:
        """
        Generate enhanced features for ML model training

        Args:
            description: Transaction description
            amount: Transaction amount

        Returns:
            Dictionary of enhanced features
        """
        components = self.extract_upi_components(description)

        features = {
            # Basic UPI features
            'has_bank_code': components.bank_code is not None,
            'has_merchant_name': components.merchant_name is not None,
            'has_upi_id': components.upi_id is not None,
            'has_reference': components.reference_number is not None,

            # Transaction classification
            'is_person_to_person': components.is_person_to_person,
            'is_merchant_payment': components.is_merchant_payment,

            # Service provider features
            'service_provider': components.service_provider,
            'is_paytm': components.service_provider == UPIServiceProvider.PAYTM.value,
            'is_gpay': components.service_provider == UPIServiceProvider.GPAY.value,
            'is_phonepe': components.service_provider == UPIServiceProvider.PHONEPE.value,

            # Transaction type features
            'transaction_type': components.transaction_type or 'unknown',
            'is_food_ordering': components.transaction_type == 'food_ordering',
            'is_recharge': components.transaction_type == 'recharge',
            'is_bill_payment': components.transaction_type == 'bill_payment',
            'is_shopping': components.transaction_type == 'shopping',

            # Merchant features
            'merchant_name_clean': components.merchant_name or '',
            'merchant_length': len(components.merchant_name or ''),

            # Amount-based features
            'amount_range': self._categorize_amount(amount),
            'is_small_amount': amount < 100,
            'is_medium_amount': 100 <= amount <= 1000,
            'is_large_amount': amount > 1000,

            # Confidence and quality
            'extraction_confidence': components.confidence_score,
            'has_category_hint': self.get_category_hint(components) is not None,
        }

        # Add category hint if available
        category_hint = self.get_category_hint(components)
        if category_hint:
            features['category_hint'] = category_hint[0]
            features['subcategory_hint'] = category_hint[1]
        else:
            features['category_hint'] = None
            features['subcategory_hint'] = None

        return features

    def _categorize_amount(self, amount: float) -> str:
        """Categorize amount into ranges"""
        if amount < 50:
            return 'very_small'
        elif amount < 200:
            return 'small'
        elif amount < 1000:
            return 'medium'
        elif amount < 5000:
            return 'large'
        else:
            return 'very_large'
