#!/usr/bin/env python3
"""
GPU Training Status Checker
Monitor the progress of GPU model training and check if models are ready
"""

import sys
import time
from pathlib import Path
from datetime import datetime

# Add bank_analyzer to path
sys.path.append('.')

def check_gpu_model_files():
    """Check if GPU model files exist"""
    print("🔍 CHECKING GPU MODEL FILES")
    print("=" * 40)
    
    model_dir = Path("bank_analyzer_config/ml_models")
    
    required_files = {
        "gpu_deep_learning_model.pt": "Main GPU model",
        "tokenizer": "Tokenizer directory", 
        "gpu_label_encoder.joblib": "Category mappings",
        "category_readiness_stats.json": "Category analysis"
    }
    
    all_exist = True
    
    for filename, description in required_files.items():
        filepath = model_dir / filename
        exists = filepath.exists()
        status = "✅" if exists else "❌"
        
        if exists and filepath.is_dir():
            # Check if tokenizer directory has files
            files_count = len(list(filepath.glob("*")))
            print(f"{status} {filename:<30} - {description} ({files_count} files)")
        elif exists:
            # Show file size
            size_mb = filepath.stat().st_size / (1024 * 1024)
            print(f"{status} {filename:<30} - {description} ({size_mb:.1f} MB)")
        else:
            print(f"{status} {filename:<30} - {description} (Missing)")
            all_exist = False
    
    print()
    if all_exist:
        print("🎉 All GPU model files are present!")
        return True
    else:
        print("⚠️ Some GPU model files are missing")
        return False

def test_gpu_system():
    """Test if the GPU system is working"""
    print("\n🧪 TESTING GPU SYSTEM")
    print("=" * 40)
    
    try:
        from bank_analyzer.ml.intelligent_category_router import IntelligentCategoryRouter
        
        print("📦 Initializing intelligent category router...")
        router = IntelligentCategoryRouter()
        
        # Test transaction
        test_transaction = {
            'description': 'UPI-SALARY CREDIT FROM TECH COMPANY-GPAY',
            'amount': 50000.0,
            'date': '2025-01-01'
        }
        
        print("🔬 Testing categorization...")
        result = router.categorize_transaction(test_transaction)
        
        print(f"✅ Categorization successful!")
        print(f"   Category: {result.category}")
        print(f"   Sub-Category: {result.sub_category}")
        print(f"   Method: {result.method_used}")
        print(f"   Confidence: {result.confidence:.2f}")
        
        # Check if GPU was used
        if result.method_used == "GPU_ML":
            print("🚀 GPU deep learning is working!")
            return True
        else:
            print(f"🔧 Using fallback method: {result.method_used}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing GPU system: {e}")
        return False

def show_training_command():
    """Show command to train GPU model"""
    print("\n💡 GPU MODEL TRAINING")
    print("=" * 40)
    
    training_data = Path("colab_training_data.csv")
    if training_data.exists():
        print(f"📁 Training data available: {training_data}")
        print("\n🚀 To train GPU model, run:")
        print("python -c \"from bank_analyzer.ml.integrated_gpu_categorizer import IntegratedGPUCategorizer; c = IntegratedGPUCategorizer(); result = c.train_gpu_model('colab_training_data.csv'); print(f'Training completed with {result.get(\\\"accuracy\\\", 0)*100:.1f}% accuracy')\"")
        
        print("\n📊 Or for detailed training with cross-validation:")
        print("python test_gpu_system_performance.py")
    else:
        print("❌ Training data not found: colab_training_data.csv")
        print("   Add training data to enable GPU model training")

def monitor_training_progress():
    """Monitor training progress by checking log files"""
    print("\n📊 MONITORING TRAINING PROGRESS")
    print("=" * 40)
    
    log_dir = Path("logs")
    if log_dir.exists():
        log_files = list(log_dir.glob("*.log"))
        if log_files:
            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"📄 Latest log file: {latest_log}")
            
            # Read last few lines
            try:
                with open(latest_log, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print("\n📝 Recent log entries:")
                        for line in lines[-10:]:  # Last 10 lines
                            if any(keyword in line.lower() for keyword in ['training', 'gpu', 'model', 'accuracy', 'epoch']):
                                print(f"   {line.strip()}")
            except Exception as e:
                print(f"   Could not read log file: {e}")
        else:
            print("📄 No log files found")
    else:
        print("📄 Log directory not found")

def main():
    """Main status checker"""
    print("🤖 GPU TRAINING STATUS CHECKER")
    print("=" * 50)
    print(f"⏰ Check time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check model files
    gpu_ready = check_gpu_model_files()
    
    # Test system
    gpu_working = test_gpu_system()
    
    # Monitor progress
    monitor_training_progress()
    
    # Show training command if needed
    if not gpu_ready:
        show_training_command()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 STATUS SUMMARY")
    print("-" * 20)
    
    if gpu_ready and gpu_working:
        print("🎉 GPU system is fully operational!")
        print("✅ All model files present")
        print("✅ GPU categorization working")
    elif gpu_ready:
        print("⚠️ GPU model files exist but system not using GPU")
        print("💡 Check logs for potential issues")
    else:
        print("🔧 GPU system not ready")
        print("💡 Train GPU model to enable GPU categorization")
    
    print(f"\n🔄 Run this script again to check updated status")

if __name__ == "__main__":
    main()
