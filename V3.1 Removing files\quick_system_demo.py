#!/usr/bin/env python3
"""
Quick GPU System Demo

A quick demonstration of the GPU-accelerated categorization system
showing the intelligent routing and fallback capabilities.
"""

import sys
import time
from pathlib import Path
from datetime import datetime, date
from decimal import Decimal

# Add bank_analyzer to path
sys.path.append('.')

def demo_intelligent_routing():
    """
    Demonstrate the intelligent routing system without requiring full GPU training
    """
    print("🚀 GPU SYSTEM INTELLIGENT ROUTING DEMO")
    print("=" * 60)
    
    try:
        # Import the intelligent router and transaction models
        from bank_analyzer.ml.intelligent_category_router import IntelligentCategoryRouter
        from bank_analyzer.models.transaction import RawTransaction

        print("📦 Initializing intelligent category router...")
        router = IntelligentCategoryRouter()

        # Sample transactions for testing - create proper RawTransaction objects
        test_transactions = [
            RawTransaction(
                date=date(2025, 1, 1),
                description='UPI-SALARY CREDIT FROM COMPANY ABC PVT LTD-GPAY',
                amount=Decimal('50000.0'),
                balance=Decimal('100000.0'),
                transaction_type='CREDIT'
            ),
            RawTransaction(
                date=date(2025, 1, 1),
                description='UPI-P2P-GROCERY STORE BIG BAZAAR-PHONEPE',
                amount=Decimal('-1500.0'),
                balance=Decimal('98500.0'),
                transaction_type='DEBIT'
            ),
            RawTransaction(
                date=date(2025, 1, 1),
                description='IMPS-ATM CASH WITHDRAWAL HDFC BANK',
                amount=Decimal('-2000.0'),
                balance=Decimal('96500.0'),
                transaction_type='DEBIT'
            ),
            RawTransaction(
                date=date(2025, 1, 1),
                description='UPI-ELECTRICITY BILL PAYMENT ONLINE-PAYTM',
                amount=Decimal('-800.0'),
                balance=Decimal('95700.0'),
                transaction_type='DEBIT'
            ),
            RawTransaction(
                date=date(2025, 1, 1),
                description='UPI-FUEL STATION PETROL PUMP-GPAY',
                amount=Decimal('-1200.0'),
                balance=Decimal('94500.0'),
                transaction_type='DEBIT'
            )
        ]
        
        print(f"\n🧪 Testing {len(test_transactions)} sample transactions:")
        print()
        
        results = []
        total_time = 0
        
        for i, transaction in enumerate(test_transactions, 1):
            start_time = time.time()

            # Convert RawTransaction to dictionary format expected by router
            transaction_dict = {
                'description': transaction.description,
                'amount': float(transaction.amount),
                'date': transaction.date.isoformat() if transaction.date else None
            }

            result = router.categorize_transaction(transaction_dict)
            processing_time = time.time() - start_time
            total_time += processing_time

            results.append(result)

            # Display result
            desc_short = transaction.description[:50] + "..." if len(transaction.description) > 50 else transaction.description
            status = "✅" if result.category != "Uncategorized" else "⚠️"

            print(f"{i}. {status} '{desc_short}'")
            print(f"   Category: {result.category}")
            print(f"   Sub-Category: {result.sub_category}")
            print(f"   Method: {result.method_used}")
            print(f"   Confidence: {result.confidence:.2f}")
            print(f"   Time: {processing_time*1000:.1f}ms")
            print()
        
        # Calculate statistics
        successful = sum(1 for r in results if r.category != "Uncategorized")
        avg_time = total_time / len(results)
        avg_confidence = sum(r.confidence for r in results) / len(results)
        
        print(f"📈 DEMO RESULTS:")
        print(f"   Success Rate: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
        print(f"   Average Time: {avg_time*1000:.1f}ms")
        print(f"   Average Confidence: {avg_confidence:.2f}")
        
        # Method usage breakdown
        method_counts = {}
        for result in results:
            method = result.method_used
            method_counts[method] = method_counts.get(method, 0) + 1
        
        print(f"   Method Usage:")
        for method, count in method_counts.items():
            print(f"     {method}: {count} ({count/len(results)*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_enhanced_categorizer():
    """
    Demonstrate the enhanced categorizer with GPU integration
    """
    print("\n🔧 ENHANCED CATEGORIZER DEMO")
    print("-" * 40)
    
    try:
        from bank_analyzer.core.categorizer import TransactionCategorizer
        from bank_analyzer.models.transaction import RawTransaction
        from datetime import datetime
        
        print("📦 Initializing enhanced categorizer...")
        categorizer = TransactionCategorizer(enable_gpu=True)
        
        # Check GPU availability
        gpu_available = categorizer.is_gpu_available()
        print(f"🔧 GPU Available: {'✅ Yes' if gpu_available else '❌ No (using fallback)'}")
        
        # Test categorization
        print("\n🧪 Testing enhanced categorization:")
        
        test_transaction = RawTransaction(
            date=datetime.now(),
            description="UPI-SALARY CREDIT FROM TECH COMPANY-GPAY",
            amount=75000.0,
            balance=100000.0
        )
        
        start_time = time.time()
        result = categorizer.categorize_transaction(test_transaction)
        processing_time = time.time() - start_time
        
        print(f"   Transaction: {test_transaction.description}")
        print(f"   Category: {result.category}")
        print(f"   Sub-Category: {result.sub_category}")
        print(f"   Confidence: {getattr(result, 'confidence', 'N/A')}")
        print(f"   Method: {getattr(result, 'categorization_method', 'Traditional')}")
        print(f"   Processing Time: {processing_time*1000:.1f}ms")
        
        # Get categorization statistics
        stats = categorizer.get_categorization_stats()
        print(f"\n📊 Categorization Statistics:")
        print(f"   Total Categorizations: {stats['total_categorizations']}")
        print(f"   GPU Usage: {stats['gpu_percentage']:.1f}%")
        print(f"   Rule Usage: {stats['rule_percentage']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced categorizer demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_system_architecture():
    """
    Demonstrate the system architecture and components
    """
    print("\n🏗️ SYSTEM ARCHITECTURE DEMO")
    print("-" * 40)
    
    print("📋 System Components:")
    
    components = [
        ("GPU Deep Learning Categorizer", "bank_analyzer.ml.gpu_deep_learning_categorizer"),
        ("Intelligent Category Router", "bank_analyzer.ml.intelligent_category_router"),
        ("GPU Training Pipeline", "bank_analyzer.ml.gpu_training_pipeline"),
        ("Integrated GPU Categorizer", "bank_analyzer.ml.integrated_gpu_categorizer"),
        ("Enhanced Core Categorizer", "bank_analyzer.core.categorizer")
    ]
    
    for name, module_path in components:
        try:
            __import__(module_path)
            print(f"   ✅ {name}")
        except ImportError as e:
            print(f"   ❌ {name}: {e}")
    
    print("\n🎯 Key Features:")
    features = [
        "✅ Intelligent hybrid architecture with manual label priority",
        "✅ GPU-accelerated deep learning with DistilBERT transformer",
        "✅ Smart category routing based on confidence and data availability",
        "✅ Real-time performance monitoring and optimization",
        "✅ Backward compatibility with existing manual labeling workflows",
        "✅ Comprehensive training pipeline with cross-validation",
        "✅ Automatic fallback to pattern matching for edge cases"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n📊 Performance Targets:")
    targets = [
        "🎯 95%+ prediction accuracy on real transaction data",
        "⚡ Sub-second categorization time (<100ms per transaction)",
        "🔧 Effective GPU utilization for training and inference",
        "🛡️ Graceful handling of edge cases and new transaction types",
        "📈 Continuous improvement through performance monitoring"
    ]
    
    for target in targets:
        print(f"   {target}")


def main():
    """
    Main demo function
    """
    print("🎉 Welcome to the GPU-Accelerated Categorization System Demo!")
    print()
    
    # Demo 1: System Architecture
    demo_system_architecture()
    
    # Demo 2: Intelligent Routing
    print("\n" + "="*60)
    routing_success = demo_intelligent_routing()
    
    # Demo 3: Enhanced Categorizer
    print("\n" + "="*60)
    categorizer_success = demo_enhanced_categorizer()
    
    # Final Summary
    print("\n" + "="*60)
    print("📋 DEMO SUMMARY")
    print("-" * 20)
    
    if routing_success and categorizer_success:
        print("🎉 All demos completed successfully!")
        print("✅ System is working correctly with intelligent routing")
        print("✅ Enhanced categorizer is functional")
        print("✅ Fallback mechanisms are operational")
    else:
        print("⚠️ Some demos encountered issues")
        print("💡 This is expected if GPU training hasn't completed yet")
        print("🔧 The system will improve once GPU model training finishes")
    
    print("\n💡 Next Steps:")
    print("1. Wait for GPU model training to complete (if running)")
    print("2. Test with your actual transaction data")
    print("3. Monitor performance and retrain as needed")
    print("4. Enjoy 95%+ categorization accuracy! 🎯")


if __name__ == "__main__":
    main()
