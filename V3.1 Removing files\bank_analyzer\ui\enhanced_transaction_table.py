"""
Enhanced transaction table with virtual scrolling, batch selection and pattern highlighting
Supports multi-row selection, similarity highlighting, and batch operations with optimized performance
"""

from PySide6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, QCheckBox, QWidget,
    QHBoxLayout, QVBoxLayout, QPushButton, QLabel, QProgressBar,
    QMessageBox, QMenu, QAbstractItemView, QGroupBox, QTextEdit,
    QComboBox, QDoubleSpinBox, QGridLayout, QScrollBar
)
from PySide6.QtCore import Qt, Signal, QTimer, QRect
from PySide6.QtGui import QColor, QBrush, QFont, QAction


class ResizableHeaderView(QHeaderView):
    """Custom header view with right-click context menu for column resizing and drag-to-resize support"""

    def __init__(self, orientation, parent=None):
        super().__init__(orientation, parent)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # Enable drag-to-resize functionality
        self.setSectionsMovable(False)  # Don't allow column reordering
        self.setSectionsClickable(True)  # Allow clicking on headers
        self.setHighlightSections(True)  # Highlight sections on hover
        self.setDefaultSectionSize(100)  # Default column width

    def show_context_menu(self, position):
        """Show context menu with resize options"""
        menu = QMenu(self)

        # Get the logical index of the column at the position
        logical_index = self.logicalIndexAt(position)
        if logical_index < 0:
            return

        # Column resize options
        resize_to_contents_action = QAction("📏 Resize to Contents", self)
        resize_to_contents_action.triggered.connect(lambda: self.resizeSection(logical_index, self.sectionSizeHint(logical_index)))
        menu.addAction(resize_to_contents_action)

        stretch_action = QAction("↔️ Stretch Column", self)
        stretch_action.triggered.connect(lambda: self.setSectionResizeMode(logical_index, QHeaderView.Stretch))
        menu.addAction(stretch_action)

        fixed_action = QAction("📌 Fixed Size", self)
        fixed_action.triggered.connect(lambda: self.setSectionResizeMode(logical_index, QHeaderView.Interactive))
        menu.addAction(fixed_action)

        menu.addSeparator()

        # All columns options
        resize_all_action = QAction("📏 Resize All to Contents", self)
        resize_all_action.triggered.connect(self.resize_all_to_contents)
        menu.addAction(resize_all_action)

        reset_action = QAction("🔄 Reset Column Sizes", self)
        reset_action.triggered.connect(self.reset_column_sizes)
        menu.addAction(reset_action)

        menu.exec(self.mapToGlobal(position))

    def resize_all_to_contents(self):
        """Resize all columns to their contents"""
        for i in range(self.count()):
            if i != 0:  # Skip checkbox column
                self.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def reset_column_sizes(self):
        """Reset column sizes to default"""
        # Set default resize modes
        if self.count() >= 9:  # Enhanced table with 9 columns
            self.setSectionResizeMode(0, QHeaderView.Fixed)      # Checkbox - keep fixed
            for i in range(1, self.count()):
                self.setSectionResizeMode(i, QHeaderView.Interactive)  # All others interactive
from typing import List, Dict, Any, Optional, Set
import time

from ..ml.data_preparation import UniqueTransaction
from ..core.logger import get_logger


def safe_table_item(value, max_length=200, placeholder="[No Data]"):
    """Create a safe QTableWidgetItem that prevents overflow errors"""
    try:
        # Convert to string and limit length
        if value is None:
            str_value = placeholder
        elif isinstance(value, str) and not value.strip():
            str_value = placeholder
        else:
            str_value = str(value)[:max_length]

        # Ensure we have a valid display string
        if not str_value or str_value.isspace():
            str_value = placeholder

        return QTableWidgetItem(str_value)
    except Exception as e:
        # Log the error for debugging
        logger = get_logger(__name__)
        logger.warning(f"Error creating table item for value '{value}': {str(e)}")
        return QTableWidgetItem(placeholder)


class SelectableTableWidget(QTableWidget):
    """
    Enhanced table widget with virtual scrolling, batch selection capabilities
    Supports checkboxes, similarity highlighting, and batch operations with optimized performance
    """

    # Signals
    selection_changed = Signal(list)  # Emits list of selected hash_ids
    similarity_requested = Signal(str)  # Emits hash_id for similarity search
    batch_label_requested = Signal(list)  # Emits list of selected transactions
    transaction_clicked = Signal(str)  # hash_id of clicked transaction

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)

        # Data storage
        self.transactions: List[UniqueTransaction] = []
        self.selected_hash_ids: Set[str] = set()
        self.highlighted_hash_ids: Set[str] = set()

        # Virtual scrolling properties
        self.visible_rows = 50  # Number of rows to render at once
        self.row_height = 25    # Estimated row height in pixels
        self.first_visible_row = 0
        self.last_visible_row = 0
        self._rendered_rows: Set[int] = set()
        self._row_widgets_cache: Dict[int, Dict[str, QWidget]] = {}

        # Performance tracking
        self._last_scroll_time = 0
        self._scroll_debounce_timer = QTimer()
        self._scroll_debounce_timer.setSingleShot(True)
        self._scroll_debounce_timer.timeout.connect(self._update_visible_rows)

        # Colors for highlighting
        self.colors = {
            'selected': QColor(173, 216, 230),      # Light blue
            'similar': QColor(255, 255, 224),       # Light yellow
            'labeled': QColor(144, 238, 144),       # Light green
            'pending': QColor(255, 218, 185),       # Light orange
            'unlabeled': QColor(255, 255, 255)      # White
        }

        self.setup_table()
        self.setup_context_menu()
        self.setup_virtual_scrolling()
    
    def setup_table(self):
        """Setup table structure and properties"""
        # Set columns
        headers = [
            "Select", "Description", "Frequency", "Amount Range", "Type",
            "Status", "Category", "Sub-category", "Similarity"
        ]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # Table properties
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        self.setWordWrap(True)
        
        # Set custom resizable header with context menu
        resizable_header = ResizableHeaderView(Qt.Horizontal, self)
        self.setHorizontalHeader(resizable_header)

        # Header properties - make all columns resizable
        resizable_header.setSectionResizeMode(0, QHeaderView.Fixed)      # Checkbox - keep fixed
        resizable_header.setSectionResizeMode(1, QHeaderView.Interactive)  # Description - resizable
        resizable_header.setSectionResizeMode(2, QHeaderView.Interactive)  # Frequency - resizable
        resizable_header.setSectionResizeMode(3, QHeaderView.Interactive)  # Amount - resizable
        resizable_header.setSectionResizeMode(4, QHeaderView.Interactive)  # Type - resizable
        resizable_header.setSectionResizeMode(5, QHeaderView.Interactive)  # Status - resizable
        resizable_header.setSectionResizeMode(6, QHeaderView.Interactive)  # Category - resizable
        resizable_header.setSectionResizeMode(7, QHeaderView.Interactive)  # Sub-category - resizable
        resizable_header.setSectionResizeMode(8, QHeaderView.Interactive)  # Similarity - resizable

        # Set initial column widths
        self.setColumnWidth(0, 60)   # Checkbox
        self.setColumnWidth(1, 300)  # Description
        self.setColumnWidth(2, 80)   # Frequency
        self.setColumnWidth(3, 120)  # Amount
        self.setColumnWidth(4, 100)  # Type - increased from 80 to 100 to show full "Credit"/"Debit"
        self.setColumnWidth(5, 80)   # Status
        self.setColumnWidth(6, 120)  # Category
        self.setColumnWidth(7, 120)  # Sub-category
        self.setColumnWidth(8, 80)   # Similarity
        
        # Enable custom context menu
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # Connect row click to transaction selection
        self.itemClicked.connect(self.on_item_clicked)

    def setup_virtual_scrolling(self):
        """Setup virtual scrolling for performance optimization"""
        # Connect scroll events
        self.verticalScrollBar().valueChanged.connect(self._on_scroll)

        # Set uniform row heights for better performance
        self.verticalHeader().setDefaultSectionSize(self.row_height)
        self.verticalHeader().setSectionResizeMode(QHeaderView.Fixed)

        # Enable smooth scrolling
        self.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)

    def setup_context_menu(self):
        """Setup context menu for right-click actions"""
        self.context_menu = QMenu(self)
        
        # Select similar action
        self.select_similar_action = QAction("Select Similar Transactions", self)
        self.select_similar_action.triggered.connect(self.select_similar_transactions)
        self.context_menu.addAction(self.select_similar_action)
        
        # Highlight similar action
        self.highlight_similar_action = QAction("Highlight Similar Transactions", self)
        self.highlight_similar_action.triggered.connect(self.highlight_similar_transactions)
        self.context_menu.addAction(self.highlight_similar_action)
        
        self.context_menu.addSeparator()
        
        # Batch labeling action
        self.batch_label_action = QAction("Batch Label Selected", self)
        self.batch_label_action.triggered.connect(self.request_batch_labeling)
        self.context_menu.addAction(self.batch_label_action)
        
        self.context_menu.addSeparator()
        
        # Selection actions
        self.select_all_action = QAction("Select All", self)
        self.select_all_action.triggered.connect(self.select_all_transactions)
        self.context_menu.addAction(self.select_all_action)
        
        self.clear_selection_action = QAction("Clear Selection", self)
        self.clear_selection_action.triggered.connect(self.clear_all_selections)
        self.context_menu.addAction(self.clear_selection_action)
    
    def populate_table(self, transactions: List[UniqueTransaction]):
        """Populate table with transaction data using virtual scrolling"""

        # Filter out invalid transactions before setting up the table
        valid_transactions = []
        invalid_count = 0

        for txn in transactions:
            if self._is_valid_transaction(txn):
                valid_transactions.append(txn)
            else:
                invalid_count += 1
                self.logger.debug(f"Filtering out invalid transaction: {txn}")

        # Store only valid transactions
        self.transactions = valid_transactions

        if invalid_count > 0:
            self.logger.info(f"Filtered out {invalid_count} invalid transactions, showing {len(valid_transactions)} valid transactions")

        # Clear previous rendering state
        self._rendered_rows.clear()
        self._row_widgets_cache.clear()

        # Set row count to match valid transactions only - do this after clearing state
        self.setRowCount(len(valid_transactions))

        # Only proceed if we have valid transactions
        if len(valid_transactions) > 0:
            # Calculate visible range and render only visible rows
            self._update_visible_rows()

            # Update selection state for visible rows only
            self.update_selection_display()

            # Force table refresh to ensure proper display
            self.viewport().update()
        else:
            # No transactions to display
            self.logger.info("No valid transactions to display")

        # Validate table state for debugging (only log if there are actual issues)
        validation = self.validate_table_state()
        if not validation['is_valid'] and len(valid_transactions) > 0:
            self.logger.warning(f"Table validation failed: {validation}")
        elif validation['is_valid']:
            self.logger.debug(f"Table validation passed: {len(valid_transactions)} transactions displayed")

    def refresh_transaction_data(self, transactions: List[UniqueTransaction]):
        """Refresh the table with new transaction data"""
        # Clear all existing data
        self.clear()
        self._rendered_rows.clear()
        self._row_widgets_cache.clear()
        self.selected_hash_ids.clear()
        self.highlighted_hash_ids.clear()

        # Repopulate with new data
        self.populate_table(transactions)

    def validate_table_state(self):
        """Validate table state and log any issues"""
        try:
            row_count = self.rowCount()
            data_count = len(self.transactions)

            self.logger.debug(f"Table validation: {row_count} rows, {data_count} transactions")

            # For virtual scrolling, only check rendered rows for content
            # Empty rows are expected in virtual scrolling when they're not in the visible range
            empty_rendered_rows = []

            # Only check rows that should be rendered (visible range)
            if hasattr(self, 'first_visible_row') and hasattr(self, 'last_visible_row'):
                start_row = max(0, self.first_visible_row)
                end_row = min(row_count - 1, self.last_visible_row)

                for row in range(start_row, end_row + 1):
                    if row in self._rendered_rows:  # Only check rows that should be rendered
                        has_content = False
                        for col in range(self.columnCount()):
                            item = self.item(row, col)
                            widget = self.cellWidget(row, col)
                            if (item and item.text().strip()) or widget:
                                has_content = True
                                break

                        if not has_content and row < data_count:  # Only flag if we have data for this row
                            empty_rendered_rows.append(row)

            # Only log warnings for actually problematic empty rows
            if empty_rendered_rows:
                self.logger.warning(f"Found {len(empty_rendered_rows)} empty rendered rows: {empty_rendered_rows[:10]}")

            # Table is valid if row count matches data count and no rendered rows are unexpectedly empty
            is_valid = (row_count == data_count) and (len(empty_rendered_rows) == 0)

            return {
                'row_count': row_count,
                'data_count': data_count,
                'empty_rendered_rows': empty_rendered_rows,
                'is_valid': is_valid
            }

        except Exception as e:
            self.logger.error(f"Error validating table state: {str(e)}")
            return {'is_valid': False, 'error': str(e)}


    
    def populate_row(self, row: int, transaction: UniqueTransaction):
        """Populate a single row with transaction data"""
        # Safety check: ensure transaction is not None
        if not transaction:
            self.logger.warning(f"Attempted to populate row {row} with None transaction")
            return

        # Additional validation to prevent attribute errors
        if not hasattr(transaction, 'description') or not hasattr(transaction, 'hash_id'):
            self.logger.warning(f"Transaction at row {row} missing required attributes")
            return

        # Checkbox for selection
        checkbox_widget = QWidget()
        checkbox_layout = QHBoxLayout(checkbox_widget)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)
        checkbox_layout.setAlignment(Qt.AlignCenter)

        checkbox = QCheckBox()
        is_selected = transaction.hash_id in self.selected_hash_ids
        checkbox.setChecked(is_selected)

        # Store the hash_id as a property of the checkbox for easy access
        checkbox._hash_id = transaction.hash_id

        # Connect checkbox to persistent handler
        checkbox.stateChanged.connect(self._handle_checkbox_state_change)





        checkbox_layout.addWidget(checkbox)

        self.setCellWidget(row, 0, checkbox_widget)
        
        # Description with tooltip - ensure we have valid data
        description = transaction.description or "[No Description]"
        normalized_desc = getattr(transaction, 'normalized_description', '') or description

        desc_item = safe_table_item(description, placeholder="[No Description]")
        desc_item.setToolTip(f"Full: {description}\nNormalized: {normalized_desc}")
        desc_item.setData(Qt.UserRole, transaction.hash_id)  # Store hash_id
        self.setItem(row, 1, desc_item)

        # Frequency
        freq_item = safe_table_item(transaction.frequency)
        freq_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 2, freq_item)
        
        # Amount range
        try:
            min_amt, max_amt = transaction.amount_range
            # Limit amounts to prevent overflow
            min_amt = min(999999999, max(0, float(min_amt))) if min_amt is not None else 0
            max_amt = min(999999999, max(0, float(max_amt))) if max_amt is not None else 0

            amount_text = f"₹{min_amt:.0f}"
            if min_amt != max_amt:
                amount_text += f" - ₹{max_amt:.0f}"
        except (ValueError, TypeError):
            amount_text = "₹0"

        amount_item = safe_table_item(amount_text)
        amount_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 3, amount_item)

        # Transaction Type
        type_text = self._get_transaction_type_display(transaction)
        type_item = QTableWidgetItem(type_text)
        type_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 4, type_item)

        # Status
        if transaction.is_manually_labeled:
            status = "Labeled"
            status_color = self.colors['labeled']
        elif transaction.category:
            status = "Pending"
            status_color = self.colors['pending']
        else:
            status = "Unlabeled"
            status_color = self.colors['unlabeled']

        status_item = safe_table_item(status)
        status_item.setBackground(QBrush(status_color))
        status_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 5, status_item)

        # Category
        category_text = str(transaction.category) if transaction.category is not None else ""
        category_item = safe_table_item(category_text)
        self.setItem(row, 6, category_item)

        # Sub-category
        subcategory_text = str(transaction.sub_category) if transaction.sub_category is not None else ""
        subcategory_item = safe_table_item(subcategory_text)
        self.setItem(row, 7, subcategory_item)

        # Similarity (initially empty)
        similarity_item = safe_table_item("")
        similarity_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 8, similarity_item)
        
        # Apply row highlighting
        self.update_row_highlighting(row, transaction.hash_id)

        # Mark row as rendered
        self._rendered_rows.add(row)

    def _create_error_row(self, row: int, error_message: str):
        """Create a row showing an error message"""
        try:
            # Empty checkbox
            checkbox_widget = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            checkbox = QCheckBox()
            checkbox.setEnabled(False)
            checkbox_layout.addWidget(checkbox)
            self.setCellWidget(row, 0, checkbox_widget)

            # Error message in description column
            error_item = QTableWidgetItem(error_message)
            error_item.setBackground(QBrush(QColor(255, 200, 200)))  # Light red background
            self.setItem(row, 1, error_item)

            # Fill remaining columns with empty items
            for col in range(2, self.columnCount()):
                empty_item = QTableWidgetItem("")
                empty_item.setBackground(QBrush(QColor(255, 200, 200)))
                self.setItem(row, col, empty_item)

        except Exception as e:
            self.logger.error(f"Failed to create error row: {str(e)}")

    def _get_transaction_type_display(self, transaction: UniqueTransaction) -> str:
        """Get display text for transaction type"""
        if not hasattr(transaction, 'transaction_types') or not transaction.transaction_types:
            print(f"DEBUG: Transaction has no transaction_types: {transaction.description[:30]}...")
            return "Unknown"

        if hasattr(transaction, 'debit_frequency') and hasattr(transaction, 'credit_frequency'):
            debit_freq = transaction.debit_frequency
            credit_freq = transaction.credit_frequency

            print(f"DEBUG: Transaction type display - D:{debit_freq}, C:{credit_freq} for '{transaction.description[:30]}...'")

            if debit_freq > 0 and credit_freq == 0:
                result = "Debit"
                print(f"DEBUG: Returning '{result}' for transaction")
                return result
            elif credit_freq > 0 and debit_freq == 0:
                result = "Credit"
                print(f"DEBUG: Returning '{result}' for transaction")
                return result
            elif debit_freq > credit_freq:
                result = f"Mixed (D:{debit_freq}/C:{credit_freq})"
                print(f"DEBUG: Returning '{result}' for transaction")
                return result
            elif credit_freq > debit_freq:
                result = f"Mixed (C:{credit_freq}/D:{debit_freq})"
                print(f"DEBUG: Returning '{result}' for transaction")
                return result
            else:
                result = f"Mixed ({debit_freq}/{credit_freq})"
                print(f"DEBUG: Returning '{result}' for transaction")
                return result

        # Fallback to transaction_types set
        types = list(transaction.transaction_types)
        if len(types) == 1:
            return types[0].capitalize()
        else:
            return "Mixed"

    def on_item_clicked(self, item):
        """Handle item click to show transaction details"""
        if item is None:
            return

        row = item.row()
        # Get hash_id from description column (column 1)
        desc_item = self.item(row, 1)
        if desc_item:
            hash_id = desc_item.data(Qt.UserRole)
            if hash_id:
                self.transaction_clicked.emit(hash_id)

    def _handle_checkbox_state_change(self, state):
        """Handle checkbox state changes and update selection.

        This method scans all visible checkboxes to determine the current selection
        and synchronizes both the internal selection state and external signals.
        """
        try:
            # Get the checkbox that sent the signal
            sender_checkbox = self.sender()
            if not sender_checkbox or not hasattr(sender_checkbox, '_hash_id'):
                self.logger.warning("Checkbox state change from unknown sender")
                return

            hash_id = sender_checkbox._hash_id

            # Get all currently checked checkboxes and emit their hash_ids directly
            checked_hash_ids = []

            for row in range(self.rowCount()):
                checkbox_widget = self.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        # Get hash_id for this row
                        desc_item = self.item(row, 1)  # Description column
                        if desc_item:
                            row_hash_id = desc_item.data(Qt.UserRole)
                            if row_hash_id:
                                checked_hash_ids.append(row_hash_id)

            # Update the internal selected_hash_ids that _refresh_button_states() relies on
            self.selected_hash_ids = set(checked_hash_ids)

            # Emit the selection changed signal
            self.selection_changed.emit(checked_hash_ids)

        except Exception as e:
            self.logger.error(f"Error in _handle_checkbox_state_change: {str(e)}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")


    
    def update_row_highlighting(self, row: int, hash_id: str):
        """Update row highlighting based on selection and similarity"""
        # Determine background color
        if hash_id in self.selected_hash_ids:
            bg_color = self.colors['selected']
        elif hash_id in self.highlighted_hash_ids:
            bg_color = self.colors['similar']
        else:
            # Use status-based color for status column only
            return
        
        # Apply background to all columns except status
        for col in range(self.columnCount()):
            if col != 5:  # Skip status column (now column 5)
                item = self.item(row, col)
                if item:
                    item.setBackground(QBrush(bg_color))
    
    def update_selection_display(self):
        """Update the display of selections for visible rows only"""
        # Only update visible rows for performance
        start_row = max(0, self.first_visible_row)
        end_row = min(self.rowCount(), self.last_visible_row + 1)

        for row in range(start_row, end_row):
            if row in self._rendered_rows:
                desc_item = self.item(row, 1)
                if desc_item:
                    hash_id = desc_item.data(Qt.UserRole)
                    if hash_id:
                        # Update checkbox
                        checkbox_widget = self.cellWidget(row, 0)
                        if checkbox_widget:
                            checkbox = checkbox_widget.findChild(QCheckBox)
                            if checkbox:
                                # Temporarily disconnect to avoid triggering signal during update
                                checkbox.blockSignals(True)
                                checkbox.setChecked(hash_id in self.selected_hash_ids)


                                checkbox.blockSignals(False)

                        # Update highlighting
                        self.update_row_highlighting(row, hash_id)
    
    def get_row_by_hash_id(self, hash_id: str) -> int:
        """Get row number by hash ID"""
        for row in range(self.rowCount()):
            desc_item = self.item(row, 1)
            if desc_item and desc_item.data(Qt.UserRole) == hash_id:
                return row
        return -1
    
    def show_context_menu(self, position):
        """Show context menu at position"""
        if self.itemAt(position):
            # Update action states
            has_selection = len(self.selected_hash_ids) > 0
            self.batch_label_action.setEnabled(has_selection)
            
            current_row = self.rowAt(position.y())
            has_current = current_row >= 0
            self.select_similar_action.setEnabled(has_current)
            self.highlight_similar_action.setEnabled(has_current)
            
            self.context_menu.exec(self.mapToGlobal(position))
    
    def select_similar_transactions(self):
        """Request selection of similar transactions"""
        current_row = self.currentRow()
        if current_row >= 0:
            desc_item = self.item(current_row, 1)
            if desc_item:
                hash_id = desc_item.data(Qt.UserRole)
                if hash_id:
                    self.similarity_requested.emit(hash_id)
    
    def highlight_similar_transactions(self):
        """Request highlighting of similar transactions"""
        current_row = self.currentRow()
        if current_row >= 0:
            desc_item = self.item(current_row, 1)
            if desc_item:
                hash_id = desc_item.data(Qt.UserRole)
                if hash_id:
                    # This will be handled by the parent window
                    self.similarity_requested.emit(hash_id)
    
    def request_batch_labeling(self):
        """Request batch labeling for selected transactions"""
        if self.selected_hash_ids:
            selected_transactions = [
                txn for txn in self.transactions 
                if txn.hash_id in self.selected_hash_ids
            ]
            self.batch_label_requested.emit(selected_transactions)
    
    def select_all_transactions(self):
        """Select all transactions"""
        for txn in self.transactions:
            self.selected_hash_ids.add(txn.hash_id)
        self.update_selection_display()
        self.selection_changed.emit(list(self.selected_hash_ids))
    
    def clear_all_selections(self):
        """Clear all selections"""
        self.selected_hash_ids.clear()
        self.update_selection_display()
        self.selection_changed.emit([])
    
    def set_similar_transactions(self, similar_hash_ids: List[str], similarities: Dict[str, float]):
        """Set similar transactions for highlighting"""
        self.highlighted_hash_ids = set(similar_hash_ids)
        
        # Update similarity scores in table
        for row in range(self.rowCount()):
            desc_item = self.item(row, 1)
            if desc_item:
                hash_id = desc_item.data(Qt.UserRole)
                if hash_id in similarities:
                    similarity_item = self.item(row, 8)  # Updated column index
                    if similarity_item:
                        similarity_score = similarities[hash_id]
                        similarity_item.setText(f"{similarity_score:.2f}")
                else:
                    similarity_item = self.item(row, 8)  # Updated column index
                    if similarity_item:
                        similarity_item.setText("")
        
        # Update highlighting
        self.update_selection_display()
    
    def clear_similarity_highlighting(self):
        """Clear similarity highlighting"""
        self.highlighted_hash_ids.clear()
        
        # Clear similarity scores
        for row in range(self.rowCount()):
            similarity_item = self.item(row, 8)  # Updated column index
            if similarity_item:
                similarity_item.setText("")
        
        self.update_selection_display()
    
    def select_similar_transactions_by_hash_ids(self, hash_ids: List[str]):
        """Select transactions by their hash IDs"""
        for hash_id in hash_ids:
            self.selected_hash_ids.add(hash_id)
        
        self.update_selection_display()
        self.selection_changed.emit(list(self.selected_hash_ids))
    
    def get_selected_transactions(self) -> List[UniqueTransaction]:
        """Get currently selected transactions"""
        try:
            selected_transactions = [
                txn for txn in self.transactions
                if txn.hash_id in self.selected_hash_ids
            ]
            return selected_transactions
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in get_selected_transactions: {str(e)}")
            return []
    
    def get_selected_count(self) -> int:
        """Get count of selected transactions"""
        return len(self.selected_hash_ids)


    
    def update_transaction_status(self, hash_id: str, category: str, sub_category: str):
        """Update transaction status after labeling"""
        row = self.get_row_by_hash_id(hash_id)
        if row >= 0:
            # Update status
            status_item = self.item(row, 5)  # Updated column index
            if status_item:
                status_item.setText("Labeled")
                status_item.setBackground(QBrush(self.colors['labeled']))

            # Update category
            category_item = self.item(row, 6)  # Updated column index
            if category_item:
                category_item.setText(category)

            # Update sub-category
            subcategory_item = self.item(row, 7)  # Updated column index
            if subcategory_item:
                subcategory_item.setText(sub_category)

            # Update transaction object - CRITICAL FIX
            for txn in self.transactions:
                if txn.hash_id == hash_id:
                    txn.category = category
                    txn.sub_category = sub_category
                    txn.is_manually_labeled = True
                    # Force update confidence and labeling metadata
                    txn.confidence = 1.0
                    txn.labeled_by = "user"
                    from datetime import datetime
                    txn.labeled_at = datetime.now()
                    break

    def refresh_transaction_data(self, updated_transactions: List['UniqueTransaction']):
        """Refresh the table with updated transaction data"""
        # Update the internal transactions list
        self.transactions = updated_transactions

        # Repopulate the table to ensure consistency
        self.populate_table(self.transactions)

        # Restore selections
        self.update_selection_display()

    # Virtual scrolling methods
    def _on_scroll(self, value):
        """Handle scroll events with debouncing"""
        current_time = time.time()
        self._last_scroll_time = current_time

        # Debounce scroll events to prevent excessive updates
        self._scroll_debounce_timer.start(50)  # 50ms debounce

    def _update_visible_rows(self):
        """Update which rows should be visible and render them"""
        if not self.transactions:
            # Initialize empty ranges for empty table
            self.first_visible_row = 0
            self.last_visible_row = -1
            return

        # Calculate visible range based on scroll position
        viewport_height = self.viewport().height()
        scroll_value = self.verticalScrollBar().value()

        # Ensure we have a valid row height
        if self.row_height <= 0:
            self.row_height = 30  # Default row height

        # Calculate first and last visible rows
        self.first_visible_row = max(0, scroll_value // self.row_height - 5)  # 5 row buffer
        visible_row_count = (viewport_height // self.row_height) + 10  # 10 row buffer
        self.last_visible_row = min(len(self.transactions) - 1,
                                   self.first_visible_row + visible_row_count)

        # Ensure valid range
        if self.last_visible_row < self.first_visible_row:
            self.last_visible_row = self.first_visible_row

        # Render visible rows
        self._render_visible_rows()

    def _render_visible_rows(self):
        """Render only the visible rows for performance"""
        start_time = time.time()

        # Clear rows that are no longer visible
        rows_to_clear = [row for row in self._rendered_rows
                        if row < self.first_visible_row or row > self.last_visible_row]

        for row in rows_to_clear:
            self._clear_row(row)
            self._rendered_rows.discard(row)

        # Render new visible rows (transactions are already validated)
        for row in range(self.first_visible_row, self.last_visible_row + 1):
            if row not in self._rendered_rows and row < len(self.transactions):
                transaction = self.transactions[row]
                self.populate_row(row, transaction)

        render_time = time.time() - start_time
        if render_time > 0.1:  # Log if rendering takes more than 100ms
            self.logger.debug(f"Row rendering took {render_time:.3f}s for rows {self.first_visible_row}-{self.last_visible_row}")

    def _is_valid_transaction(self, transaction: UniqueTransaction) -> bool:
        """Validate transaction data before rendering"""
        if not transaction:
            return False

        # Check for essential fields
        if not hasattr(transaction, 'description') or not transaction.description:
            return False

        if not hasattr(transaction, 'hash_id') or not transaction.hash_id:
            return False

        return True

    def _clear_empty_rows(self):
        """Clear any empty rows that might be displayed beyond our data"""
        try:
            current_row_count = self.rowCount()
            data_row_count = len(self.transactions)

            # If table has more rows than data, clear the extra rows
            if current_row_count > data_row_count:
                self.logger.debug(f"Clearing {current_row_count - data_row_count} empty rows")

                # Clear extra rows
                for row in range(data_row_count, current_row_count):
                    self._clear_row(row)

                # Adjust row count to match data
                self.setRowCount(data_row_count)

        except Exception as e:
            self.logger.error(f"Error clearing empty rows: {str(e)}")

    def _clear_row(self, row: int):
        """Clear a row to free memory"""
        try:
            # Clear cell widgets
            for col in range(self.columnCount()):
                self.setCellWidget(row, col, None)
                self.setItem(row, col, None)

            # Clear cached widgets
            if row in self._row_widgets_cache:
                del self._row_widgets_cache[row]

        except Exception as e:
            pass  # Error clearing row

    def resizeEvent(self, event):
        """Handle resize events to update visible rows"""
        super().resizeEvent(event)
        if hasattr(self, 'transactions') and self.transactions:
            self._update_visible_rows()


class BatchLabelingDialog(QWidget):
    """
    Dialog for batch labeling multiple transactions
    Shows preview, suggestions, and confirmation
    """

    # Signals
    batch_confirmed = Signal(list, str, str, float)  # transactions, category, sub_category, confidence

    def __init__(self, transactions: List[UniqueTransaction], suggestions: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.transactions = transactions
        self.suggestions = suggestions

        self.setWindowTitle("Batch Labeling")
        self.setWindowFlags(self.windowFlags() | Qt.Dialog)
        self.resize(600, 500)

        self.setup_ui()

    def setup_ui(self):
        """Setup batch labeling dialog UI"""
        layout = QVBoxLayout(self)

        # Title and summary
        title_label = QLabel(f"Batch Label {len(self.transactions)} Transactions")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(14)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Transaction preview
        preview_group = self.create_transaction_preview()
        layout.addWidget(preview_group)

        # Suggestions
        if self.suggestions.get('suggestions'):
            suggestions_group = self.create_suggestions_section()
            layout.addWidget(suggestions_group)

        # Labeling controls
        labeling_group = self.create_labeling_controls()
        layout.addWidget(labeling_group)

        # Buttons
        button_layout = self.create_buttons()
        layout.addLayout(button_layout)

    def create_transaction_preview(self):
        """Create transaction preview section"""
        from PySide6.QtWidgets import QGroupBox, QTextEdit

        group = QGroupBox("Transaction Preview")
        layout = QVBoxLayout(group)

        # Show first few transactions
        preview_text = QTextEdit()
        preview_text.setMaximumHeight(150)
        preview_text.setReadOnly(True)

        preview_content = []
        for i, txn in enumerate(self.transactions[:5]):
            if txn and hasattr(txn, 'description') and hasattr(txn, 'frequency'):
                description = txn.description or "[No Description]"
                frequency = txn.frequency or "Unknown"
                preview_content.append(f"{i+1}. {description} (Freq: {frequency})")
            else:
                preview_content.append(f"{i+1}. [Invalid Transaction Data]")

        if len(self.transactions) > 5:
            preview_content.append(f"... and {len(self.transactions) - 5} more transactions")

        preview_text.setPlainText("\n".join(preview_content))
        layout.addWidget(preview_text)

        return group

    def create_suggestions_section(self):
        """Create suggestions section"""
        from PySide6.QtWidgets import QGroupBox, QListWidget, QListWidgetItem

        group = QGroupBox("Suggested Labels")
        layout = QVBoxLayout(group)

        suggestions_list = QListWidget()
        suggestions_list.setMaximumHeight(100)

        for suggestion in self.suggestions['suggestions'][:3]:
            item_text = f"{suggestion['category']} / {suggestion['sub_category']} "
            item_text += f"(Support: {suggestion['supporting_transactions']})"

            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, suggestion)
            suggestions_list.addItem(item)

        suggestions_list.itemClicked.connect(self.on_suggestion_clicked)
        layout.addWidget(suggestions_list)

        self.suggestions_list = suggestions_list

        return group

    def create_labeling_controls(self):
        """Create labeling controls"""
        from PySide6.QtWidgets import QGroupBox, QGridLayout, QComboBox, QDoubleSpinBox

        group = QGroupBox("Label Assignment")
        layout = QGridLayout(group)

        # Category selection
        layout.addWidget(QLabel("Category:"), 0, 0)
        self.category_combo = QComboBox()
        self.category_combo.addItem("Select Category...")
        layout.addWidget(self.category_combo, 0, 1)

        # Sub-category selection
        layout.addWidget(QLabel("Sub-category:"), 1, 0)
        self.subcategory_combo = QComboBox()
        self.subcategory_combo.addItem("Select Sub-category...")
        layout.addWidget(self.subcategory_combo, 1, 1)

        # Confidence
        layout.addWidget(QLabel("Confidence:"), 2, 0)
        self.confidence_spin = QDoubleSpinBox()
        self.confidence_spin.setRange(0.1, 1.0)
        self.confidence_spin.setSingleStep(0.1)
        self.confidence_spin.setValue(0.9)
        self.confidence_spin.setSuffix("%")
        layout.addWidget(self.confidence_spin, 2, 1)

        return group

    def create_buttons(self):
        """Create dialog buttons"""
        layout = QHBoxLayout()

        # Cancel button
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.close)
        layout.addWidget(cancel_button)

        layout.addStretch()

        # Apply button
        apply_button = QPushButton("Apply Labels")
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #007ACC;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
        """)
        apply_button.clicked.connect(self.apply_labels)
        layout.addWidget(apply_button)

        return layout

    def on_suggestion_clicked(self, item):
        """Handle suggestion selection"""
        suggestion = item.data(Qt.UserRole)
        if suggestion:
            # Set category and subcategory
            category = suggestion['category']
            sub_category = suggestion['sub_category']

            # Find and select in combo boxes
            category_index = self.category_combo.findText(category)
            if category_index >= 0:
                self.category_combo.setCurrentIndex(category_index)

            subcategory_index = self.subcategory_combo.findText(sub_category)
            if subcategory_index >= 0:
                self.subcategory_combo.setCurrentIndex(subcategory_index)

    def apply_labels(self):
        """Apply labels to all selected transactions"""
        category = self.category_combo.currentText()
        sub_category = self.subcategory_combo.currentText()
        confidence = self.confidence_spin.value() / 100.0

        if category == "Select Category..." or sub_category == "Select Sub-category...":
            QMessageBox.warning(self, "Warning", "Please select both category and sub-category")
            return

        # Confirm batch operation
        reply = QMessageBox.question(
            self, "Confirm Batch Labeling",
            f"Apply label '{category} / {sub_category}' to {len(self.transactions)} transactions?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.batch_confirmed.emit(self.transactions, category, sub_category, confidence)
            self.close()

    def load_categories(self, categories: list, subcategories: dict):
        """Load available categories and subcategories"""
        self.category_combo.clear()
        self.category_combo.addItem("Select Category...")

        for category in categories:
            self.category_combo.addItem(category)

        self.subcategory_combo.clear()
        self.subcategory_combo.addItem("Select Sub-category...")

        # Add all subcategories (flattened)
        all_subcategories = set()
        for subcat_list in subcategories.values():
            all_subcategories.update(subcat_list)

        for subcategory in sorted(all_subcategories):
            self.subcategory_combo.addItem(subcategory)
