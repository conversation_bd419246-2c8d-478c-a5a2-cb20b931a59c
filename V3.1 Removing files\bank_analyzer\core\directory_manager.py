#!/usr/bin/env python3
"""
Directory Manager - Manages organized directory structure for bank statement analyzer
Separates raw/processed statement data from categorized/labeled output data
"""

from pathlib import Path
from datetime import datetime
from typing import Dict, Optional
import json

from .logger import get_logger


class DirectoryManager:
    """
    Manages organized directory structure for the bank statement analyzer
    
    Directory Structure:
    - data/
      - raw_statements/          # Original statement files (PDF, CSV, etc.)
      - processed_statements/    # Processed transaction data (CSV format)
      - categorized_data/        # Categorized/labeled transaction data
        - ml_categorized/        # ML model categorized data
        - ai_categorized/        # AI categorized data
        - manually_categorized/  # Manually categorized data
        - hybrid_categorized/    # Final hybrid categorized data
      - exports/                 # Final exported data for external use
      - backups/                 # Backup files
      - temp/                    # Temporary files
    """
    
    def __init__(self, base_dir: str = "data"):
        self.logger = get_logger(__name__)
        self.base_dir = Path(base_dir)
        
        # Define directory structure
        self.directories = {
            'base': self.base_dir,
            'raw_statements': self.base_dir / "raw_statements",
            'processed_statements': self.base_dir / "processed_statements", 
            'categorized_data': self.base_dir / "categorized_data",
            'ml_categorized': self.base_dir / "categorized_data" / "ml_categorized",
            'ai_categorized': self.base_dir / "categorized_data" / "ai_categorized",
            'manually_categorized': self.base_dir / "categorized_data" / "manually_categorized",
            'hybrid_categorized': self.base_dir / "categorized_data" / "hybrid_categorized",
            'exports': self.base_dir / "exports",
            'backups': self.base_dir / "backups",
            'temp': self.base_dir / "temp"
        }
        
        # Create all directories
        self._create_directories()
        self._create_readme_files()
        
        self.logger.info("Directory Manager initialized with organized structure")
    
    def _create_directories(self):
        """Create all required directories"""
        for name, path in self.directories.items():
            try:
                path.mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"Created/verified directory: {path}")
            except Exception as e:
                self.logger.error(f"Failed to create directory {path}: {str(e)}")
                raise
    
    def _create_readme_files(self):
        """Create README files in each directory to explain their purpose"""
        readme_content = {
            'raw_statements': "Original bank statement files (PDF, CSV, Excel, etc.)\nThese are the unprocessed files as received from banks.",
            'processed_statements': "Processed transaction data in standardized CSV format\nGenerated from parsing raw statement files.",
            'categorized_data': "Transaction data with categories assigned through various methods",
            'ml_categorized': "Transactions categorized using trained ML models",
            'ai_categorized': "Transactions categorized using AI services",
            'manually_categorized': "Transactions categorized through manual review",
            'hybrid_categorized': "Final categorized data combining ML, AI, and manual categorization",
            'exports': "Final exported data for external use (reports, analysis, etc.)",
            'backups': "Backup files for data recovery",
            'temp': "Temporary files (automatically cleaned up)"
        }
        
        for dir_name, content in readme_content.items():
            if dir_name in self.directories:
                readme_path = self.directories[dir_name] / "README.txt"
                if not readme_path.exists():
                    try:
                        readme_path.write_text(content, encoding='utf-8')
                    except Exception as e:
                        self.logger.warning(f"Could not create README in {dir_name}: {str(e)}")
    
    def get_directory(self, dir_type: str) -> Path:
        """
        Get directory path by type
        
        Args:
            dir_type: Directory type (e.g., 'processed_statements', 'hybrid_categorized')
            
        Returns:
            Path object for the directory
        """
        if dir_type not in self.directories:
            raise ValueError(f"Unknown directory type: {dir_type}. Available: {list(self.directories.keys())}")
        
        return self.directories[dir_type]
    
    def get_processed_statements_file(self, bank_name: str, timestamp: Optional[str] = None) -> Path:
        """
        Get file path for processed statements
        
        Args:
            bank_name: Name of the bank
            timestamp: Optional timestamp, if None uses current time
            
        Returns:
            Path for the processed statements file
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        filename = f"transactions_{bank_name.replace(' ', '_')}_{timestamp}.csv"
        return self.directories['processed_statements'] / filename
    
    def get_categorized_file(self, categorization_type: str, bank_name: str, 
                           timestamp: Optional[str] = None) -> Path:
        """
        Get file path for categorized data
        
        Args:
            categorization_type: Type of categorization ('ml', 'ai', 'manual', 'hybrid')
            bank_name: Name of the bank
            timestamp: Optional timestamp, if None uses current time
            
        Returns:
            Path for the categorized data file
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        dir_key = f"{categorization_type}_categorized"
        if dir_key not in self.directories:
            raise ValueError(f"Unknown categorization type: {categorization_type}")
        
        filename = f"{categorization_type}_categorized_{bank_name.replace(' ', '_')}_{timestamp}.csv"
        return self.directories[dir_key] / filename
    
    def get_export_file(self, export_type: str, timestamp: Optional[str] = None) -> Path:
        """
        Get file path for exports
        
        Args:
            export_type: Type of export (e.g., 'monthly_report', 'category_analysis')
            timestamp: Optional timestamp, if None uses current time
            
        Returns:
            Path for the export file
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        filename = f"{export_type}_{timestamp}.csv"
        return self.directories['exports'] / filename
    
    def get_temp_file(self, filename: str) -> Path:
        """
        Get path for temporary file
        
        Args:
            filename: Name of the temporary file
            
        Returns:
            Path for the temporary file
        """
        return self.directories['temp'] / filename
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        Clean up temporary files older than specified hours
        
        Args:
            max_age_hours: Maximum age in hours for temp files
        """
        try:
            temp_dir = self.directories['temp']
            current_time = datetime.now()
            
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_age.total_seconds() > (max_age_hours * 3600):
                        file_path.unlink()
                        self.logger.info(f"Cleaned up temp file: {file_path}")
                        
        except Exception as e:
            self.logger.error(f"Error cleaning up temp files: {str(e)}")
    
    def create_backup(self, source_file: Path, backup_type: str = "manual") -> Path:
        """
        Create backup of a file
        
        Args:
            source_file: Source file to backup
            backup_type: Type of backup ('manual', 'auto', etc.)
            
        Returns:
            Path to the backup file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{backup_type}_{source_file.stem}_{timestamp}{source_file.suffix}"
        backup_path = self.directories['backups'] / backup_filename
        
        try:
            import shutil
            shutil.copy2(source_file, backup_path)
            self.logger.info(f"Created backup: {backup_path}")
            return backup_path
        except Exception as e:
            self.logger.error(f"Failed to create backup: {str(e)}")
            raise
    
    def get_directory_info(self) -> Dict:
        """
        Get information about all directories
        
        Returns:
            Dictionary with directory information
        """
        info = {}
        for name, path in self.directories.items():
            try:
                file_count = len(list(path.glob('*'))) if path.exists() else 0
                info[name] = {
                    'path': str(path),
                    'exists': path.exists(),
                    'file_count': file_count
                }
            except Exception as e:
                info[name] = {
                    'path': str(path),
                    'exists': False,
                    'error': str(e)
                }
        
        return info


# Global instance
_directory_manager = None

def get_directory_manager() -> DirectoryManager:
    """Get global directory manager instance"""
    global _directory_manager
    if _directory_manager is None:
        _directory_manager = DirectoryManager()
    return _directory_manager
