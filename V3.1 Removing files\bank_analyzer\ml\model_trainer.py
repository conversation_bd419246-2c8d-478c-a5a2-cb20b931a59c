"""
Model training and retraining system for ML categorization
Handles automated training pipelines, performance tracking, and model versioning
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import logging
import threading
import time

from ..core.logger import get_logger
from .ml_categorizer import MLTransactionCategorizer
from .training_data_manager import TrainingDataManager
from .data_preparation import TransactionDataPreparator


@dataclass
class TrainingJob:
    """Represents a training job"""
    job_id: str
    job_type: str  # "initial", "retrain", "incremental"
    status: str  # "pending", "running", "completed", "failed"
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    training_samples: int = 0
    accuracy: float = 0.0
    model_version: str = ""
    error_message: str = ""
    metrics: Dict[str, Any] = None
    metadata: Dict[str, Any] = None  # Additional job-specific data


@dataclass
class ModelPerformanceHistory:
    """Tracks model performance over time"""
    model_version: str
    training_date: datetime
    accuracy: float
    training_samples: int
    categories_count: int
    cv_accuracy_mean: float
    cv_accuracy_std: float
    evaluation_metrics: Dict[str, Any]


class ModelTrainer:
    """
    Manages ML model training and retraining
    Provides automated training pipelines and performance tracking
    """
    
    def __init__(self, ml_data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.ml_data_dir = Path(ml_data_dir)
        self.ml_data_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.training_jobs_file = self.ml_data_dir / "training_jobs.json"
        self.performance_history_file = self.ml_data_dir / "performance_history.json"
        self.training_config_file = self.ml_data_dir / "training_config.json"
        
        # Components
        self.ml_categorizer = MLTransactionCategorizer()
        self.training_manager = TrainingDataManager()
        self.data_preparator = TransactionDataPreparator()
        
        # Training state
        self.current_job: Optional[TrainingJob] = None
        self.training_thread: Optional[threading.Thread] = None
        self.is_training = False
        
        # Load configuration
        self.config = self._load_training_config()
        
        # Performance history
        self.performance_history: List[ModelPerformanceHistory] = []
        self._load_performance_history()
    
    def _load_training_config(self) -> Dict[str, Any]:
        """Load training configuration"""
        default_config = {
            "auto_retrain_enabled": True,
            "min_new_samples_for_retrain": 20,  # Reduced for smaller datasets
            "retrain_interval_days": 7,
            "min_accuracy_threshold": 0.25,  # Very lenient for challenging datasets
            "max_training_time_minutes": 30,
            "backup_models": True,
            "notification_enabled": True
        }
        
        if self.training_config_file.exists():
            try:
                with open(self.training_config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults
                default_config.update(config)
            except Exception as e:
                self.logger.error(f"Error loading training config: {str(e)}")
        
        return default_config
    
    def _save_training_config(self):
        """Save training configuration"""
        try:
            with open(self.training_config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving training config: {str(e)}")
    
    def _load_performance_history(self):
        """Load model performance history"""
        if not self.performance_history_file.exists():
            return
        
        try:
            with open(self.performance_history_file, 'r') as f:
                history_data = json.load(f)
            
            self.performance_history = []
            for item in history_data:
                history = ModelPerformanceHistory(
                    model_version=item['model_version'],
                    training_date=datetime.fromisoformat(item['training_date']),
                    accuracy=item['accuracy'],
                    training_samples=item['training_samples'],
                    categories_count=item['categories_count'],
                    cv_accuracy_mean=item['cv_accuracy_mean'],
                    cv_accuracy_std=item['cv_accuracy_std'],
                    evaluation_metrics=item['evaluation_metrics']
                )
                self.performance_history.append(history)
                
        except Exception as e:
            self.logger.error(f"Error loading performance history: {str(e)}")
    
    def _save_performance_history(self):
        """Save model performance history"""
        try:
            history_data = []
            for history in self.performance_history:
                item = asdict(history)
                item['training_date'] = history.training_date.isoformat()
                history_data.append(item)
            
            with open(self.performance_history_file, 'w') as f:
                json.dump(history_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving performance history: {str(e)}")
    
    def create_training_job(self, job_type: str = "retrain", selected_transactions: List[str] = None) -> str:
        """
        Create a new training job

        Args:
            job_type: Type of training job ("retrain", "incremental")
            selected_transactions: List of transaction hash IDs for incremental training

        Returns:
            Job ID
        """
        job_id = f"job_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        job = TrainingJob(
            job_id=job_id,
            job_type=job_type,
            status="pending"
        )

        # Store selected transactions for incremental training
        if selected_transactions and job_type == "incremental":
            job.metadata = {"selected_transactions": selected_transactions}

        self._save_training_job(job)

        self.logger.info(f"Created training job: {job_id} (type: {job_type})")
        if selected_transactions:
            self.logger.info(f"Selected {len(selected_transactions)} transactions for incremental training")
        return job_id
    
    def start_training(self, job_id: str, async_training: bool = True) -> bool:
        """
        Start a training job
        
        Args:
            job_id: ID of the job to start
            async_training: Whether to run training asynchronously
            
        Returns:
            True if training started successfully
        """
        if self.is_training:
            self.logger.warning("Training already in progress")
            return False
        
        job = self._load_training_job(job_id)
        if not job:
            self.logger.error(f"Training job {job_id} not found")
            return False
        
        if job.status != "pending":
            self.logger.error(f"Training job {job_id} is not in pending status")
            return False
        
        self.current_job = job
        
        if async_training:
            self.training_thread = threading.Thread(target=self._run_training_job)
            self.training_thread.start()
        else:
            self._run_training_job()
        
        return True
    
    def _run_training_job(self):
        """Run the current training job"""
        if not self.current_job:
            return
        
        job = self.current_job
        
        try:
            self.is_training = True
            job.status = "running"
            job.started_at = datetime.now()
            self._save_training_job(job)
            
            self.logger.info(f"Starting training job: {job.job_id}")
            
            # Get training data stats
            stats = self.training_manager.get_labeling_stats()
            job.training_samples = stats.labeled_transactions
            
            # Check if we have sufficient data
            if job.training_samples < 10:
                raise Exception("Insufficient training data (minimum 10 labeled samples required)")
            
            # Train the model
            training_result = self.ml_categorizer.train_models(force_retrain=True)
            
            if not training_result.get("success", False):
                error_msg = "; ".join(training_result.get("errors", ["Unknown error"]))
                raise Exception(f"Training failed: {error_msg}")
            
            # Evaluate the model
            evaluation_result = self.ml_categorizer.evaluate_model()
            
            if "error" in evaluation_result:
                raise Exception(f"Model evaluation failed: {evaluation_result['error']}")
            
            # Update job with results
            job.accuracy = evaluation_result.get("accuracy", 0.0)
            job.model_version = training_result.get("model_version", "unknown")
            job.status = "completed"
            job.completed_at = datetime.now()
            
            # Save performance history
            self._add_performance_history(evaluation_result, job.model_version)
            
            # Check if accuracy meets threshold
            threshold = self.config.get("min_accuracy_threshold", 0.4)
            if job.accuracy < threshold:
                self.logger.warning(f"Model accuracy ({job.accuracy:.3f}) below threshold ({threshold:.3f})")
            else:
                self.logger.info(f"Model accuracy ({job.accuracy:.3f}) meets threshold ({threshold:.3f})")

            # Mark transactions as trained after successful training
            try:
                from .training_data_manager import TrainingDataManager
                training_manager = TrainingDataManager()

                # Get selected transactions from metadata or mark all labeled transactions
                selected_transactions = []
                if job.metadata and "selected_transactions" in job.metadata:
                    selected_transactions = job.metadata["selected_transactions"]
                else:
                    # If no specific selection, mark all labeled transactions as trained
                    all_labeled = training_manager.get_all_labeled_transactions()
                    selected_transactions = [txn.hash_id for txn in all_labeled]

                if selected_transactions:
                    mark_result = training_manager.mark_transactions_as_trained(
                        selected_transactions,
                        job.job_id  # Use job_id as training session ID
                    )
                    self.logger.info(f"Marked {mark_result['success_count']} transactions as trained")
                    if mark_result['error_count'] > 0:
                        self.logger.warning(f"Failed to mark {mark_result['error_count']} transactions as trained")
                else:
                    self.logger.warning("No transactions to mark as trained")

            except Exception as e:
                self.logger.error(f"Failed to update training status: {e}")

            self.logger.info(f"Training job completed: {job.job_id} (accuracy: {job.accuracy:.3f})")
            
        except Exception as e:
            job.status = "failed"
            job.error_message = str(e)
            job.completed_at = datetime.now()
            
            self.logger.error(f"Training job failed: {job.job_id} - {str(e)}")
        
        finally:
            self.is_training = False
            self._save_training_job(job)
            self.current_job = None
    
    def _add_performance_history(self, evaluation_result: Dict[str, Any], model_version: str):
        """Add performance record to history"""
        try:
            history = ModelPerformanceHistory(
                model_version=model_version,
                training_date=datetime.now(),
                accuracy=evaluation_result.get("accuracy", 0.0),
                training_samples=evaluation_result.get("training_samples", 0),
                categories_count=evaluation_result.get("categories_count", 0),
                cv_accuracy_mean=evaluation_result.get("cv_mean_accuracy", 0.0),
                cv_accuracy_std=evaluation_result.get("cv_std_accuracy", 0.0),
                evaluation_metrics=evaluation_result.get("classification_report", {})
            )
            
            self.performance_history.append(history)
            
            # Keep only last 50 records
            if len(self.performance_history) > 50:
                self.performance_history = self.performance_history[-50:]
            
            self._save_performance_history()
            
        except Exception as e:
            self.logger.error(f"Error adding performance history: {str(e)}")
    
    def check_auto_retrain_conditions(self) -> bool:
        """
        Check if automatic retraining conditions are met
        
        Returns:
            True if retraining should be triggered
        """
        if not self.config.get("auto_retrain_enabled", True):
            return False
        
        if self.is_training:
            return False
        
        # Check if enough time has passed since last training
        if self.performance_history:
            last_training = self.performance_history[-1].training_date
            interval_days = self.config.get("retrain_interval_days", 7)
            
            if datetime.now() - last_training < timedelta(days=interval_days):
                return False
        
        # Check if we have enough new samples
        stats = self.training_manager.get_labeling_stats()
        min_samples = self.config.get("min_new_samples_for_retrain", 50)
        
        if stats.labeled_transactions < min_samples:
            return False
        
        # Check if there are new samples since last training
        if self.performance_history:
            last_training_samples = self.performance_history[-1].training_samples
            new_samples = stats.labeled_transactions - last_training_samples
            
            if new_samples < min_samples:
                return False
        
        return True
    
    def trigger_auto_retrain(self) -> Optional[str]:
        """
        Trigger automatic retraining if conditions are met
        
        Returns:
            Job ID if retraining started, None otherwise
        """
        if not self.check_auto_retrain_conditions():
            return None
        
        self.logger.info("Auto-retrain conditions met, starting retraining...")
        
        job_id = self.create_training_job("auto_retrain")
        
        if self.start_training(job_id, async_training=True):
            return job_id
        
        return None
    
    def get_training_status(self) -> Dict[str, Any]:
        """Get current training status"""
        status = {
            "is_training": self.is_training,
            "current_job": None,
            "auto_retrain_enabled": self.config.get("auto_retrain_enabled", True),
            "can_auto_retrain": self.check_auto_retrain_conditions()
        }
        
        if self.current_job:
            status["current_job"] = {
                "job_id": self.current_job.job_id,
                "job_type": self.current_job.job_type,
                "status": self.current_job.status,
                "started_at": self.current_job.started_at.isoformat() if self.current_job.started_at else None,
                "training_samples": self.current_job.training_samples
            }
        
        return status
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get model performance summary"""
        if not self.performance_history:
            return {"error": "No performance history available"}
        
        latest = self.performance_history[-1]
        
        # Calculate trends
        accuracy_trend = "stable"
        if len(self.performance_history) >= 2:
            prev_accuracy = self.performance_history[-2].accuracy
            if latest.accuracy > prev_accuracy + 0.05:
                accuracy_trend = "improving"
            elif latest.accuracy < prev_accuracy - 0.05:
                accuracy_trend = "declining"
        
        return {
            "latest_model_version": latest.model_version,
            "latest_accuracy": latest.accuracy,
            "latest_training_date": latest.training_date.isoformat(),
            "training_samples": latest.training_samples,
            "categories_count": latest.categories_count,
            "accuracy_trend": accuracy_trend,
            "total_training_sessions": len(self.performance_history),
            "cv_accuracy_mean": latest.cv_accuracy_mean,
            "cv_accuracy_std": latest.cv_accuracy_std
        }
    
    def _save_training_job(self, job: TrainingJob):
        """Save training job to storage"""
        try:
            jobs = []
            
            # Load existing jobs
            if self.training_jobs_file.exists():
                with open(self.training_jobs_file, 'r') as f:
                    jobs = json.load(f)
            
            # Update or add job
            job_dict = asdict(job)
            if job.started_at:
                job_dict["started_at"] = job.started_at.isoformat()
            if job.completed_at:
                job_dict["completed_at"] = job.completed_at.isoformat()
            
            # Find and update existing job or add new one
            updated = False
            for i, existing_job in enumerate(jobs):
                if existing_job["job_id"] == job.job_id:
                    jobs[i] = job_dict
                    updated = True
                    break
            
            if not updated:
                jobs.append(job_dict)
            
            # Keep only last 100 jobs
            if len(jobs) > 100:
                jobs = jobs[-100:]
            
            with open(self.training_jobs_file, 'w') as f:
                json.dump(jobs, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving training job: {str(e)}")
    
    def _load_training_job(self, job_id: str) -> Optional[TrainingJob]:
        """Load training job from storage"""
        if not self.training_jobs_file.exists():
            return None
        
        try:
            with open(self.training_jobs_file, 'r') as f:
                jobs = json.load(f)
            
            for job_data in jobs:
                if job_data["job_id"] == job_id:
                    job = TrainingJob(
                        job_id=job_data["job_id"],
                        job_type=job_data["job_type"],
                        status=job_data["status"],
                        training_samples=job_data.get("training_samples", 0),
                        accuracy=job_data.get("accuracy", 0.0),
                        model_version=job_data.get("model_version", ""),
                        error_message=job_data.get("error_message", ""),
                        metrics=job_data.get("metrics")
                    )
                    
                    if job_data.get("started_at"):
                        job.started_at = datetime.fromisoformat(job_data["started_at"])
                    if job_data.get("completed_at"):
                        job.completed_at = datetime.fromisoformat(job_data["completed_at"])
                    
                    return job
            
        except Exception as e:
            self.logger.error(f"Error loading training job: {str(e)}")
        
        return None
    
    def update_config(self, **kwargs) -> bool:
        """
        Update training configuration
        
        Args:
            **kwargs: Configuration parameters to update
            
        Returns:
            True if updated successfully
        """
        try:
            self.config.update(kwargs)
            self._save_training_config()
            
            self.logger.info(f"Training configuration updated: {kwargs}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating training config: {str(e)}")
            return False
