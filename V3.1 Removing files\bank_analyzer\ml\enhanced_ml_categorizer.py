"""
Enhanced ML Categorizer with Advanced Feature Engineering

This module implements an improved ML categorizer that addresses the current
89.7% category accuracy and 83.2% subcategory accuracy issues through:
1. Advanced feature engineering
2. Better UPI transaction handling
3. Improved class balancing
4. Enhanced model architectures
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass
from pathlib import Path

# ML imports with availability checking
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.svm import SVC
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
    from sklearn.preprocessing import LabelEncoder, StandardScaler
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    from sklearn.pipeline import Pipeline
    from sklearn.compose import ColumnTransformer
    from sklearn.preprocessing import OneHotEncoder
    from imblearn.over_sampling import SMOTE
    from imblearn.under_sampling import RandomUnderSampler
    from imblearn.pipeline import Pipeline as ImbPipeline
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from .advanced_feature_engineering import AdvancedFeatureEngineer, TransactionFeatures
from .enhanced_upi_feature_extractor import EnhancedUPIFeatureExtractor
from ..core.logger import get_logger

@dataclass
class EnhancedMLPrediction:
    """Enhanced ML prediction result with detailed information"""
    category: str
    sub_category: str
    confidence: float
    model_version: str
    predicted_at: datetime
    feature_importance: Dict[str, float] = None
    category_confidence: float = 0.0
    subcategory_confidence: float = 0.0
    upi_features: Dict[str, Any] = None
    category_hints: List[Tuple[str, str, float]] = None

class EnhancedMLCategorizer:
    """Enhanced ML categorizer with advanced feature engineering"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.feature_engineer = AdvancedFeatureEngineer()
        self.upi_extractor = EnhancedUPIFeatureExtractor()
        
        # Model components
        self.category_model = None
        self.subcategory_models = {}
        self.category_encoder = None
        self.subcategory_encoders = {}
        self.feature_scaler = None
        
        # Model configuration
        self.model_config = {
            'use_random_forest': True,  # Use RF instead of SVM for better performance
            'use_class_balancing': True,
            'use_feature_selection': True,
            'cross_validation_folds': 5,
            'min_samples_for_subcategory': 3,  # Reduced threshold
            'confidence_threshold': 0.6
        }
        
        self.model_version = f"enhanced_v{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.is_trained = False
    
    def prepare_enhanced_training_data(self, transactions_df: pd.DataFrame) -> Tuple[pd.DataFrame, bool]:
        """
        Prepare training data with enhanced features
        
        Args:
            transactions_df: DataFrame with transaction data
            
        Returns:
            Tuple of (enhanced_df, has_sufficient_data)
        """
        if transactions_df.empty:
            return pd.DataFrame(), False
        
        self.logger.info(f"Preparing enhanced training data for {len(transactions_df)} transactions")
        
        enhanced_rows = []
        
        for _, row in transactions_df.iterrows():
            try:
                # Extract comprehensive features
                features = self.feature_engineer.extract_comprehensive_features(
                    description=row.get('normalized_description', ''),
                    amount=float(row.get('max_amount', 0)),
                    date=pd.to_datetime(row.get('last_seen')) if pd.notna(row.get('last_seen')) else None
                )
                
                # Create ML feature vector
                feature_vector = self.feature_engineer.create_ml_feature_vector(features)
                
                # Add original data
                enhanced_row = {
                    'category': row.get('category'),
                    'sub_category': row.get('sub_category'),
                    'description': row.get('description', ''),
                    'normalized_description': row.get('normalized_description', ''),
                    'amount': float(row.get('max_amount', 0)),
                    'frequency': int(row.get('frequency', 1)),
                }
                
                # Add enhanced features
                enhanced_row.update(feature_vector)
                enhanced_rows.append(enhanced_row)
                
            except Exception as e:
                self.logger.warning(f"Error processing transaction: {e}")
                continue
        
        if not enhanced_rows:
            return pd.DataFrame(), False
        
        enhanced_df = pd.DataFrame(enhanced_rows)
        
        # Filter out rows without category labels
        enhanced_df = enhanced_df.dropna(subset=['category'])
        
        # Check if we have sufficient data
        has_sufficient_data = (
            len(enhanced_df) >= 10 and
            enhanced_df['category'].nunique() >= 2
        )
        
        self.logger.info(f"Enhanced training data prepared: {len(enhanced_df)} samples, "
                        f"{enhanced_df['category'].nunique()} categories")
        
        return enhanced_df, has_sufficient_data
    
    def create_enhanced_pipeline(self, model_type: str = 'random_forest') -> Pipeline:
        """
        Create enhanced ML pipeline with feature engineering
        
        Args:
            model_type: Type of model to use ('random_forest', 'svm')
            
        Returns:
            Configured pipeline
        """
        # Define feature columns
        text_features = ['normalized_description']
        numerical_features = [
            'description_length', 'word_count', 'amount', 'amount_log',
            'day_of_week', 'hour_of_day', 'merchant_confidence', 'top_hint_confidence'
        ]
        categorical_features = [
            'amount_category', 'transaction_pattern', 'service_provider',
            'transaction_type', 'hint_category', 'hint_subcategory'
        ]
        boolean_features = [
            'is_micro_amount', 'is_small_amount', 'is_medium_amount', 'is_large_amount',
            'is_very_large_amount', 'is_round_amount', 'is_weekend', 'is_business_hours',
            'has_numbers', 'has_special_chars', 'has_category_hint',
            'has_bank_code', 'has_merchant_name', 'has_upi_id', 'is_person_to_person',
            'is_merchant_payment', 'is_paytm', 'is_gpay', 'is_phonepe',
            'is_food_ordering', 'is_recharge', 'is_bill_payment', 'is_shopping'
        ]
        
        # Create preprocessor
        preprocessor = ColumnTransformer([
            ('text', TfidfVectorizer(
                max_features=3000,
                ngram_range=(1, 3),  # Include trigrams for better context
                min_df=1,
                max_df=0.95,
                stop_words='english'
            ), text_features),
            ('num', StandardScaler(), numerical_features),
            ('cat', OneHotEncoder(handle_unknown='ignore', sparse=False), categorical_features),
            ('bool', 'passthrough', boolean_features)
        ])
        
        # Choose classifier
        if model_type == 'random_forest':
            classifier = RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                class_weight='balanced',  # Handle class imbalance
                n_jobs=-1
            )
        else:  # SVM
            classifier = SVC(
                kernel='rbf',
                probability=True,
                random_state=42,
                class_weight='balanced',
                C=10.0,
                gamma='scale'
            )
        
        # Create pipeline with class balancing if enabled
        if self.model_config['use_class_balancing']:
            pipeline = ImbPipeline([
                ('preprocessor', preprocessor),
                ('sampler', SMOTE(random_state=42, k_neighbors=1)),  # Handle class imbalance
                ('classifier', classifier)
            ])
        else:
            pipeline = Pipeline([
                ('preprocessor', preprocessor),
                ('classifier', classifier)
            ])
        
        return pipeline
    
    def train_enhanced_models(self, training_data: pd.DataFrame, force_retrain: bool = False) -> Dict[str, Any]:
        """
        Train enhanced models with advanced feature engineering
        
        Args:
            training_data: Enhanced training data
            force_retrain: Force retraining even if models exist
            
        Returns:
            Training results dictionary
        """
        if not SKLEARN_AVAILABLE:
            return {
                "success": False,
                "errors": ["scikit-learn not available"]
            }
        
        result = {
            "success": False,
            "category_model_trained": False,
            "subcategory_models_trained": 0,
            "metrics": {},
            "errors": [],
            "model_version": self.model_version
        }
        
        try:
            self.logger.info("Starting enhanced model training...")
            
            # Prepare feature columns
            feature_columns = [col for col in training_data.columns 
                             if col not in ['category', 'sub_category', 'description']]
            
            X = training_data[feature_columns]
            y_category = training_data['category'].values
            
            # Train category model
            self.logger.info("Training enhanced category model...")
            
            # Encode category labels
            self.category_encoder = LabelEncoder()
            y_category_encoded = self.category_encoder.fit_transform(y_category)
            
            # Create enhanced pipeline
            self.category_model = self.create_enhanced_pipeline('random_forest')
            
            # Train with cross-validation for evaluation
            cv_folds = min(self.model_config['cross_validation_folds'], 
                          len(np.unique(y_category_encoded)))
            
            if cv_folds >= 2:
                cv_scores = cross_val_score(
                    self.category_model, X, y_category_encoded,
                    cv=StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42),
                    scoring='accuracy'
                )
                category_accuracy = np.mean(cv_scores)
                self.logger.info(f"Category model CV accuracy: {category_accuracy:.3f} ± {np.std(cv_scores):.3f}")
            else:
                category_accuracy = 1.0
            
            # Train on full dataset
            self.category_model.fit(X, y_category_encoded)
            result["category_model_trained"] = True
            
            # Train subcategory models
            self.logger.info("Training enhanced subcategory models...")
            subcategory_count = 0
            
            for category in training_data['category'].unique():
                category_data = training_data[training_data['category'] == category]
                
                if len(category_data) < self.model_config['min_samples_for_subcategory']:
                    self.logger.warning(f"Insufficient data for {category} subcategory model: {len(category_data)} samples")
                    continue
                
                if category_data['sub_category'].nunique() < 2:
                    self.logger.warning(f"Only one subcategory for {category}, skipping model training")
                    continue
                
                # Train subcategory model
                X_sub = category_data[feature_columns]
                y_sub = category_data['sub_category'].values
                
                # Encode subcategory labels
                sub_encoder = LabelEncoder()
                y_sub_encoded = sub_encoder.fit_transform(y_sub)
                
                # Create and train subcategory model
                sub_model = self.create_enhanced_pipeline('random_forest')
                sub_model.fit(X_sub, y_sub_encoded)
                
                self.subcategory_models[category] = sub_model
                self.subcategory_encoders[category] = sub_encoder
                subcategory_count += 1
                
                self.logger.info(f"Trained subcategory model for {category}: {len(category_data)} samples")
            
            result["subcategory_models_trained"] = subcategory_count
            result["success"] = True
            result["metrics"] = {
                "category_accuracy": category_accuracy,
                "training_samples": len(training_data),
                "categories_count": len(training_data['category'].unique()),
                "subcategory_models": subcategory_count
            }
            
            self.is_trained = True
            self.logger.info(f"Enhanced model training completed successfully")
            
        except Exception as e:
            error_msg = f"Error training enhanced models: {str(e)}"
            self.logger.error(error_msg)
            result["errors"].append(error_msg)
        
        return result

    def predict_enhanced_category(self, description: str, amount: float = 0.0,
                                date: Optional[datetime] = None) -> Optional[EnhancedMLPrediction]:
        """
        Make enhanced prediction using advanced features

        Args:
            description: Transaction description
            amount: Transaction amount
            date: Transaction date

        Returns:
            EnhancedMLPrediction object or None if prediction fails
        """
        if not self.is_trained or not SKLEARN_AVAILABLE:
            return None

        try:
            # Extract comprehensive features
            features = self.feature_engineer.extract_comprehensive_features(
                description=description,
                amount=amount,
                date=date
            )

            # Create feature vector
            feature_vector = self.feature_engineer.create_ml_feature_vector(features)

            # Add required columns for consistency
            feature_vector['normalized_description'] = description

            # Convert to DataFrame for pipeline
            feature_df = pd.DataFrame([feature_vector])

            # Predict category
            category_proba = self.category_model.predict_proba(feature_df)[0]
            category_pred_encoded = self.category_model.predict(feature_df)[0]
            category = self.category_encoder.inverse_transform([category_pred_encoded])[0]
            category_confidence = np.max(category_proba)

            # Predict subcategory
            sub_category = None
            subcategory_confidence = 0.0

            if category in self.subcategory_models:
                sub_model = self.subcategory_models[category]
                sub_encoder = self.subcategory_encoders[category]

                sub_proba = sub_model.predict_proba(feature_df)[0]
                sub_pred_encoded = sub_model.predict(feature_df)[0]
                sub_category = sub_encoder.inverse_transform([sub_pred_encoded])[0]
                subcategory_confidence = np.max(sub_proba)

            # Calculate combined confidence
            combined_confidence = (category_confidence * 0.7) + (subcategory_confidence * 0.3)

            # Get feature importance if available
            feature_importance = None
            if hasattr(self.category_model.named_steps['classifier'], 'feature_importances_'):
                # This would require more complex feature name mapping
                # For now, we'll skip this to keep the implementation focused
                pass

            return EnhancedMLPrediction(
                category=category,
                sub_category=sub_category,
                confidence=combined_confidence,
                model_version=self.model_version,
                predicted_at=datetime.now(),
                feature_importance=feature_importance,
                category_confidence=category_confidence,
                subcategory_confidence=subcategory_confidence,
                upi_features=features.upi_features,
                category_hints=features.category_hints
            )

        except Exception as e:
            self.logger.error(f"Error in enhanced prediction: {str(e)}")
            return None

    def evaluate_enhanced_model(self, test_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Evaluate enhanced model performance

        Args:
            test_data: Test data with enhanced features

        Returns:
            Evaluation metrics
        """
        if not self.is_trained or test_data.empty:
            return {"error": "Model not trained or no test data"}

        try:
            results = {
                "total_samples": len(test_data),
                "category_accuracy": 0.0,
                "subcategory_accuracy": 0.0,
                "both_accuracy": 0.0,
                "category_report": {},
                "confusion_matrix": {},
                "per_category_performance": {}
            }

            # Prepare features
            feature_columns = [col for col in test_data.columns
                             if col not in ['category', 'sub_category', 'description']]
            X_test = test_data[feature_columns]

            # Category evaluation
            y_true_category = test_data['category'].values
            y_true_category_encoded = self.category_encoder.transform(y_true_category)
            y_pred_category_encoded = self.category_model.predict(X_test)

            category_accuracy = accuracy_score(y_true_category_encoded, y_pred_category_encoded)
            results["category_accuracy"] = category_accuracy

            # Category classification report
            category_names = self.category_encoder.classes_
            results["category_report"] = classification_report(
                y_true_category_encoded, y_pred_category_encoded,
                target_names=category_names, output_dict=True, zero_division=0
            )

            # Confusion matrix
            results["confusion_matrix"] = confusion_matrix(
                y_true_category_encoded, y_pred_category_encoded
            ).tolist()

            # Subcategory evaluation
            subcategory_correct = 0
            both_correct = 0

            for i, (_, row) in enumerate(test_data.iterrows()):
                true_category = row['category']
                true_subcategory = row['sub_category']

                pred_category = self.category_encoder.inverse_transform([y_pred_category_encoded[i]])[0]

                category_match = true_category == pred_category
                subcategory_match = False

                if category_match and true_category in self.subcategory_models:
                    sub_model = self.subcategory_models[true_category]
                    sub_encoder = self.subcategory_encoders[true_category]

                    # Get single row for prediction
                    X_single = X_test.iloc[[i]]
                    pred_sub_encoded = sub_model.predict(X_single)[0]
                    pred_subcategory = sub_encoder.inverse_transform([pred_sub_encoded])[0]

                    subcategory_match = true_subcategory == pred_subcategory

                if subcategory_match:
                    subcategory_correct += 1

                if category_match and subcategory_match:
                    both_correct += 1

            results["subcategory_accuracy"] = subcategory_correct / len(test_data)
            results["both_accuracy"] = both_correct / len(test_data)

            self.logger.info(f"Enhanced model evaluation completed:")
            self.logger.info(f"  Category accuracy: {category_accuracy:.3f}")
            self.logger.info(f"  Subcategory accuracy: {results['subcategory_accuracy']:.3f}")
            self.logger.info(f"  Both correct: {results['both_accuracy']:.3f}")

            return results

        except Exception as e:
            self.logger.error(f"Error evaluating enhanced model: {str(e)}")
            return {"error": str(e)}

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the enhanced models"""
        return {
            "is_trained": self.is_trained,
            "model_version": self.model_version,
            "sklearn_available": SKLEARN_AVAILABLE,
            "has_category_model": self.category_model is not None,
            "subcategory_models_count": len(self.subcategory_models),
            "subcategory_categories": list(self.subcategory_models.keys()),
            "model_config": self.model_config,
            "feature_engineer_available": self.feature_engineer is not None,
            "upi_extractor_available": self.upi_extractor is not None
        }
