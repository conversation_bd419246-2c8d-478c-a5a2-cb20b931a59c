"""
Parser factory for automatic format detection and parser selection
"""

from pathlib import Path
from typing import List, Optional, Dict, Any
import logging

from .base_parser import BaseStatementParser
from .indian_bank_yearly_pdf_parser import IndianBankYearlyPDFParser
from .final_indian_bank_parser import FinalIndianBankParser
from .indian_bank_excel_parser import IndianBankExcelParser
from .sbi_statement_parser import SBIStatementParser
from ..models.transaction import RawTransaction
from ..core.logger import get_logger


class StatementParserFactory:
    """
    Factory class for creating bank-specific parsers only
    Supports only Indian Bank and State Bank of India parsers
    """

    def __init__(self):
        self.logger = get_logger(__name__)

        # Supported banks with file-type specific parsers
        self.supported_banks = {
            "Indian Bank": {
                "yearly": {
                    "pdf": IndianBankYearlyPDFParser,
                    "excel": IndianBankExcelParser
                },
                "monthly": {
                    "pdf": FinalIndianBankParser,
                    "excel": IndianBankExcelParser
                }
            },
            "State Bank of India": {
                "yearly": {
                    "pdf": SBIStatementParser,
                    "excel": SBIStatementParser  # SBI parser can handle both
                },
                "monthly": {
                    "pdf": SBIStatementParser,
                    "excel": SBIStatementParser
                }
            }
        }
    
    def get_parser(self, file_path: Path, bank_name: str, period_type: str = "monthly") -> Optional[BaseStatementParser]:
        """
        Get the appropriate bank-specific parser for the given file

        Args:
            file_path: Path to the statement file
            bank_name: Name of the bank (must be supported)
            period_type: Type of statement period ("yearly" or "monthly")

        Returns:
            Parser instance or None if no suitable parser found
        """
        if not file_path.exists():
            self.logger.error(f"File does not exist: {file_path}")
            return None

        # Check if bank is supported
        if bank_name not in self.supported_banks:
            self.logger.error(f"Unsupported bank: {bank_name}")
            return None

        # Determine file type
        file_extension = file_path.suffix.lower()
        file_type = None

        if file_extension == '.pdf':
            file_type = 'pdf'
        elif file_extension in ['.xlsx', '.xls', '.xlsm']:
            file_type = 'excel'
        else:
            self.logger.error(f"Unsupported file type: {file_extension}")
            return None

        # Get parser class for the bank, period type, and file type
        bank_parsers = self.supported_banks[bank_name]
        period_parsers = bank_parsers.get(period_type)

        if not period_parsers:
            self.logger.error(f"No parser available for {bank_name} {period_type} statements")
            return None

        parser_class = period_parsers.get(file_type)

        if not parser_class:
            self.logger.error(f"No {file_type} parser available for {bank_name} {period_type} statements")
            return None

        try:
            parser = parser_class()

            # Verify parser can handle the file
            if not parser.can_parse(file_path):
                self.logger.error(f"Parser cannot handle file: {file_path}")
                return None

            self.logger.info(f"Selected {bank_name} {period_type} {file_type} parser for {file_path}")
            return parser

        except Exception as e:
            self.logger.error(f"Error creating parser: {str(e)}")
            return None

    def get_supported_banks(self) -> List[str]:
        """Get list of supported banks"""
        return list(self.supported_banks.keys())

    def get_supported_periods(self, bank_name: str) -> List[str]:
        """Get list of supported periods for a bank"""
        if bank_name in self.supported_banks:
            return list(self.supported_banks[bank_name].keys())
        return []

    def get_supported_file_types(self, bank_name: str, period_type: str) -> List[str]:
        """Get list of supported file types for a bank and period"""
        if bank_name in self.supported_banks:
            bank_parsers = self.supported_banks[bank_name]
            if period_type in bank_parsers:
                return list(bank_parsers[period_type].keys())
        return []
    
    def parse_file(self, file_path: Path, bank_name: str, period_type: str = "monthly") -> List[RawTransaction]:
        """
        Parse a statement file using the appropriate bank-specific parser

        Args:
            file_path: Path to the statement file
            bank_name: Name of the bank (must be supported)
            period_type: Type of statement period ("yearly" or "monthly")

        Returns:
            List of RawTransaction objects
        """
        parser = self.get_parser(file_path, bank_name, period_type)

        if not parser:
            return []

        try:
            transactions = parser.parse(file_path)

            # Log parsing results
            errors = parser.get_parsing_errors()
            if errors:
                self.logger.warning(f"Parsing completed with {len(errors)} errors:")
                for error in errors:
                    self.logger.warning(f"  - {error}")

            self.logger.info(f"Successfully parsed {len(transactions)} transactions from {file_path}")
            return transactions

        except Exception as e:
            self.logger.error(f"Error parsing file {file_path}: {str(e)}")
            return []
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats"""
        return ['.pdf', '.csv', '.xlsx', '.xls']

    def get_parser_info(self) -> Dict[str, Any]:
        """Get information about available parsers"""
        info = {}

        for bank_name, periods in self.supported_banks.items():
            info[bank_name] = {
                'periods': list(periods.keys()),
                'parsers': {}
            }

            for period, file_types in periods.items():
                info[bank_name]['parsers'][period] = {
                    file_type: parser_class.__name__
                    for file_type, parser_class in file_types.items()
                }

        return info
    
    def validate_file_format(self, file_path: Path, bank_name: str = None) -> Dict[str, Any]:
        """
        Validate file format and return detailed information

        Args:
            file_path: Path to the statement file
            bank_name: Name of the bank (optional)

        Returns:
            Dictionary with validation results
        """
        result = {
            'is_valid': False,
            'file_exists': False,
            'supported_format': False,
            'parser_available': False,
            'file_info': {},
            'errors': []
        }

        try:
            # Check if file exists
            if not file_path.exists():
                result['errors'].append(f"File does not exist: {file_path}")
                return result

            result['file_exists'] = True

            # Get file info
            stat = file_path.stat()
            result['file_info'] = {
                'name': file_path.name,
                'size': stat.st_size,
                'extension': file_path.suffix.lower(),
                'size_mb': round(stat.st_size / (1024 * 1024), 2)
            }

            # Check if format is supported
            extension = file_path.suffix.lower()
            supported_formats = self.get_supported_formats()
            if extension not in supported_formats:
                result['errors'].append(f"Unsupported file format: {extension}")
                return result

            result['supported_format'] = True

            # If bank name is provided, check if parser is available
            if bank_name and bank_name in self.supported_banks:
                result['parser_available'] = True
                result['is_valid'] = True
            else:
                result['errors'].append("Bank name must be specified for validation")

        except Exception as e:
            result['errors'].append(f"Validation error: {str(e)}")

        return result


# Global factory instance
parser_factory = StatementParserFactory()
