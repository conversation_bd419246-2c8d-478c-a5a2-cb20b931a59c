"""
State Bank of India specific statement parser
Handles SBI-specific Excel and PDF statement formats
"""

from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import date
from decimal import Decimal
import pandas as pd
import warnings

# Suppress openpyxl styling warnings for cleaner Excel parsing
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl.styles.stylesheet')

try:
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

from .bank_specific_parser import BankSpecificParser
from ..models.transaction import RawTransaction


class SBIStatementParser(BankSpecificParser):
    """
    State Bank of India statement parser
    Handles both Excel and PDF formats
    """
    
    def __init__(self):
        super().__init__("State Bank of India")
        self.supported_formats = ['.xlsx', '.xls', '.pdf']
    
    def _get_date_formats(self) -> List[str]:
        """Get SBI-specific date formats"""
        return [
            '%d %b %Y',      # 21 Jan 2024
            '%d/%m/%Y',      # 21/01/2024
            '%d-%m-%Y',      # 21-01-2024
            '%Y-%m-%d'       # 2024-01-21
        ]
    
    def _get_column_mappings(self) -> Dict[str, Any]:
        """Get SBI-specific column mappings"""
        return {
            'excel': {
                'date_col': 1,           # Column 1: Date
                'description_col': 2,    # Column 2: Details
                'reference_col': 3,      # Column 3: Ref No./Cheque No
                'debit_col': 4,          # Column 4: Debit
                'credit_col': 5,         # Column 5: Credit
                'balance_col': 6,        # Column 6: Balance
                'header_row': 0          # Row 0 contains headers
            },
            'pdf': {
                'date_col': 0,           # First column: Date
                'description_col': 1,    # Second column: Details
                'reference_col': 2,      # Third column: Ref No./Cheque No
                'debit_col': 3,          # Fourth column: Debit
                'credit_col': 4,         # Fifth column: Credit
                'balance_col': 5         # Sixth column: Balance
            }
        }
    
    def _get_transaction_patterns(self) -> Dict[str, str]:
        """Get SBI-specific transaction patterns"""
        return {
            'upi_transfer': r'TRANSFER TO \d+ -UPI/DR/',
            'upi_credit': r'TRANSFER FROM \d+ -UPI/CR/',
            'neft': r'NEFT\*',
            'imps': r'IMPS\*',
            'atm': r'ATM',
            'debit_card': r'POS'
        }
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if this parser can handle the file"""
        return file_path.suffix.lower() in self.supported_formats
    
    def parse(self, file_path: Path) -> List[RawTransaction]:
        """Parse SBI statement file"""
        if not self.can_parse(file_path):
            self.add_parsing_error(f"Unsupported file format: {file_path.suffix}")
            return []
        
        if file_path.suffix.lower() in ['.xlsx', '.xls']:
            return self._parse_excel(file_path)
        elif file_path.suffix.lower() == '.pdf':
            return self._parse_pdf(file_path)
        else:
            self.add_parsing_error(f"Unknown file format: {file_path.suffix}")
            return []
    
    def _parse_excel(self, file_path: Path) -> List[RawTransaction]:
        """Parse SBI Excel statement"""
        transactions = []
        
        try:
            self.logger.info(f"Parsing SBI Excel statement: {file_path}")
            
            # Read Excel file
            df = pd.read_excel(file_path)
            
            if df.empty:
                self.add_parsing_error("Excel file is empty")
                return []
            
            # Get column mappings
            col_map = self.column_mappings['excel']
            
            # Skip header row and process data rows
            for index, row in df.iterrows():
                if index == col_map['header_row']:
                    continue  # Skip header row
                
                try:
                    # Extract data from row
                    date_str = row.iloc[col_map['date_col']] if len(row) > col_map['date_col'] else None
                    description = row.iloc[col_map['description_col']] if len(row) > col_map['description_col'] else ""
                    reference = row.iloc[col_map['reference_col']] if len(row) > col_map['reference_col'] else None
                    debit_str = row.iloc[col_map['debit_col']] if len(row) > col_map['debit_col'] else None
                    credit_str = row.iloc[col_map['credit_col']] if len(row) > col_map['credit_col'] else None
                    balance_str = row.iloc[col_map['balance_col']] if len(row) > col_map['balance_col'] else None
                    
                    # Skip empty rows
                    if not date_str or str(date_str).strip() == "" or str(date_str).strip().lower() == "date":
                        continue
                    
                    # Parse date
                    transaction_date = self.parse_date(str(date_str))
                    if not transaction_date:
                        self.add_parsing_error(f"Invalid date: {date_str}", index + 2)
                        continue
                    
                    # Parse amounts
                    debit_amount = self.parse_amount(debit_str)
                    credit_amount = self.parse_amount(credit_str)
                    balance = self.parse_amount(balance_str)
                    
                    # Skip if no valid amount
                    if not debit_amount and not credit_amount:
                        continue
                    
                    # Create transaction
                    transaction = self.create_raw_transaction(
                        transaction_date=transaction_date,
                        description=str(description),
                        debit_amount=debit_amount,
                        credit_amount=credit_amount,
                        balance=balance,
                        reference_number=str(reference) if reference else None,
                        source_file=str(file_path),
                        source_line=index + 2
                    )
                    
                    # Validate and add transaction
                    if self.validate_transaction(transaction):
                        transactions.append(transaction)
                    
                except Exception as e:
                    self.add_parsing_error(f"Error parsing row {index + 2}: {str(e)}", index + 2)
                    continue
            
            self.logger.info(f"Successfully parsed {len(transactions)} transactions from Excel")
            return transactions
            
        except Exception as e:
            self.logger.error(f"Error parsing SBI Excel file: {str(e)}")
            self.add_parsing_error(f"Failed to parse Excel file: {str(e)}")
            return []
    
    def _parse_pdf(self, file_path: Path) -> List[RawTransaction]:
        """Parse SBI PDF statement"""
        if not PDF_AVAILABLE:
            self.add_parsing_error("pdfplumber not available for PDF parsing")
            return []
        
        transactions = []
        
        try:
            self.logger.info(f"Parsing SBI PDF statement: {file_path}")
            
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    self.logger.debug(f"Processing page {page_num + 1}/{len(pdf.pages)}")
                    
                    # Extract tables from page
                    tables = page.extract_tables()
                    
                    for table in tables:
                        if table:
                            page_transactions = self._extract_from_pdf_table(
                                table, page_num + 1, file_path
                            )
                            transactions.extend(page_transactions)
            
            self.logger.info(f"Successfully parsed {len(transactions)} transactions from PDF")
            return transactions
            
        except Exception as e:
            self.logger.error(f"Error parsing SBI PDF file: {str(e)}")
            self.add_parsing_error(f"Failed to parse PDF file: {str(e)}")
            return []
    
    def _extract_from_pdf_table(self, table: List[List[str]], 
                               page_num: int, file_path: Path) -> List[RawTransaction]:
        """Extract transactions from PDF table"""
        transactions = []
        col_map = self.column_mappings['pdf']
        
        for row_num, row in enumerate(table):
            try:
                # Skip header row
                if row_num == 0 or not row or len(row) < 6:
                    continue
                
                # Check if this looks like a header row
                if any(header in str(cell).lower() for cell in row[:3] 
                       for header in ['date', 'details', 'debit', 'credit']):
                    continue
                
                # Extract data from row
                date_str = row[col_map['date_col']] if len(row) > col_map['date_col'] else None
                description = row[col_map['description_col']] if len(row) > col_map['description_col'] else ""
                reference = row[col_map['reference_col']] if len(row) > col_map['reference_col'] else None
                debit_str = row[col_map['debit_col']] if len(row) > col_map['debit_col'] else None
                credit_str = row[col_map['credit_col']] if len(row) > col_map['credit_col'] else None
                balance_str = row[col_map['balance_col']] if len(row) > col_map['balance_col'] else None
                
                # Skip empty rows
                if not date_str or str(date_str).strip() == "":
                    continue
                
                # Parse date
                transaction_date = self.parse_date(str(date_str))
                if not transaction_date:
                    continue
                
                # Parse amounts
                debit_amount = self.parse_amount(debit_str)
                credit_amount = self.parse_amount(credit_str)
                balance = self.parse_amount(balance_str)
                
                # Skip if no valid amount
                if not debit_amount and not credit_amount:
                    continue
                
                # Create transaction
                transaction = self.create_raw_transaction(
                    transaction_date=transaction_date,
                    description=str(description),
                    debit_amount=debit_amount,
                    credit_amount=credit_amount,
                    balance=balance,
                    reference_number=str(reference) if reference else None,
                    source_file=str(file_path),
                    source_line=f"Page {page_num}, Row {row_num + 1}"
                )
                
                # Validate and add transaction
                if self.validate_transaction(transaction):
                    transactions.append(transaction)
                
            except Exception as e:
                self.add_parsing_error(
                    f"Error parsing PDF row {row_num + 1} on page {page_num}: {str(e)}"
                )
                continue
        
        return transactions
