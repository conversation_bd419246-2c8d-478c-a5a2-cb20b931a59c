"""
Enhanced Training Data Management System
Provides comprehensive model training data management with backup, clear, and smart auto-labeling capabilities
"""

import pandas as pd
import json
import pickle
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple, Set
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from ..core.logger import get_logger
from ..models.transaction import RawTransaction, ProcessedTransaction
from .training_data_manager import TrainingDataManager
from .data_preparation import UniqueTransaction, TransactionDataPreparator


class AutoLabelStatus(Enum):
    """Status of auto-labeled transactions"""
    PENDING_REVIEW = "pending_review"
    CONFIRMED = "confirmed"
    MODIFIED = "modified"
    REJECTED = "rejected"


@dataclass
class AutoLabeledTransaction:
    """Represents an auto-labeled transaction"""
    hash_id: str
    original_description: str
    normalized_description: str
    amount: float
    suggested_category: str
    suggested_sub_category: str
    confidence_score: float
    matched_transaction_id: str  # ID of the training transaction it matched
    similarity_score: float
    auto_labeled_at: datetime
    status: AutoLabelStatus = AutoLabelStatus.PENDING_REVIEW
    reviewed_by: Optional[str] = None
    reviewed_at: Optional[datetime] = None
    final_category: Optional[str] = None
    final_sub_category: Optional[str] = None


@dataclass
class TrainingDataBackup:
    """Represents a training data backup"""
    backup_id: str
    created_at: datetime
    description: str
    transaction_count: int
    file_path: str
    metadata: Dict[str, Any]


class EnhancedTrainingDataManager:
    """
    Enhanced training data manager with backup, clear, and auto-labeling capabilities
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize base training data manager
        self.base_manager = TrainingDataManager(str(self.data_dir))
        
        # Enhanced file paths
        self.backups_dir = self.data_dir / "backups"
        self.backups_dir.mkdir(parents=True, exist_ok=True)
        
        self.auto_labels_file = self.data_dir / "auto_labeled_transactions.json"
        self.backup_registry_file = self.data_dir / "backup_registry.json"
        self.similarity_cache_file = self.data_dir / "similarity_cache.pkl"
        
        # Configuration
        self.config = {
            "similarity_threshold": 0.75,  # Minimum similarity for auto-labeling
            "confidence_threshold": 0.8,   # Minimum confidence for auto-labeling
            "max_auto_labels_per_session": 100,  # Limit auto-labels per session
            "enable_amount_matching": True,
            "amount_tolerance": 0.1,  # 10% tolerance for amount matching
            "enable_caching": True
        }
        
        # Load existing data
        self.auto_labeled_transactions = self._load_auto_labeled_transactions()
        self.backup_registry = self._load_backup_registry()
        self.similarity_cache = self._load_similarity_cache()
        
        self.logger.info("Enhanced Training Data Manager initialized")
    
    def create_training_data_backup(self, description: str = "") -> str:
        """
        Create a backup of current training data
        
        Args:
            description: Optional description for the backup
            
        Returns:
            Backup ID
        """
        try:
            backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Get current training data
            training_data = self.base_manager.data_preparator.get_training_data()
            unique_transactions = self.base_manager.data_preparator.load_unique_transactions()
            
            # Create backup file
            backup_file = self.backups_dir / f"{backup_id}.json"
            
            backup_data = {
                "backup_id": backup_id,
                "created_at": datetime.now().isoformat(),
                "description": description or f"Automatic backup created at {datetime.now()}",
                "training_data": training_data.to_dict('records') if not training_data.empty else [],
                "unique_transactions": {
                    hash_id: {
                        "description": txn.description,
                        "normalized_description": txn.normalized_description,
                        "hash_id": txn.hash_id,
                        "frequency": txn.frequency,
                        "category": txn.category,
                        "sub_category": txn.sub_category,
                        "confidence": txn.confidence,
                        "is_manually_labeled": txn.is_manually_labeled,
                        "labeled_by": txn.labeled_by,
                        "labeled_at": txn.labeled_at.isoformat() if txn.labeled_at else None,
                        "amount_range": txn.amount_range,
                        "sample_amounts": txn.sample_amounts,
                        "transaction_types": list(txn.transaction_types),
                        "debit_frequency": txn.debit_frequency,
                        "credit_frequency": txn.credit_frequency
                    }
                    for hash_id, txn in unique_transactions.items()
                },
                "metadata": {
                    "total_transactions": len(unique_transactions),
                    "labeled_transactions": len([t for t in unique_transactions.values() if t.is_manually_labeled]),
                    "categories": list(set(t.category for t in unique_transactions.values() if t.category)),
                    "backup_size_mb": 0  # Will be calculated after saving
                }
            }
            
            # Save backup
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            # Calculate file size
            backup_data["metadata"]["backup_size_mb"] = round(backup_file.stat().st_size / (1024 * 1024), 2)
            
            # Update backup registry
            backup_info = TrainingDataBackup(
                backup_id=backup_id,
                created_at=datetime.now(),
                description=backup_data["description"],
                transaction_count=backup_data["metadata"]["total_transactions"],
                file_path=str(backup_file),
                metadata=backup_data["metadata"]
            )
            
            self.backup_registry[backup_id] = backup_info
            self._save_backup_registry()
            
            self.logger.info(f"Created training data backup: {backup_id} ({backup_data['metadata']['total_transactions']} transactions)")
            return backup_id
            
        except Exception as e:
            self.logger.error(f"Error creating training data backup: {str(e)}", exc_info=True)
            raise
    
    def clear_manual_labeling_interface(self, create_backup: bool = True) -> bool:
        """
        Clear all transactions from manual labeling interface while preserving trained model
        
        Args:
            create_backup: Whether to create a backup before clearing
            
        Returns:
            True if successful
        """
        try:
            if create_backup:
                backup_id = self.create_training_data_backup("Pre-clear backup")
                self.logger.info(f"Created backup before clearing: {backup_id}")
            
            # Clear auto-labeled transactions
            self.auto_labeled_transactions.clear()
            self._save_auto_labeled_transactions()
            
            # Clear similarity cache to force fresh calculations
            self.similarity_cache.clear()
            self._save_similarity_cache()
            
            self.logger.info("Manual labeling interface cleared successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error clearing manual labeling interface: {str(e)}", exc_info=True)
            return False

    def process_new_transactions_with_auto_labeling(self, new_transactions: List[RawTransaction]) -> Dict[str, Any]:
        """
        Process new transactions and apply auto-labeling based on trained model knowledge

        Args:
            new_transactions: List of new raw transactions to process

        Returns:
            Dictionary with processing results and auto-labeling statistics
        """
        try:
            results = {
                "total_processed": len(new_transactions),
                "auto_labeled": 0,
                "manual_required": 0,
                "auto_labeled_transactions": [],
                "manual_transactions": [],
                "processing_time": 0,
                "similarity_matches": 0
            }

            start_time = datetime.now()

            # Load existing training data for similarity matching
            existing_transactions = self.base_manager.data_preparator.load_unique_transactions()
            labeled_transactions = {
                hash_id: txn for hash_id, txn in existing_transactions.items()
                if txn.is_manually_labeled and txn.category and txn.sub_category
            }

            self.logger.info(f"Processing {len(new_transactions)} new transactions against {len(labeled_transactions)} labeled transactions")

            for raw_txn in new_transactions:
                if not raw_txn.description:
                    results["manual_required"] += 1
                    results["manual_transactions"].append(raw_txn)
                    continue

                # Find best match in existing labeled transactions
                best_match = self._find_best_similarity_match(raw_txn, labeled_transactions)

                if best_match and best_match["similarity_score"] >= self.config["similarity_threshold"]:
                    # Create auto-labeled transaction
                    auto_labeled = self._create_auto_labeled_transaction(raw_txn, best_match)

                    if auto_labeled:
                        self.auto_labeled_transactions[auto_labeled.hash_id] = auto_labeled
                        results["auto_labeled"] += 1
                        results["auto_labeled_transactions"].append(auto_labeled)
                        results["similarity_matches"] += 1
                    else:
                        results["manual_required"] += 1
                        results["manual_transactions"].append(raw_txn)
                else:
                    # No good match found, requires manual labeling
                    results["manual_required"] += 1
                    results["manual_transactions"].append(raw_txn)

            # Save auto-labeled transactions
            self._save_auto_labeled_transactions()

            # Calculate processing time
            results["processing_time"] = (datetime.now() - start_time).total_seconds()

            self.logger.info(f"Auto-labeling complete: {results['auto_labeled']} auto-labeled, "
                           f"{results['manual_required']} require manual review")

            return results

        except Exception as e:
            self.logger.error(f"Error processing transactions with auto-labeling: {str(e)}", exc_info=True)
            raise

    def _find_best_similarity_match(self, raw_txn: RawTransaction,
                                   labeled_transactions: Dict[str, UniqueTransaction]) -> Optional[Dict[str, Any]]:
        """
        Find the best similarity match for a raw transaction

        Args:
            raw_txn: Raw transaction to match
            labeled_transactions: Dictionary of labeled transactions to match against

        Returns:
            Best match information or None if no good match found
        """
        try:
            # Normalize the new transaction description
            normalized_desc = self.base_manager.data_preparator.normalize_description(raw_txn.description)
            if not normalized_desc:
                return None

            # Check cache first
            cache_key = f"{normalized_desc}_{raw_txn.amount}"
            if self.config["enable_caching"] and cache_key in self.similarity_cache:
                cached_result = self.similarity_cache[cache_key]
                if cached_result["hash_id"] in labeled_transactions:
                    return cached_result

            best_match = None
            best_score = 0.0

            for hash_id, labeled_txn in labeled_transactions.items():
                # Calculate similarity score
                similarity_score = self._calculate_enhanced_similarity(
                    raw_txn, labeled_txn, normalized_desc
                )

                if similarity_score > best_score and similarity_score >= self.config["similarity_threshold"]:
                    best_score = similarity_score
                    best_match = {
                        "hash_id": hash_id,
                        "transaction": labeled_txn,
                        "similarity_score": similarity_score,
                        "category": labeled_txn.category,
                        "sub_category": labeled_txn.sub_category,
                        "confidence": labeled_txn.confidence
                    }

            # Cache the result if caching is enabled
            if self.config["enable_caching"] and best_match:
                self.similarity_cache[cache_key] = best_match
                self._save_similarity_cache()

            return best_match

        except Exception as e:
            self.logger.error(f"Error finding similarity match: {str(e)}", exc_info=True)
            return None

    def _calculate_enhanced_similarity(self, raw_txn: RawTransaction,
                                     labeled_txn: UniqueTransaction,
                                     normalized_desc: str) -> float:
        """
        Calculate enhanced similarity score between transactions

        Args:
            raw_txn: Raw transaction
            labeled_txn: Labeled transaction to compare against
            normalized_desc: Pre-normalized description of raw transaction

        Returns:
            Similarity score between 0.0 and 1.0
        """
        try:
            # Base description similarity using existing algorithm
            desc_similarity = self.base_manager._calculate_similarity(
                normalized_desc, labeled_txn.normalized_description
            )

            # Amount similarity (if enabled)
            amount_similarity = 1.0
            if self.config["enable_amount_matching"] and labeled_txn.sample_amounts:
                raw_amount = abs(float(raw_txn.amount))

                # Check if amount is within tolerance of any sample amounts
                for sample_amount in labeled_txn.sample_amounts:
                    sample_amount = abs(sample_amount)
                    if sample_amount > 0:
                        tolerance = sample_amount * self.config["amount_tolerance"]
                        if abs(raw_amount - sample_amount) <= tolerance:
                            amount_similarity = 1.0
                            break
                        else:
                            # Calculate proportional similarity
                            diff_ratio = abs(raw_amount - sample_amount) / max(raw_amount, sample_amount)
                            amount_similarity = max(amount_similarity, 1.0 - diff_ratio)
                else:
                    # No close amount match found
                    amount_similarity = 0.5

            # Transaction type consistency
            raw_txn_type = "debit" if float(raw_txn.amount) < 0 else "credit"
            type_consistency = 1.0 if raw_txn_type in labeled_txn.transaction_types else 0.3

            # Weighted combination
            final_similarity = (
                0.7 * desc_similarity +      # Description is most important
                0.2 * amount_similarity +    # Amount provides good context
                0.1 * type_consistency       # Transaction type consistency
            )

            return min(final_similarity, 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating enhanced similarity: {str(e)}", exc_info=True)
            return 0.0

    def _create_auto_labeled_transaction(self, raw_txn: RawTransaction,
                                       match_info: Dict[str, Any]) -> Optional[AutoLabeledTransaction]:
        """
        Create an auto-labeled transaction from a raw transaction and match info

        Args:
            raw_txn: Raw transaction
            match_info: Match information from similarity search

        Returns:
            AutoLabeledTransaction or None if creation failed
        """
        try:
            # Generate hash ID for the new transaction
            normalized_desc = self.base_manager.data_preparator.normalize_description(raw_txn.description)
            hash_id = self.base_manager.data_preparator.generate_hash_id(normalized_desc)

            # Calculate confidence score based on similarity and original confidence
            base_confidence = match_info["confidence"]
            similarity_score = match_info["similarity_score"]

            # Confidence is weighted combination of similarity and original confidence
            confidence_score = min(0.9, (similarity_score * 0.6 + base_confidence * 0.4))

            # Only create auto-label if confidence is above threshold
            if confidence_score < self.config["confidence_threshold"]:
                return None

            auto_labeled = AutoLabeledTransaction(
                hash_id=hash_id,
                original_description=raw_txn.description,
                normalized_description=normalized_desc,
                amount=float(raw_txn.amount),
                suggested_category=match_info["category"],
                suggested_sub_category=match_info["sub_category"],
                confidence_score=confidence_score,
                matched_transaction_id=match_info["hash_id"],
                similarity_score=similarity_score,
                auto_labeled_at=datetime.now(),
                status=AutoLabelStatus.PENDING_REVIEW
            )

            return auto_labeled

        except Exception as e:
            self.logger.error(f"Error creating auto-labeled transaction: {str(e)}", exc_info=True)
            return None

    def get_pending_auto_labeled_transactions(self) -> List[AutoLabeledTransaction]:
        """
        Get all auto-labeled transactions pending review

        Returns:
            List of auto-labeled transactions with pending status
        """
        return [
            txn for txn in self.auto_labeled_transactions.values()
            if txn.status == AutoLabelStatus.PENDING_REVIEW
        ]

    def review_auto_labeled_transaction(self, hash_id: str, action: str,
                                      final_category: Optional[str] = None,
                                      final_sub_category: Optional[str] = None,
                                      reviewer: str = "user") -> bool:
        """
        Review an auto-labeled transaction

        Args:
            hash_id: Hash ID of the auto-labeled transaction
            action: "confirm", "modify", or "reject"
            final_category: Final category if modifying
            final_sub_category: Final sub-category if modifying
            reviewer: Name of the reviewer

        Returns:
            True if successful
        """
        try:
            if hash_id not in self.auto_labeled_transactions:
                self.logger.error(f"Auto-labeled transaction not found: {hash_id}")
                return False

            auto_labeled = self.auto_labeled_transactions[hash_id]
            auto_labeled.reviewed_by = reviewer
            auto_labeled.reviewed_at = datetime.now()

            if action == "confirm":
                auto_labeled.status = AutoLabelStatus.CONFIRMED
                auto_labeled.final_category = auto_labeled.suggested_category
                auto_labeled.final_sub_category = auto_labeled.suggested_sub_category

                # Add to training data
                success = self._add_confirmed_to_training_data(auto_labeled)
                if not success:
                    self.logger.error(f"Failed to add confirmed transaction to training data: {hash_id}")
                    return False

            elif action == "modify":
                if not final_category or not final_sub_category:
                    self.logger.error("Final category and sub-category required for modify action")
                    return False

                auto_labeled.status = AutoLabelStatus.MODIFIED
                auto_labeled.final_category = final_category
                auto_labeled.final_sub_category = final_sub_category

                # Add modified version to training data
                success = self._add_confirmed_to_training_data(auto_labeled)
                if not success:
                    self.logger.error(f"Failed to add modified transaction to training data: {hash_id}")
                    return False

            elif action == "reject":
                auto_labeled.status = AutoLabelStatus.REJECTED
                # Don't add to training data

            else:
                self.logger.error(f"Invalid review action: {action}")
                return False

            # Save updated auto-labeled transactions
            self._save_auto_labeled_transactions()

            self.logger.info(f"Reviewed auto-labeled transaction {hash_id}: {action}")
            return True

        except Exception as e:
            self.logger.error(f"Error reviewing auto-labeled transaction: {str(e)}", exc_info=True)
            return False

    def batch_review_auto_labeled_transactions(self, review_actions: List[Dict[str, Any]],
                                             reviewer: str = "user") -> Dict[str, Any]:
        """
        Batch review multiple auto-labeled transactions

        Args:
            review_actions: List of review actions, each containing:
                - hash_id: Transaction hash ID
                - action: "confirm", "modify", or "reject"
                - final_category: Optional final category for modify
                - final_sub_category: Optional final sub-category for modify
            reviewer: Name of the reviewer

        Returns:
            Dictionary with batch review results
        """
        try:
            results = {
                "total_reviewed": 0,
                "confirmed": 0,
                "modified": 0,
                "rejected": 0,
                "errors": []
            }

            for review_action in review_actions:
                hash_id = review_action.get("hash_id")
                action = review_action.get("action")
                final_category = review_action.get("final_category")
                final_sub_category = review_action.get("final_sub_category")

                success = self.review_auto_labeled_transaction(
                    hash_id, action, final_category, final_sub_category, reviewer
                )

                if success:
                    results["total_reviewed"] += 1
                    results[action] += 1
                else:
                    results["errors"].append(f"Failed to review {hash_id}")

            self.logger.info(f"Batch review complete: {results['total_reviewed']} transactions reviewed")
            return results

        except Exception as e:
            self.logger.error(f"Error in batch review: {str(e)}", exc_info=True)
            return {"total_reviewed": 0, "errors": [str(e)]}

    def _add_confirmed_to_training_data(self, auto_labeled: AutoLabeledTransaction) -> bool:
        """
        Add a confirmed auto-labeled transaction to the training data

        Args:
            auto_labeled: Confirmed auto-labeled transaction

        Returns:
            True if successful
        """
        try:
            # Create a raw transaction for the training data manager
            raw_txn = RawTransaction(
                description=auto_labeled.original_description,
                amount=auto_labeled.amount,
                date=datetime.now().date()
            )

            # Add to training data using the base manager
            success = self.base_manager.data_preparator.update_transaction_label(
                auto_labeled.hash_id,
                auto_labeled.final_category,
                auto_labeled.final_sub_category,
                auto_labeled.confidence_score,
                labeled_by=f"auto_confirmed_{auto_labeled.reviewed_by}"
            )

            return success

        except Exception as e:
            self.logger.error(f"Error adding confirmed transaction to training data: {str(e)}", exc_info=True)
            return False

    def get_auto_labeling_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about auto-labeling performance

        Returns:
            Dictionary with auto-labeling statistics
        """
        try:
            total_auto_labeled = len(self.auto_labeled_transactions)

            status_counts = {}
            for status in AutoLabelStatus:
                status_counts[status.value] = len([
                    txn for txn in self.auto_labeled_transactions.values()
                    if txn.status == status
                ])

            # Calculate accuracy (confirmed + modified / total reviewed)
            total_reviewed = sum(status_counts[status.value] for status in [
                AutoLabelStatus.CONFIRMED, AutoLabelStatus.MODIFIED, AutoLabelStatus.REJECTED
            ])

            accuracy = 0.0
            if total_reviewed > 0:
                correct = status_counts[AutoLabelStatus.CONFIRMED.value] + status_counts[AutoLabelStatus.MODIFIED.value]
                accuracy = correct / total_reviewed

            # Average confidence and similarity scores
            avg_confidence = 0.0
            avg_similarity = 0.0
            if total_auto_labeled > 0:
                avg_confidence = sum(txn.confidence_score for txn in self.auto_labeled_transactions.values()) / total_auto_labeled
                avg_similarity = sum(txn.similarity_score for txn in self.auto_labeled_transactions.values()) / total_auto_labeled

            return {
                "total_auto_labeled": total_auto_labeled,
                "status_breakdown": status_counts,
                "total_reviewed": total_reviewed,
                "accuracy": accuracy,
                "average_confidence": avg_confidence,
                "average_similarity": avg_similarity,
                "pending_review": status_counts[AutoLabelStatus.PENDING_REVIEW.value]
            }

        except Exception as e:
            self.logger.error(f"Error getting auto-labeling statistics: {str(e)}", exc_info=True)
            return {}

    def _load_auto_labeled_transactions(self) -> Dict[str, AutoLabeledTransaction]:
        """Load auto-labeled transactions from file"""
        try:
            if not self.auto_labels_file.exists():
                return {}

            with open(self.auto_labels_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            auto_labeled = {}
            for hash_id, txn_data in data.items():
                # Convert datetime strings back to datetime objects
                txn_data['auto_labeled_at'] = datetime.fromisoformat(txn_data['auto_labeled_at'])
                if txn_data.get('reviewed_at'):
                    txn_data['reviewed_at'] = datetime.fromisoformat(txn_data['reviewed_at'])

                # Convert status string to enum
                txn_data['status'] = AutoLabelStatus(txn_data['status'])

                auto_labeled[hash_id] = AutoLabeledTransaction(**txn_data)

            return auto_labeled

        except Exception as e:
            self.logger.error(f"Error loading auto-labeled transactions: {str(e)}", exc_info=True)
            return {}

    def _save_auto_labeled_transactions(self):
        """Save auto-labeled transactions to file"""
        try:
            data = {}
            for hash_id, txn in self.auto_labeled_transactions.items():
                txn_dict = asdict(txn)
                # Convert datetime objects to strings
                txn_dict['auto_labeled_at'] = txn.auto_labeled_at.isoformat()
                if txn.reviewed_at:
                    txn_dict['reviewed_at'] = txn.reviewed_at.isoformat()
                # Convert enum to string
                txn_dict['status'] = txn.status.value
                data[hash_id] = txn_dict

            with open(self.auto_labels_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"Error saving auto-labeled transactions: {str(e)}", exc_info=True)

    def _load_backup_registry(self) -> Dict[str, TrainingDataBackup]:
        """Load backup registry from file"""
        try:
            if not self.backup_registry_file.exists():
                return {}

            with open(self.backup_registry_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            registry = {}
            for backup_id, backup_data in data.items():
                backup_data['created_at'] = datetime.fromisoformat(backup_data['created_at'])
                registry[backup_id] = TrainingDataBackup(**backup_data)

            return registry

        except Exception as e:
            self.logger.error(f"Error loading backup registry: {str(e)}", exc_info=True)
            return {}

    def _save_backup_registry(self):
        """Save backup registry to file"""
        try:
            data = {}
            for backup_id, backup in self.backup_registry.items():
                backup_dict = asdict(backup)
                backup_dict['created_at'] = backup.created_at.isoformat()
                data[backup_id] = backup_dict

            with open(self.backup_registry_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error(f"Error saving backup registry: {str(e)}", exc_info=True)

    def _load_similarity_cache(self) -> Dict[str, Any]:
        """Load similarity cache from file"""
        try:
            if not self.similarity_cache_file.exists():
                return {}

            with open(self.similarity_cache_file, 'rb') as f:
                return pickle.load(f)

        except Exception as e:
            self.logger.error(f"Error loading similarity cache: {str(e)}", exc_info=True)
            return {}

    def _save_similarity_cache(self):
        """Save similarity cache to file"""
        try:
            with open(self.similarity_cache_file, 'wb') as f:
                pickle.dump(self.similarity_cache, f)

        except Exception as e:
            self.logger.error(f"Error saving similarity cache: {str(e)}", exc_info=True)
