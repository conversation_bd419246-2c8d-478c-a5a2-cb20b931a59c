"""
Transaction data models for Bank Statement Analyzer
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Optional, Dict, Any, List
from decimal import Decimal
import uuid


@dataclass
class RawTransaction:
    """
    Raw transaction data as parsed from bank statements
    This represents the unprocessed transaction before categorization
    """
    
    # Core transaction data
    date: date
    description: str
    amount: Decimal
    balance: Optional[Decimal] = None
    
    # Transaction type (debit/credit)
    transaction_type: str = ""  # "DEBIT", "CREDIT", "TRANSFER"
    
    # Additional bank-specific fields
    reference_number: Optional[str] = None
    cheque_number: Optional[str] = None
    branch_code: Optional[str] = None
    
    # Metadata
    source_file: Optional[str] = None
    source_line: Optional[int] = None
    bank_name: Optional[str] = None
    account_number: Optional[str] = None
    
    # Processing status
    is_processed: bool = False
    processing_errors: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Post-initialization validation and cleanup"""
        # Ensure amount is Decimal
        if not isinstance(self.amount, Decimal):
            self.amount = Decimal(str(self.amount))
        
        # Ensure balance is Decimal if provided
        if self.balance is not None and not isinstance(self.balance, Decimal):
            self.balance = Decimal(str(self.balance))
        
        # Clean up description
        if self.description:
            self.description = self.description.strip()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'date': self.date.isoformat() if self.date else None,
            'description': self.description,
            'amount': float(self.amount),
            'balance': float(self.balance) if self.balance else None,
            'transaction_type': self.transaction_type,
            'reference_number': self.reference_number,
            'cheque_number': self.cheque_number,
            'branch_code': self.branch_code,
            'source_file': self.source_file,
            'source_line': self.source_line,
            'bank_name': self.bank_name,
            'account_number': self.account_number,
            'is_processed': self.is_processed,
            'processing_errors': self.processing_errors
        }


@dataclass
class ProcessedTransaction:
    """
    Processed transaction ready for import into the main application
    This matches the structure expected by the main application's expense tracker
    """
    
    # Main application fields (matching expenses.csv structure)
    id: Optional[str] = None
    date: Optional[date] = None
    type: str = "Expense"  # "Expense", "Income", "Transfer"
    category: str = ""
    sub_category: str = ""
    transaction_mode: str = ""
    amount: Decimal = Decimal('0.00')
    notes: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # Additional fields for processing
    original_description: str = ""
    confidence_score: float = 0.0  # Confidence in automatic categorization
    is_manually_reviewed: bool = False
    suggested_categories: List[Dict[str, Any]] = field(default_factory=list)
    
    # Link to original raw transaction
    raw_transaction: Optional[RawTransaction] = None
    
    def __post_init__(self):
        """Post-initialization setup"""
        if self.id is None:
            self.id = str(uuid.uuid4())
        
        if self.created_at is None:
            self.created_at = datetime.now()
        
        if self.updated_at is None:
            self.updated_at = datetime.now()
        
        # Ensure amount is Decimal
        if not isinstance(self.amount, Decimal):
            self.amount = Decimal(str(self.amount))
    
    def validate(self) -> List[str]:
        """
        Validate the processed transaction
        Returns list of validation errors
        """
        errors = []
        
        if not self.date:
            errors.append("Date is required")
        
        if not self.category:
            errors.append("Category is required")
        
        if not self.sub_category:
            errors.append("Sub-category is required")
        
        if not self.transaction_mode:
            errors.append("Transaction mode is required")
        
        if self.amount <= 0:
            errors.append("Amount must be greater than 0")
        
        if not self.type or self.type not in ["Expense", "Income", "Transfer"]:
            errors.append("Type must be one of: Expense, Income, Transfer")
        
        return errors
    
    def to_expense_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary format expected by main application's expense tracker
        """
        return {
            'id': self.id,
            'date': self.date.isoformat() if self.date else '',
            'type': self.type,
            'category': self.category,
            'sub_category': self.sub_category,
            'transaction_mode': self.transaction_mode,
            'amount': float(self.amount),
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else '',
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else ''
        }
    
    def update_from_raw(self, raw_transaction: RawTransaction):
        """Update fields from raw transaction"""
        self.raw_transaction = raw_transaction
        self.date = raw_transaction.date
        self.original_description = raw_transaction.description
        self.notes = raw_transaction.description

        # Determine transaction type - prioritize explicit transaction_type field over amount sign
        if hasattr(raw_transaction, 'transaction_type') and raw_transaction.transaction_type:
            txn_type_str = raw_transaction.transaction_type.lower()
            if 'debit' in txn_type_str or 'dr' in txn_type_str:
                self.type = "Expense"
                self.amount = abs(raw_transaction.amount)
            elif 'credit' in txn_type_str or 'cr' in txn_type_str:
                self.type = "Income"
                self.amount = abs(raw_transaction.amount)
            else:
                # Fallback to amount sign
                if raw_transaction.amount < 0:
                    self.type = "Expense"
                    self.amount = abs(raw_transaction.amount)
                else:
                    self.type = "Income"
                    self.amount = raw_transaction.amount
        else:
            # Fallback to amount sign
            if raw_transaction.amount < 0:
                self.type = "Expense"
                self.amount = abs(raw_transaction.amount)
            else:
                self.type = "Income"
                self.amount = raw_transaction.amount
