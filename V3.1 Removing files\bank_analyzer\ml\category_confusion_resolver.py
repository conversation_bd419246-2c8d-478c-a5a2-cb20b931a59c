"""
Category Confusion Resolver

This module addresses specific misclassification patterns identified in the analysis:
1. Investments → Business (27 times)
2. Youtube Income → Salary (25 times) 
3. Shared Money → Business (19 times)
4. Other common confusions

It implements targeted rules and enhanced features to resolve these issues.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

@dataclass
class ConfusionRule:
    """Rule to resolve category confusion"""
    source_category: str
    target_category: str
    patterns: List[str]
    amount_range: Optional[Tuple[float, float]] = None
    confidence_boost: float = 0.3
    description: str = ""

class CategoryConfusionResolver:
    """Resolves common category confusion patterns"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._initialize_confusion_rules()
        self._initialize_disambiguation_patterns()
    
    def _initialize_confusion_rules(self):
        """Initialize rules to resolve specific confusion patterns"""
        
        # Rules to distinguish Investments from Business
        self.investment_vs_business_rules = [
            ConfusionRule(
                source_category="Business",
                target_category="Investments",
                patterns=[
                    r'aurasilver', r'gold', r'silver', r'mutual\s*fund', r'sip',
                    r'investment', r'equity', r'stock', r'share', r'bond',
                    r'fd', r'fixed\s*deposit', r'recurring\s*deposit', r'rd'
                ],
                amount_range=(1000, float('inf')),  # Investments usually larger amounts
                confidence_boost=0.4,
                description="Distinguish investments from business transactions"
            ),
            ConfusionRule(
                source_category="Investments",
                target_category="Business",
                patterns=[
                    r'kitchen', r'cooking', r'essentials', r'supplies',
                    r'grocery', r'vegetables', r'fruits', r'market'
                ],
                amount_range=(10, 1000),  # Business expenses usually smaller
                confidence_boost=0.3,
                description="Distinguish business expenses from investments"
            )
        ]
        
        # Rules to distinguish Youtube Income from Salary
        self.youtube_vs_salary_rules = [
            ConfusionRule(
                source_category="Salary",
                target_category="Youtube Income",
                patterns=[
                    r'youtube', r'id\s*sales', r'content', r'creator',
                    r'monetization', r'adsense', r'video', r'channel'
                ],
                amount_range=(100, 5000),  # Youtube income range
                confidence_boost=0.5,
                description="Distinguish Youtube income from regular salary"
            ),
            ConfusionRule(
                source_category="Youtube Income",
                target_category="Salary",
                patterns=[
                    r'zomato', r'swiggy', r'delivery', r'tips', r'payout',
                    r'shadowfax', r'dunzo', r'uber', r'ola', r'driver'
                ],
                amount_range=(50, 2000),  # Delivery salary range
                confidence_boost=0.4,
                description="Distinguish delivery salary from Youtube income"
            )
        ]
        
        # Rules to distinguish Shared Money from Business
        self.shared_vs_business_rules = [
            ConfusionRule(
                source_category="Business",
                target_category="Shared Money",
                patterns=[
                    r'from\s+(dad|father|papa|mom|mother|mama|vasanth|friend)',
                    r'(mr|mrs|ms)\s+[a-z]+\s+[a-z]+',  # Person names
                    r'personal', r'family', r'relative', r'brother', r'sister'
                ],
                amount_range=(100, 10000),  # Shared money range
                confidence_boost=0.4,
                description="Distinguish shared money from business transactions"
            ),
            ConfusionRule(
                source_category="Shared Money",
                target_category="Business",
                patterns=[
                    r'kitchen', r'cooking', r'essentials', r'supplies',
                    r'business', r'commercial', r'trade', r'vendor'
                ],
                amount_range=(10, 1000),  # Business transaction range
                confidence_boost=0.3,
                description="Distinguish business transactions from shared money"
            )
        ]
        
        # Additional confusion rules for other common patterns
        self.additional_rules = [
            # Borrowed vs Returned confusion
            ConfusionRule(
                source_category="Returned",
                target_category="Borrowed",
                patterns=[
                    r'borrowed', r'loan', r'lend', r'advance', r'credit'
                ],
                confidence_boost=0.3,
                description="Distinguish borrowed money from returned money"
            ),
            ConfusionRule(
                source_category="Borrowed",
                target_category="Returned",
                patterns=[
                    r'returned', r'repay', r'payback', r'settle', r'clear'
                ],
                confidence_boost=0.3,
                description="Distinguish returned money from borrowed money"
            ),
            
            # EMI vs Business confusion
            ConfusionRule(
                source_category="Business",
                target_category="Emi",
                patterns=[
                    r'emi', r'installment', r'loan', r'finance', r'ola\s*s1',
                    r'bike', r'vehicle', r'car', r'monthly\s*payment'
                ],
                amount_range=(500, 10000),  # EMI range
                confidence_boost=0.4,
                description="Distinguish EMI payments from business transactions"
            ),
            
            # Grooming vs Business confusion
            ConfusionRule(
                source_category="Business",
                target_category="Grooming",
                patterns=[
                    r'haircut', r'salon', r'barber', r'grooming', r'beauty',
                    r'spa', r'massage', r'facial', r'hair'
                ],
                amount_range=(50, 500),  # Grooming service range
                confidence_boost=0.4,
                description="Distinguish grooming services from business transactions"
            )
        ]
        
        # Combine all rules
        self.all_confusion_rules = (
            self.investment_vs_business_rules +
            self.youtube_vs_salary_rules +
            self.shared_vs_business_rules +
            self.additional_rules
        )
    
    def _initialize_disambiguation_patterns(self):
        """Initialize patterns for disambiguating similar transactions"""
        
        # Patterns that strongly indicate specific categories
        self.strong_category_indicators = {
            'Investments': [
                r'aurasilver', r'gold\s*investment', r'mutual\s*fund',
                r'sip', r'systematic\s*investment', r'equity', r'stock'
            ],
            'Youtube Income': [
                r'youtube', r'id\s*sales', r'content\s*creator',
                r'monetization', r'adsense'
            ],
            'Shared Money': [
                r'from\s+(dad|father|mom|mother|vasanth)',
                r'family\s*transfer', r'personal\s*money'
            ],
            'Salary': [
                r'zomato\s*(tips|payout)', r'swiggy\s*(tips|payout)',
                r'shadowfax\s*payout', r'delivery\s*payment'
            ],
            'Emi': [
                r'ola\s*s1\s*(pro|x)', r'bike\s*emi', r'loan\s*emi',
                r'monthly\s*installment'
            ],
            'Grooming': [
                r'haircut', r'salon', r'barber\s*shop', r'beauty\s*parlor'
            ],
            'Transportation': [
                r'petrol', r'diesel', r'fuel', r'gas\s*station',
                r'bunk', r'service\s*station'
            ]
        }
        
        # Amount-based category hints
        self.amount_based_hints = {
            'Investments': (1000, float('inf')),
            'Youtube Income': (100, 5000),
            'Shared Money': (100, 20000),
            'Emi': (500, 15000),
            'Grooming': (50, 500),
            'Transportation': (50, 2000)
        }
    
    def resolve_category_confusion(self, description: str, amount: float,
                                 predicted_category: str, confidence: float) -> Tuple[str, float]:
        """
        Resolve category confusion using targeted rules
        
        Args:
            description: Transaction description
            amount: Transaction amount
            predicted_category: Originally predicted category
            confidence: Original prediction confidence
            
        Returns:
            Tuple of (corrected_category, adjusted_confidence)
        """
        desc_lower = description.lower()
        
        # Check strong category indicators first
        for category, patterns in self.strong_category_indicators.items():
            for pattern in patterns:
                if re.search(pattern, desc_lower):
                    # Strong indicator found, boost confidence significantly
                    if category != predicted_category:
                        self.logger.info(f"Strong indicator resolved: {predicted_category} → {category}")
                        return category, min(confidence + 0.3, 0.95)
                    else:
                        return predicted_category, min(confidence + 0.2, 0.95)
        
        # Apply confusion resolution rules
        for rule in self.all_confusion_rules:
            if rule.source_category == predicted_category:
                # Check if any pattern matches
                pattern_match = any(re.search(pattern, desc_lower) for pattern in rule.patterns)
                
                # Check amount range if specified
                amount_match = True
                if rule.amount_range:
                    min_amt, max_amt = rule.amount_range
                    amount_match = min_amt <= amount <= max_amt
                
                if pattern_match and amount_match:
                    new_confidence = min(confidence + rule.confidence_boost, 0.95)
                    self.logger.info(f"Confusion rule applied: {rule.source_category} → {rule.target_category}")
                    return rule.target_category, new_confidence
        
        # Check amount-based hints for additional validation
        for category, (min_amt, max_amt) in self.amount_based_hints.items():
            if category == predicted_category and not (min_amt <= amount <= max_amt):
                # Amount doesn't match expected range, reduce confidence
                reduced_confidence = max(confidence - 0.2, 0.1)
                self.logger.debug(f"Amount mismatch for {category}: {amount} not in [{min_amt}, {max_amt}]")
                return predicted_category, reduced_confidence
        
        # No confusion resolution needed
        return predicted_category, confidence
    
    def get_category_disambiguation_features(self, description: str, amount: float) -> Dict[str, Any]:
        """
        Generate features specifically for category disambiguation
        
        Args:
            description: Transaction description
            amount: Transaction amount
            
        Returns:
            Dictionary of disambiguation features
        """
        desc_lower = description.lower()
        
        features = {
            # Investment indicators
            'has_investment_keywords': any(re.search(pattern, desc_lower) 
                                         for pattern in self.strong_category_indicators.get('Investments', [])),
            'investment_amount_range': 1000 <= amount <= 100000,
            
            # Youtube income indicators
            'has_youtube_keywords': any(re.search(pattern, desc_lower) 
                                      for pattern in self.strong_category_indicators.get('Youtube Income', [])),
            'youtube_amount_range': 100 <= amount <= 5000,
            
            # Shared money indicators
            'has_personal_keywords': any(re.search(pattern, desc_lower) 
                                       for pattern in self.strong_category_indicators.get('Shared Money', [])),
            'shared_amount_range': 100 <= amount <= 20000,
            
            # Salary indicators
            'has_salary_keywords': any(re.search(pattern, desc_lower) 
                                     for pattern in self.strong_category_indicators.get('Salary', [])),
            'salary_amount_range': 50 <= amount <= 10000,
            
            # EMI indicators
            'has_emi_keywords': any(re.search(pattern, desc_lower) 
                                  for pattern in self.strong_category_indicators.get('Emi', [])),
            'emi_amount_range': 500 <= amount <= 15000,
            
            # Business transaction indicators
            'has_business_keywords': any(re.search(pattern, desc_lower) 
                                       for pattern in ['kitchen', 'cooking', 'essentials', 'supplies']),
            'business_amount_range': 10 <= amount <= 1000,
            
            # Person-to-person indicators
            'has_person_name': bool(re.search(r'\b(mr|mrs|ms)\s+[a-z]+', desc_lower)),
            'has_family_keywords': any(word in desc_lower for word in 
                                     ['dad', 'mom', 'father', 'mother', 'family', 'friend']),
            
            # Amount characteristics
            'is_round_amount': amount % 100 == 0,
            'is_small_amount': amount < 100,
            'is_medium_amount': 100 <= amount <= 1000,
            'is_large_amount': amount > 1000,
        }
        
        return features
