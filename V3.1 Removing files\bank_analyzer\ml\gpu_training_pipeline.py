"""
GPU Training Pipeline

High-performance CUDA-accelerated training pipeline for bank transaction categorization
targeting 95%+ real-world accuracy with intelligent data augmentation and validation.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, WeightedRandomSampler
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import json
import logging
from datetime import datetime
import time
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

from .gpu_deep_learning_categorizer import GPUDeepLearningCategorizer, TransactionDataset
from ..core.logger import get_logger


class AdvancedTrainingPipeline:
    """
    Advanced GPU training pipeline with sophisticated validation and optimization
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config/ml_models"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # GPU setup with optimization
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        if torch.cuda.is_available():
            # Optimize GPU settings
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
            # Clear GPU cache
            torch.cuda.empty_cache()
            
            self.logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
            self.logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
            self.logger.info(f"CUDA Version: {torch.version.cuda}")
        else:
            self.logger.warning("CUDA not available, using CPU (training will be slower)")
        
        # Training configuration
        self.training_config = {
            'target_accuracy': 0.95,
            'max_epochs': 20,
            'early_stopping_patience': 5,
            'learning_rate': 2e-5,
            'weight_decay': 0.01,
            'batch_size': 16,
            'gradient_accumulation_steps': 2,
            'warmup_steps': 100,
            'max_grad_norm': 1.0,
            'validation_split': 0.2,
            'cross_validation_folds': 5,
            'min_samples_per_class': 10
        }
        
        # Data augmentation settings
        self.augmentation_config = {
            'enable_augmentation': True,
            'synonym_replacement_prob': 0.1,
            'random_insertion_prob': 0.1,
            'random_swap_prob': 0.1,
            'random_deletion_prob': 0.1,
            'augmentation_factor': 2  # How many augmented samples per original
        }
        
        self.categorizer = GPUDeepLearningCategorizer(config_dir)
    
    def analyze_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Comprehensive data quality analysis for optimal training
        """
        self.logger.info("Analyzing data quality for optimal training...")
        
        # Basic statistics
        total_samples = len(df)
        unique_categories = df['category'].nunique()
        
        # Category distribution analysis
        category_counts = df['category'].value_counts()
        
        # Identify problematic categories
        insufficient_data = category_counts[category_counts < self.training_config['min_samples_per_class']]
        imbalanced_categories = category_counts[category_counts < total_samples * 0.01]  # Less than 1%
        
        # Text quality analysis
        df['text_length'] = df['description'].str.len()
        df['word_count'] = df['description'].str.split().str.len()
        
        # Duplicate analysis
        duplicate_descriptions = df['description'].duplicated().sum()
        
        # Class imbalance metrics
        max_samples = category_counts.max()
        min_samples = category_counts.min()
        imbalance_ratio = max_samples / min_samples if min_samples > 0 else float('inf')
        
        quality_report = {
            'total_samples': total_samples,
            'unique_categories': unique_categories,
            'category_distribution': category_counts.to_dict(),
            'insufficient_data_categories': insufficient_data.index.tolist(),
            'imbalanced_categories': imbalanced_categories.index.tolist(),
            'duplicate_descriptions': duplicate_descriptions,
            'avg_text_length': df['text_length'].mean(),
            'avg_word_count': df['word_count'].mean(),
            'class_imbalance_ratio': imbalance_ratio,
            'data_quality_score': self.calculate_data_quality_score(df, category_counts)
        }
        
        # Recommendations
        recommendations = []
        if len(insufficient_data) > 0:
            recommendations.append(f"Remove {len(insufficient_data)} categories with <{self.training_config['min_samples_per_class']} samples")
        
        if imbalance_ratio > 10:
            recommendations.append("Consider data augmentation for minority classes")
        
        if duplicate_descriptions > total_samples * 0.05:
            recommendations.append("High number of duplicate descriptions detected")
        
        quality_report['recommendations'] = recommendations
        
        self.logger.info(f"Data quality score: {quality_report['data_quality_score']:.2f}/10")
        
        return quality_report
    
    def calculate_data_quality_score(self, df: pd.DataFrame, category_counts: pd.Series) -> float:
        """Calculate overall data quality score (0-10)"""
        score = 10.0
        
        # Penalize for insufficient categories
        insufficient_ratio = len(category_counts[category_counts < 10]) / len(category_counts)
        score -= insufficient_ratio * 3
        
        # Penalize for high class imbalance
        max_samples = category_counts.max()
        min_samples = category_counts.min()
        if min_samples > 0:
            imbalance_ratio = max_samples / min_samples
            if imbalance_ratio > 20:
                score -= 2
            elif imbalance_ratio > 10:
                score -= 1
        
        # Penalize for duplicates
        duplicate_ratio = df['description'].duplicated().sum() / len(df)
        score -= duplicate_ratio * 2
        
        # Penalize for very short descriptions
        short_desc_ratio = (df['description'].str.len() < 20).sum() / len(df)
        score -= short_desc_ratio * 1
        
        return max(0, score)
    
    def prepare_balanced_dataset(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare a balanced dataset with data augmentation and filtering
        """
        self.logger.info("Preparing balanced dataset...")
        
        # Filter out categories with insufficient data
        category_counts = df['category'].value_counts()
        valid_categories = category_counts[category_counts >= self.training_config['min_samples_per_class']].index
        
        df_filtered = df[df['category'].isin(valid_categories)].copy()
        self.logger.info(f"Filtered to {len(df_filtered)} samples across {len(valid_categories)} categories")
        
        # Apply data augmentation if enabled
        if self.augmentation_config['enable_augmentation']:
            df_augmented = self.apply_data_augmentation(df_filtered)
            return df_augmented
        
        return df_filtered
    
    def apply_data_augmentation(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply intelligent data augmentation for minority classes
        """
        self.logger.info("Applying data augmentation...")
        
        category_counts = df['category'].value_counts()
        median_count = category_counts.median()
        
        augmented_data = []
        
        for category in category_counts.index:
            category_data = df[df['category'] == category]
            current_count = len(category_data)
            
            # Determine how many augmented samples to create
            if current_count < median_count:
                target_count = min(int(median_count), current_count * self.augmentation_config['augmentation_factor'])
                augment_count = target_count - current_count
                
                if augment_count > 0:
                    augmented_samples = self.augment_category_data(category_data, augment_count)
                    augmented_data.extend(augmented_samples)
        
        if augmented_data:
            augmented_df = pd.DataFrame(augmented_data)
            combined_df = pd.concat([df, augmented_df], ignore_index=True)
            self.logger.info(f"Added {len(augmented_data)} augmented samples")
            return combined_df
        
        return df
    
    def augment_category_data(self, category_data: pd.DataFrame, augment_count: int) -> List[Dict[str, Any]]:
        """
        Create augmented samples for a specific category
        """
        augmented_samples = []
        
        for _ in range(augment_count):
            # Randomly select a sample to augment
            base_sample = category_data.sample(1).iloc[0]
            
            # Apply text augmentation
            augmented_text = self.augment_text(base_sample['description'])
            
            augmented_sample = {
                'description': augmented_text,
                'category': base_sample['category'],
                'sub_category': base_sample.get('sub_category', ''),
                'is_manually_labeled': True  # Mark as manually labeled
            }
            
            augmented_samples.append(augmented_sample)
        
        return augmented_samples
    
    def augment_text(self, text: str) -> str:
        """
        Apply text augmentation techniques
        """
        words = text.split()
        
        # Random word swapping
        if len(words) > 2 and np.random.random() < self.augmentation_config['random_swap_prob']:
            idx1, idx2 = np.random.choice(len(words), 2, replace=False)
            words[idx1], words[idx2] = words[idx2], words[idx1]
        
        # Random deletion
        if len(words) > 3 and np.random.random() < self.augmentation_config['random_deletion_prob']:
            del_idx = np.random.choice(len(words))
            words.pop(del_idx)
        
        return ' '.join(words)
    
    def create_weighted_sampler(self, labels: List[int]) -> WeightedRandomSampler:
        """
        Create weighted sampler to handle class imbalance
        """
        class_counts = np.bincount(labels)
        class_weights = 1.0 / class_counts
        sample_weights = [class_weights[label] for label in labels]
        
        return WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(sample_weights),
            replacement=True
        )
    
    def train_with_cross_validation(self, csv_path: str) -> Dict[str, Any]:
        """
        Train model with k-fold cross-validation for robust performance estimation
        """
        self.logger.info("Starting cross-validation training...")
        
        # Load and prepare data
        df = pd.read_csv(csv_path)
        
        # Filter to manually labeled data
        if 'is_manually_labeled' in df.columns:
            df = df[df['is_manually_labeled'] == True].copy()
        else:
            df = df[df['category'].notna() & (df['category'] != '')].copy()
        
        # Analyze data quality
        quality_report = self.analyze_data_quality(df)
        
        # Prepare balanced dataset
        df_prepared = self.prepare_balanced_dataset(df)
        
        # Prepare data for training
        texts, labels, metadata = self.categorizer.prepare_training_data_from_df(df_prepared)
        
        # Cross-validation
        cv_results = []
        kfold = StratifiedKFold(n_splits=self.training_config['cross_validation_folds'], shuffle=True, random_state=42)
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(texts, labels)):
            self.logger.info(f"Training fold {fold + 1}/{self.training_config['cross_validation_folds']}")
            
            # Split data
            X_train = [texts[i] for i in train_idx]
            X_val = [texts[i] for i in val_idx]
            y_train = [labels[i] for i in train_idx]
            y_val = [labels[i] for i in val_idx]
            
            # Train model for this fold
            fold_results = self.train_single_fold(X_train, X_val, y_train, y_val, metadata, fold)
            cv_results.append(fold_results)
        
        # Aggregate results
        avg_accuracy = np.mean([r['val_accuracy'] for r in cv_results])
        std_accuracy = np.std([r['val_accuracy'] for r in cv_results])
        
        final_results = {
            'cross_validation_results': cv_results,
            'average_accuracy': avg_accuracy,
            'accuracy_std': std_accuracy,
            'target_achieved': avg_accuracy >= self.training_config['target_accuracy'],
            'data_quality_report': quality_report,
            'metadata': metadata,
            'training_config': self.training_config
        }
        
        self.logger.info(f"Cross-validation completed. Average accuracy: {avg_accuracy:.4f} ± {std_accuracy:.4f}")
        
        # Train final model on all data if target achieved
        if final_results['target_achieved']:
            self.logger.info("Target accuracy achieved. Training final model on all data...")
            final_model_results = self.categorizer.train_model_from_prepared_data(texts, labels, metadata)
            final_results['final_model'] = final_model_results
        
        return final_results

    def train_single_fold(self, X_train: List[str], X_val: List[str],
                         y_train: List[int], y_val: List[int],
                         metadata: Dict[str, Any], fold: int) -> Dict[str, Any]:
        """
        Train model for a single cross-validation fold
        """
        # Create temporary categorizer for this fold
        fold_categorizer = GPUDeepLearningCategorizer(str(self.config_dir))

        # Train model
        training_results = fold_categorizer.train_model_from_prepared_data(
            X_train + X_val, y_train + y_val, metadata
        )

        # Evaluate on validation set
        val_predictions = []
        val_confidences = []

        for text in X_val:
            try:
                pred_category, confidence, _ = fold_categorizer.predict(text)
                pred_idx = fold_categorizer.label_encoder.transform([pred_category])[0]
                val_predictions.append(pred_idx)
                val_confidences.append(confidence)
            except:
                val_predictions.append(0)  # Default prediction
                val_confidences.append(0.0)

        # Calculate metrics
        val_accuracy = accuracy_score(y_val, val_predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(y_val, val_predictions, average='weighted')

        return {
            'fold': fold,
            'val_accuracy': val_accuracy,
            'val_precision': precision,
            'val_recall': recall,
            'val_f1': f1,
            'avg_confidence': np.mean(val_confidences),
            'training_samples': len(X_train),
            'validation_samples': len(X_val)
        }

    def evaluate_real_world_performance(self, csv_path: str, model_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Evaluate model performance on real-world data with comprehensive metrics
        """
        self.logger.info("Evaluating real-world performance...")

        # Load model if path provided
        if model_path:
            self.categorizer.load_model_from_path(model_path)
        elif not self.categorizer.model:
            raise ValueError("No model loaded for evaluation")

        # Load test data
        df = pd.read_csv(csv_path)

        # Use a held-out test set (different from training)
        test_df = df.sample(frac=0.2, random_state=123)  # Different seed from training

        predictions = []
        actual_categories = []
        confidences = []
        processing_times = []

        for _, row in test_df.iterrows():
            description = row['description']
            actual_category = row['category']

            start_time = time.time()
            try:
                pred_category, confidence, _ = self.categorizer.predict(description)
                processing_time = time.time() - start_time

                predictions.append(pred_category)
                actual_categories.append(actual_category)
                confidences.append(confidence)
                processing_times.append(processing_time)

            except Exception as e:
                self.logger.warning(f"Prediction failed for: {description[:50]}... Error: {e}")
                continue

        # Calculate comprehensive metrics
        accuracy = accuracy_score(actual_categories, predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(
            actual_categories, predictions, average='weighted'
        )

        # Per-category analysis
        unique_categories = list(set(actual_categories))
        category_metrics = {}

        for category in unique_categories:
            cat_actual = [1 if cat == category else 0 for cat in actual_categories]
            cat_pred = [1 if cat == category else 0 for cat in predictions]

            if sum(cat_actual) > 0:  # Only if category exists in test set
                cat_accuracy = accuracy_score(cat_actual, cat_pred)
                category_metrics[category] = {
                    'accuracy': cat_accuracy,
                    'samples': sum(cat_actual),
                    'avg_confidence': np.mean([conf for conf, actual in zip(confidences, actual_categories) if actual == category])
                }

        # Performance analysis
        high_confidence_predictions = [p for p, c in zip(predictions, confidences) if c > 0.9]
        high_conf_accuracy = accuracy_score(
            [a for a, c in zip(actual_categories, confidences) if c > 0.9],
            high_confidence_predictions
        ) if high_confidence_predictions else 0

        results = {
            'overall_accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'avg_confidence': np.mean(confidences),
            'avg_processing_time': np.mean(processing_times),
            'high_confidence_accuracy': high_conf_accuracy,
            'category_metrics': category_metrics,
            'total_predictions': len(predictions),
            'target_achieved': accuracy >= self.training_config['target_accuracy'],
            'confidence_distribution': {
                'high_confidence': sum(1 for c in confidences if c > 0.9),
                'medium_confidence': sum(1 for c in confidences if 0.7 <= c <= 0.9),
                'low_confidence': sum(1 for c in confidences if c < 0.7)
            }
        }

        self.logger.info(f"Real-world accuracy: {accuracy:.4f}")
        self.logger.info(f"Average confidence: {np.mean(confidences):.4f}")
        self.logger.info(f"Average processing time: {np.mean(processing_times)*1000:.2f}ms")

        return results

    def generate_performance_report(self, results: Dict[str, Any]) -> str:
        """
        Generate a comprehensive performance report
        """
        report = []
        report.append("=" * 80)
        report.append("GPU DEEP LEARNING MODEL PERFORMANCE REPORT")
        report.append("=" * 80)

        # Overall performance
        if 'overall_accuracy' in results:
            report.append(f"\n📊 OVERALL PERFORMANCE:")
            report.append(f"   Accuracy: {results['overall_accuracy']:.4f} ({results['overall_accuracy']*100:.1f}%)")
            report.append(f"   Precision: {results['precision']:.4f}")
            report.append(f"   Recall: {results['recall']:.4f}")
            report.append(f"   F1-Score: {results['f1_score']:.4f}")
            report.append(f"   Average Confidence: {results['avg_confidence']:.4f}")

            target_status = "✅ ACHIEVED" if results['target_achieved'] else "❌ NOT ACHIEVED"
            report.append(f"   Target 95% Accuracy: {target_status}")

        # Cross-validation results
        if 'cross_validation_results' in results:
            report.append(f"\n🔄 CROSS-VALIDATION RESULTS:")
            report.append(f"   Average Accuracy: {results['average_accuracy']:.4f} ± {results['accuracy_std']:.4f}")

            for i, fold_result in enumerate(results['cross_validation_results']):
                report.append(f"   Fold {i+1}: {fold_result['val_accuracy']:.4f}")

        # Performance breakdown
        if 'confidence_distribution' in results:
            conf_dist = results['confidence_distribution']
            total = sum(conf_dist.values())
            report.append(f"\n📈 CONFIDENCE DISTRIBUTION:")
            report.append(f"   High Confidence (>0.9): {conf_dist['high_confidence']} ({conf_dist['high_confidence']/total*100:.1f}%)")
            report.append(f"   Medium Confidence (0.7-0.9): {conf_dist['medium_confidence']} ({conf_dist['medium_confidence']/total*100:.1f}%)")
            report.append(f"   Low Confidence (<0.7): {conf_dist['low_confidence']} ({conf_dist['low_confidence']/total*100:.1f}%)")

        # Category performance
        if 'category_metrics' in results:
            report.append(f"\n📋 TOP PERFORMING CATEGORIES:")
            sorted_categories = sorted(
                results['category_metrics'].items(),
                key=lambda x: x[1]['accuracy'],
                reverse=True
            )

            for category, metrics in sorted_categories[:10]:
                report.append(f"   {category}: {metrics['accuracy']:.3f} ({metrics['samples']} samples)")

        # Data quality
        if 'data_quality_report' in results:
            quality = results['data_quality_report']
            report.append(f"\n🔍 DATA QUALITY ANALYSIS:")
            report.append(f"   Quality Score: {quality['data_quality_score']:.1f}/10")
            report.append(f"   Total Samples: {quality['total_samples']:,}")
            report.append(f"   Categories: {quality['unique_categories']}")

            if quality['recommendations']:
                report.append(f"   Recommendations:")
                for rec in quality['recommendations']:
                    report.append(f"     • {rec}")

        # Performance metrics
        if 'avg_processing_time' in results:
            report.append(f"\n⚡ PERFORMANCE METRICS:")
            report.append(f"   Average Processing Time: {results['avg_processing_time']*1000:.2f}ms")
            report.append(f"   GPU Acceleration: {'✅ Enabled' if torch.cuda.is_available() else '❌ Disabled'}")

        report.append("\n" + "=" * 80)

        return "\n".join(report)
