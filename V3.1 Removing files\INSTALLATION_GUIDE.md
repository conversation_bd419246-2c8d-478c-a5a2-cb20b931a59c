# 🚀 GPU-Accelerated Bank Statement Analyzer Installation Guide

This guide will help you set up the GPU-accelerated categorization system with your NVIDIA RTX 4050/4060 or similar GPU.

## 📋 Prerequisites

### System Requirements
- **Windows 10/11** (tested)
- **Python 3.8+** (Python 3.10 recommended)
- **NVIDIA RTX 4050/4060** or similar GPU
- **8GB+ RAM** (16GB recommended)
- **5GB+ free disk space**

### NVIDIA Drivers
1. **Download NVIDIA drivers**: https://www.nvidia.com/drivers/
2. **Install the latest drivers** for your RTX 4050/4060
3. **Restart your computer** after installation

## 🛠️ Installation Methods

### Method 1: Automated Installation (Recommended)

```bash
# 1. Clone/download the repository
cd "V3.1 Removing files"

# 2. Run the automated GPU installer
python install_gpu_requirements.py
```

The script will:
- ✅ Check Python version compatibility
- ✅ Detect NVIDIA drivers and CUDA version
- ✅ Install base requirements
- ✅ Install CUDA-enabled PyTorch
- ✅ Verify GPU acceleration is working

### Method 2: Manual Installation

```bash
# 1. Install base requirements
pip install -r requirements.txt

# 2. Install CUDA-enabled PyTorch (choose your CUDA version)

# For CUDA 12.1 (recommended for RTX 4050/4060):
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# For CUDA 11.8 (alternative):
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 3. Verify CUDA installation
python -c "import torch; print('CUDA available:', torch.cuda.is_available())"
```

## 🧪 Testing the Installation

### 1. Verify GPU Detection
```bash
python -c "import torch; print('GPU:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'Not found')"
```

Expected output:
```
GPU: NVIDIA GeForce RTX 4050 Laptop GPU
```

### 2. Train the GPU Model
```bash
python train_gpu_model.py
```

Expected output:
```
🚀 TRAINING GPU MODEL WITH RTX 4050
✅ CUDA available: True
🎮 GPU device: NVIDIA GeForce RTX 4050 Laptop GPU
🎉 Training completed!
✅ Training successful!
   Accuracy: 94.5%
```

### 3. Run the Demo
```bash
python quick_system_demo.py
```

Look for:
- ✅ **CUDA available: Yes**
- ✅ **GPU model files found**
- ✅ **Method: GPU_ML** in test results

## 🔧 Troubleshooting

### CUDA Not Available
**Problem**: `CUDA available: False`

**Solutions**:
1. **Check NVIDIA drivers**: Run `nvidia-smi` in command prompt
2. **Reinstall PyTorch**: 
   ```bash
   pip uninstall torch torchvision torchaudio
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
   ```
3. **Restart Python environment** (close IDE/terminal and reopen)

### Installation Timeout
**Problem**: PyTorch installation times out

**Solutions**:
1. **Use --user flag**: 
   ```bash
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121 --user
   ```
2. **Increase timeout**: 
   ```bash
   pip install --timeout 1000 torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
   ```

### GPU Model Training Fails
**Problem**: Training script fails or shows low accuracy

**Solutions**:
1. **Check training data**: Ensure `colab_training_data.csv` exists
2. **Free GPU memory**: Close other GPU-intensive applications
3. **Check disk space**: Ensure 2GB+ free space for model files

### Pattern_Matching Instead of GPU_ML
**Problem**: Demo shows Pattern_Matching instead of GPU_ML

**This is normal!** The intelligent router chooses the best method:
- **Pattern_Matching**: For well-known transaction types (fast, reliable)
- **GPU_ML**: For complex transactions requiring deep learning

Both methods working together is the intended behavior.

## 📊 Performance Expectations

### Training Performance
- **RTX 4050**: ~3-5 minutes training time
- **RTX 4060**: ~2-4 minutes training time
- **CPU fallback**: ~15-30 minutes

### Inference Performance
- **GPU acceleration**: <50ms per transaction
- **Pattern matching**: <10ms per transaction
- **Target accuracy**: 94-96%

## 🎯 Next Steps

Once installation is complete:

1. **Train your model**: `python train_gpu_model.py`
2. **Test the system**: `python quick_system_demo.py`
3. **Process your data**: Use the main analyzer with GPU acceleration
4. **Monitor performance**: Check accuracy and retrain as needed

## 💡 Tips for Best Performance

1. **Keep drivers updated**: Check for NVIDIA driver updates monthly
2. **Monitor GPU memory**: Use Task Manager → Performance → GPU
3. **Regular retraining**: Retrain model when adding new transaction types
4. **Backup models**: Keep copies of trained models in `bank_analyzer_config/ml_models/backup_*`

## 📞 Support

If you encounter issues:

1. **Check logs**: Look in `logs/` directory for error details
2. **Run diagnostics**: `python diagnose_gpu_model.py`
3. **Verify installation**: `python fix_gpu_setup.py`

---

🎉 **Enjoy 95%+ accuracy with GPU-accelerated transaction categorization!** 🚀
