"""
AI Processing Window - Hybrid Categorisation System
Clean implementation for AI-based transaction processing
"""

from PySide6.QtWidgets import (
    QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QFileDialog, QTableWidget, QTableWidgetItem,
    QGroupBox, QTextEdit, QMessageBox, QProgressBar,
    QHeaderView, QStatusBar
)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QFont

import csv
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.logger import get_logger
from ..core.directory_manager import get_directory_manager


class AIProcessingThread(QThread):
    """Thread for AI processing in background"""
    
    progress_updated = Signal(int, str)  # progress percentage, status message
    processing_completed = Signal(list)  # processed_transactions
    error_occurred = Signal(str)  # error message
    
    def __init__(self, transactions: List[Dict[str, Any]]):
        super().__init__()
        self.transactions = transactions
        self.logger = get_logger(__name__)
    
    def run(self):
        """Run AI processing (placeholder implementation)"""
        try:
            self.progress_updated.emit(10, "Initializing AI processing...")
            
            # Placeholder for AI processing
            # In a real implementation, this would use trained ML models
            processed_transactions = []
            
            total_transactions = len(self.transactions)
            for i, txn in enumerate(self.transactions):
                progress = 10 + (i * 80) // total_transactions
                self.progress_updated.emit(progress, f"Processing transaction {i+1}/{total_transactions}")
                
                # Simple rule-based categorization as placeholder
                processed_txn = txn.copy()
                if not processed_txn.get('category'):
                    # Basic categorization logic
                    description = str(processed_txn.get('description', '')).lower()
                    amount = float(processed_txn.get('amount', 0))
                    
                    if amount > 0:  # Credit
                        if 'salary' in description:
                            processed_txn['category'] = 'Income'
                            processed_txn['subcategory'] = 'Salary'
                        else:
                            processed_txn['category'] = 'Income'
                            processed_txn['subcategory'] = 'Other Income'
                    else:  # Debit
                        if any(word in description for word in ['food', 'restaurant', 'cafe']):
                            processed_txn['category'] = 'Food & Dining'
                            processed_txn['subcategory'] = 'Restaurants'
                        elif any(word in description for word in ['fuel', 'petrol', 'gas']):
                            processed_txn['category'] = 'Transportation'
                            processed_txn['subcategory'] = 'Fuel'
                        else:
                            processed_txn['category'] = 'General'
                            processed_txn['subcategory'] = 'Other'
                
                processed_transactions.append(processed_txn)
            
            self.progress_updated.emit(100, "AI processing completed")
            self.processing_completed.emit(processed_transactions)
            
        except Exception as e:
            self.logger.error(f"AI processing failed: {str(e)}")
            self.error_occurred.emit(str(e))


class AIProcessingWindow(QMainWindow):
    """
    AI Processing Window - Hybrid Categorisation System
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # Data
        self.transactions = []
        self.processed_transactions = []
        self.csv_file_path = None
        self.processing_thread = None
        
        self.setup_ui()
        self.logger.info("AI Processing Window initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("Hybrid Categorisation System")
        self.setGeometry(100, 100, 1200, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Title
        title_label = QLabel("AI Processing - Hybrid Categorisation System")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # Load data section
        load_group = QGroupBox("Load Processed Statements")
        load_layout = QVBoxLayout(load_group)
        
        self.load_btn = QPushButton("Load CSV File")
        self.load_btn.clicked.connect(self.load_data)
        load_layout.addWidget(self.load_btn)
        
        self.status_label = QLabel("No data loaded")
        self.status_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        load_layout.addWidget(self.status_label)
        
        main_layout.addWidget(load_group)
        
        # Processing section
        processing_group = QGroupBox("AI Processing")
        processing_layout = QVBoxLayout(processing_group)
        
        self.process_btn = QPushButton("Start AI Categorization")
        self.process_btn.clicked.connect(self.start_ai_processing)
        self.process_btn.setEnabled(False)
        processing_layout.addWidget(self.process_btn)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        processing_layout.addWidget(self.progress_bar)
        
        # Progress label
        self.progress_label = QLabel()
        self.progress_label.setVisible(False)
        processing_layout.addWidget(self.progress_label)
        
        main_layout.addWidget(processing_group)
        
        # Results table
        results_group = QGroupBox("Processing Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(6)
        self.results_table.setHorizontalHeaderLabels([
            "Date", "Description", "Amount", "Category", "Subcategory", "Status"
        ])
        
        # Set up table headers with drag-to-resize
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setStretchLastSection(True)
        
        results_layout.addWidget(self.results_table)
        
        # Save results
        self.save_btn = QPushButton("Save Results")
        self.save_btn.clicked.connect(self.save_results)
        self.save_btn.setEnabled(False)
        results_layout.addWidget(self.save_btn)
        
        main_layout.addWidget(results_group)
        
        # Import function placeholder
        import_group = QGroupBox("Import Function")
        import_layout = QVBoxLayout(import_group)
        
        import_label = QLabel("Import function placeholder - Future implementation")
        import_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        import_layout.addWidget(import_label)
        
        main_layout.addWidget(import_group)
        
        # Create status bar
        self.create_status_bar()
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Load data to begin AI processing")
    
    def load_data(self):
        """Load data from CSV file"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setNameFilter("CSV Files (*.csv);;All Files (*)")
        
        if file_dialog.exec():
            file_path = Path(file_dialog.selectedFiles()[0])
            self.load_csv_file(file_path)
    
    def load_csv_file(self, file_path: Path):
        """Load transactions from CSV file"""
        try:
            self.transactions = []
            
            with open(file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    transaction = {
                        'date': row.get('date', ''),
                        'description': row.get('description', ''),
                        'amount': float(row.get('amount', 0)),
                        'bank_name': row.get('bank_name', ''),
                        'source_file': row.get('source_file', ''),
                        'category': row.get('category', ''),
                        'subcategory': row.get('subcategory', '')
                    }
                    self.transactions.append(transaction)
            
            self.csv_file_path = file_path
            self.process_btn.setEnabled(True)
            
            self.status_label.setText(f"Loaded {len(self.transactions)} transactions from {file_path.name}")
            self.status_bar.showMessage(f"Loaded {len(self.transactions)} transactions")
            
            self.logger.info(f"Loaded {len(self.transactions)} transactions from {file_path}")
            
        except Exception as e:
            self.logger.error(f"Error loading CSV file: {str(e)}")
            QMessageBox.critical(self, "Load Error", f"Failed to load CSV file:\n{str(e)}")
    
    def start_ai_processing(self):
        """Start AI processing"""
        if not self.transactions:
            QMessageBox.warning(self, "No Data", "Please load data first.")
            return
        
        # Start processing in background thread
        self.processing_thread = AIProcessingThread(self.transactions)
        self.processing_thread.progress_updated.connect(self.on_processing_progress)
        self.processing_thread.processing_completed.connect(self.on_processing_completed)
        self.processing_thread.error_occurred.connect(self.on_processing_error)
        
        # Update UI
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        self.process_btn.setEnabled(False)
        self.status_bar.showMessage("AI processing in progress...")
        
        # Start thread
        self.processing_thread.start()
    
    def on_processing_progress(self, percentage: int, message: str):
        """Handle processing progress updates"""
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(message)
        self.status_bar.showMessage(message)
    
    def on_processing_completed(self, processed_transactions: List[Dict[str, Any]]):
        """Handle processing completion"""
        self.processed_transactions = processed_transactions
        
        # Update UI
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        self.process_btn.setEnabled(True)
        self.save_btn.setEnabled(True)
        
        # Populate results table
        self.populate_results_table()
        
        # Show completion message
        QMessageBox.information(
            self, "Processing Complete",
            f"Successfully processed {len(processed_transactions)} transactions using AI."
        )
        
        self.status_bar.showMessage(f"AI processing completed - {len(processed_transactions)} transactions processed")
    
    def on_processing_error(self, error_message: str):
        """Handle processing errors"""
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        self.process_btn.setEnabled(True)
        
        self.status_bar.showMessage("AI processing failed")
        QMessageBox.critical(self, "Processing Error", f"AI processing failed:\n\n{error_message}")
    
    def populate_results_table(self):
        """Populate the results table"""
        self.results_table.setRowCount(len(self.processed_transactions))
        
        for row, txn in enumerate(self.processed_transactions):
            self.results_table.setItem(row, 0, QTableWidgetItem(str(txn['date'])))
            self.results_table.setItem(row, 1, QTableWidgetItem(str(txn['description'])))
            self.results_table.setItem(row, 2, QTableWidgetItem(f"{txn['amount']:.2f}"))
            self.results_table.setItem(row, 3, QTableWidgetItem(str(txn['category'])))
            self.results_table.setItem(row, 4, QTableWidgetItem(str(txn['subcategory'])))
            
            # Determine status
            status = "AI Categorized" if txn.get('category') else "Uncategorized"
            self.results_table.setItem(row, 5, QTableWidgetItem(status))
    
    def save_results(self):
        """Save the processed results"""
        if not self.processed_transactions:
            QMessageBox.warning(self, "No Results", "No processed results to save.")
            return
        
        # Use organized directory structure for AI categorized data
        try:
            dir_manager = get_directory_manager()

            # Determine bank name from transactions
            bank_name = "Unknown"
            if self.processed_transactions and self.processed_transactions[0].get('bank_name'):
                bank_name = self.processed_transactions[0]['bank_name']

            # Get file path for AI categorized data
            file_path = dir_manager.get_categorized_file('ai', bank_name)
            self.save_csv_file(file_path)

        except Exception as e:
            self.logger.error(f"Error getting AI categorized file path: {str(e)}")
            # Fallback to user selection
            file_dialog = QFileDialog(self)
            file_dialog.setAcceptMode(QFileDialog.AcceptSave)
            file_dialog.setNameFilter("CSV Files (*.csv)")
            file_dialog.setDefaultSuffix("csv")

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_dialog.selectFile(f"ai_processed_transactions_{timestamp}.csv")

            if file_dialog.exec():
                file_path = Path(file_dialog.selectedFiles()[0])
                self.save_csv_file(file_path)
    
    def save_csv_file(self, file_path: Path):
        """Save processed transactions to CSV file"""
        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['date', 'description', 'amount', 'transaction_type', 'bank_name', 'source_file', 'category', 'subcategory']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for txn in self.processed_transactions:
                    writer.writerow({
                        'date': txn['date'],
                        'description': txn['description'],
                        'amount': txn['amount'],
                        'transaction_type': txn.get('transaction_type', ''),
                        'bank_name': txn['bank_name'],
                        'source_file': txn['source_file'],
                        'category': txn['category'],
                        'subcategory': txn['subcategory']
                    })
            
            QMessageBox.information(self, "Save Complete", f"Results saved to {file_path}")
            self.status_bar.showMessage("Results saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving results: {str(e)}")
            QMessageBox.critical(self, "Save Error", f"Failed to save results:\n{str(e)}")
