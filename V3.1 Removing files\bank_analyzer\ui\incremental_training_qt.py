"""
Incremental ML Training Interface
Provides UI for incremental training with clear distinction between existing and new data
"""

import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QProgressBar, QTextEdit, QGroupBox, QGridLayout, QFrame,
    QMessageBox, QDialog, QDialogButtonBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QSplitter, QTabWidget
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QColor, QPalette

# Add the parent directory to the path to import bank_analyzer modules
current_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(current_dir))

from bank_analyzer.ml.incremental_trainer import IncrementalMLTrainer, IncrementalTrainingResult
from bank_analyzer.ml.incremental_data_manager import IncrementalDataManager, IncrementalDataSummary
from bank_analyzer.core.logger import get_logger


class IncrementalTrainingWorker(QThread):
    """Worker thread for incremental training"""
    progress_updated = Signal(str)
    training_completed = Signal(object)  # IncrementalTrainingResult
    error_occurred = Signal(str)
    
    def __init__(self, trainer: IncrementalMLTrainer, new_transactions: List[Dict]):
        super().__init__()
        self.trainer = trainer
        self.new_transactions = new_transactions
    
    def run(self):
        try:
            self.progress_updated.emit("Starting incremental training...")
            result = self.trainer.train_incremental_model(self.new_transactions)
            self.training_completed.emit(result)
        except Exception as e:
            self.error_occurred.emit(str(e))


class DataSummaryWidget(QGroupBox):
    """Widget to display data summary with visual distinction"""
    
    def __init__(self, title: str):
        super().__init__(title)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QGridLayout()
        
        # Create labels
        self.total_label = QLabel("0")
        self.labeled_label = QLabel("0")
        self.categories_label = QLabel("0")
        self.last_updated_label = QLabel("Never")
        
        # Style labels
        font = QFont()
        font.setBold(True)
        font.setPointSize(12)
        
        self.total_label.setFont(font)
        self.labeled_label.setFont(font)
        self.categories_label.setFont(font)
        
        # Add to layout
        layout.addWidget(QLabel("Total Transactions:"), 0, 0)
        layout.addWidget(self.total_label, 0, 1)
        layout.addWidget(QLabel("Labeled Transactions:"), 1, 0)
        layout.addWidget(self.labeled_label, 1, 1)
        layout.addWidget(QLabel("Categories:"), 2, 0)
        layout.addWidget(self.categories_label, 2, 1)
        layout.addWidget(QLabel("Last Updated:"), 3, 0)
        layout.addWidget(self.last_updated_label, 3, 1)
        
        self.setLayout(layout)
    
    def update_summary(self, summary: IncrementalDataSummary):
        """Update the summary display"""
        self.total_label.setText(str(summary.total_transactions))
        self.labeled_label.setText(str(summary.labeled_transactions))
        self.categories_label.setText(str(len(summary.categories)))
        
        if summary.latest_version:
            timestamp = datetime.fromisoformat(summary.latest_version.timestamp)
            self.last_updated_label.setText(timestamp.strftime("%Y-%m-%d %H:%M"))
        else:
            self.last_updated_label.setText("Never")
        
        # Color coding
        if summary.can_do_incremental:
            self.setStyleSheet("QGroupBox { color: #2e7d32; }")
        else:
            self.setStyleSheet("QGroupBox { color: #d32f2f; }")


class TrainingResultsWidget(QWidget):
    """Widget to display training results"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Results text area
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setMaximumHeight(200)
        
        layout.addWidget(QLabel("Training Results:"))
        layout.addWidget(self.results_text)
        
        self.setLayout(layout)
    
    def display_results(self, result: IncrementalTrainingResult):
        """Display training results"""
        if result.success:
            text = f"✅ Training Successful!\n\n"
            text += f"Message: {result.message}\n\n"
            
            if result.session:
                text += f"Session ID: {result.session.session_id}\n"
                text += f"Transactions Processed: {result.session.transactions_processed}\n"
                text += f"New Labels Added: {result.session.new_labels_added}\n"
                text += f"Categories Learned: {len(result.session.categories_learned)}\n\n"
            
            text += f"Performance Metrics:\n"
            for metric, value in result.performance_metrics.items():
                if isinstance(value, float):
                    text += f"  • {metric}: {value:.3f}\n"
                else:
                    text += f"  • {metric}: {value}\n"
            
            text += f"\nCategories Before: {len(result.categories_before)}\n"
            text += f"Categories After: {len(result.categories_after)}\n"
            text += f"New Categories: {result.new_categories}\n"
            
            self.results_text.setStyleSheet("QTextEdit { background-color: #e8f5e8; }")
        else:
            text = f"❌ Training Failed!\n\n"
            text += f"Error: {result.message}\n"
            
            self.results_text.setStyleSheet("QTextEdit { background-color: #ffeaea; }")
        
        self.results_text.setText(text)


class IncrementalTrainingDialog(QDialog):
    """Main dialog for incremental training"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.trainer = IncrementalMLTrainer()
        self.data_manager = IncrementalDataManager()
        self.worker = None
        
        # Data
        self.new_transactions = []
        self.current_summary = None
        
        self.setup_ui()
        self.refresh_data_summary()
        
        # Set window properties
        self.setWindowTitle("Incremental ML Training System")
        self.setModal(True)
        self.resize(800, 600)
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("🤖 Incremental ML Training System")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(16)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Create tabs
        tab_widget = QTabWidget()
        
        # Data Overview Tab
        overview_tab = self.create_overview_tab()
        tab_widget.addTab(overview_tab, "📊 Data Overview")
        
        # Training Tab
        training_tab = self.create_training_tab()
        tab_widget.addTab(training_tab, "🎯 Training")
        
        # History Tab
        history_tab = self.create_history_tab()
        tab_widget.addTab(history_tab, "📈 History")
        
        layout.addWidget(tab_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 Refresh Data")
        self.refresh_btn.clicked.connect(self.refresh_data_summary)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_overview_tab(self) -> QWidget:
        """Create the data overview tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Current data summary
        self.existing_data_widget = DataSummaryWidget("📚 Existing Training Data")
        layout.addWidget(self.existing_data_widget)
        
        # New data summary (initially empty)
        self.new_data_widget = DataSummaryWidget("🆕 New Data to Add")
        layout.addWidget(self.new_data_widget)
        
        # Incremental analysis
        self.analysis_group = QGroupBox("🔍 Incremental Training Analysis")
        analysis_layout = QVBoxLayout()
        
        self.analysis_text = QTextEdit()
        self.analysis_text.setReadOnly(True)
        self.analysis_text.setMaximumHeight(150)
        analysis_layout.addWidget(self.analysis_text)
        
        self.analysis_group.setLayout(analysis_layout)
        layout.addWidget(self.analysis_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_training_tab(self) -> QWidget:
        """Create the training tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Training controls
        controls_group = QGroupBox("🎯 Training Controls")
        controls_layout = QVBoxLayout()
        
        # Load new data button
        self.load_data_btn = QPushButton("📁 Load New Labeled Data")
        self.load_data_btn.clicked.connect(self.load_new_data)
        controls_layout.addWidget(self.load_data_btn)
        
        # Start training button
        self.start_training_btn = QPushButton("🚀 Start Incremental Training")
        self.start_training_btn.clicked.connect(self.start_training)
        self.start_training_btn.setEnabled(False)
        controls_layout.addWidget(self.start_training_btn)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        controls_layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready to load new data")
        controls_layout.addWidget(self.status_label)
        
        controls_group.setLayout(controls_layout)
        layout.addWidget(controls_group)
        
        # Training results
        self.results_widget = TrainingResultsWidget()
        layout.addWidget(self.results_widget)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_history_tab(self) -> QWidget:
        """Create the training history tab"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # History table
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "Session ID", "Timestamp", "Transactions", "New Labels", "Categories", "Accuracy"
        ])
        
        # Make table read-only
        self.history_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.history_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Resize columns
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(QLabel("📈 Training History"))
        layout.addWidget(self.history_table)
        
        widget.setLayout(layout)
        return widget

    def refresh_data_summary(self):
        """Refresh the data summary display"""
        try:
            self.current_summary = self.data_manager.get_data_summary()
            self.existing_data_widget.update_summary(self.current_summary)

            # Update analysis
            if self.new_transactions:
                analysis = self.data_manager.analyze_new_data_for_incremental(self.new_transactions)
                self.update_analysis_display(analysis)
            else:
                self.analysis_text.setText("No new data loaded for analysis.")

            # Update history
            self.refresh_training_history()

            self.logger.info(f"Data summary refreshed: {self.current_summary.total_transactions} total, {self.current_summary.labeled_transactions} labeled")

        except Exception as e:
            self.logger.error(f"Error refreshing data summary: {str(e)}")
            QMessageBox.warning(self, "Error", f"Failed to refresh data summary: {str(e)}")

    def load_new_data(self):
        """Load new labeled data for incremental training"""
        # For now, this is a placeholder - in real implementation, this would
        # integrate with the ML labeling system to get newly labeled transactions

        QMessageBox.information(
            self,
            "Load New Data",
            "This feature will integrate with the ML Labeling system to load newly labeled transactions.\n\n"
            "For testing, you can use the ML Labeling interface to label transactions, "
            "then return here to perform incremental training."
        )

        # TODO: Integrate with ML labeling system
        # self.new_transactions = get_newly_labeled_transactions()
        # self.update_new_data_display()

    def update_new_data_display(self):
        """Update the new data display"""
        if self.new_transactions:
            # Create a mock summary for new data
            new_summary = IncrementalDataSummary(
                total_transactions=len(self.new_transactions),
                labeled_transactions=len([t for t in self.new_transactions if t.get('is_manually_labeled', False)]),
                unlabeled_transactions=0,
                categories=list(set(t.get('category', '') for t in self.new_transactions if t.get('category'))),
                subcategories=[],
                latest_version=None,
                has_existing_data=True,
                can_do_incremental=True
            )

            self.new_data_widget.update_summary(new_summary)
            self.start_training_btn.setEnabled(True)

            # Update analysis
            if self.current_summary:
                analysis = self.data_manager.analyze_new_data_for_incremental(self.new_transactions)
                self.update_analysis_display(analysis)

    def update_analysis_display(self, analysis: Dict[str, Any]):
        """Update the incremental analysis display"""
        text = "🔍 Incremental Training Analysis\n\n"

        existing = analysis["existing_data"]
        new = analysis["new_data"]
        incremental = analysis["incremental_analysis"]

        text += f"📚 Existing Data:\n"
        text += f"  • Total Transactions: {existing['total_transactions']}\n"
        text += f"  • Labeled Transactions: {existing['labeled_transactions']}\n"
        text += f"  • Categories: {len(existing['categories'])}\n\n"

        text += f"🆕 New Data:\n"
        text += f"  • Total Transactions: {new['total_transactions']}\n"
        text += f"  • Labeled Transactions: {new['labeled_transactions']}\n"
        text += f"  • Categories: {len(new['categories'])}\n\n"

        text += f"🎯 Incremental Analysis:\n"
        text += f"  • Can Do Incremental: {'✅ Yes' if incremental['can_do_incremental'] else '❌ No'}\n"
        text += f"  • Recommended: {'✅ Yes' if incremental['is_recommended'] else '❌ No'}\n"
        text += f"  • New Categories: {len(incremental['new_categories'])}\n"
        text += f"  • Overlapping Categories: {len(incremental['overlapping_categories'])}\n\n"

        if analysis["recommendations"]:
            text += f"💡 Recommendations:\n"
            for rec in analysis["recommendations"]:
                text += f"  • {rec}\n"

        self.analysis_text.setText(text)

    def start_training(self):
        """Start incremental training"""
        if not self.new_transactions:
            QMessageBox.warning(self, "No Data", "Please load new labeled data first.")
            return

        # Confirm training
        reply = QMessageBox.question(
            self,
            "Start Training",
            f"Start incremental training with {len(self.new_transactions)} new transactions?\n\n"
            "This will:\n"
            "• Retain existing knowledge\n"
            "• Learn new patterns\n"
            "• Update the ML model\n\n"
            "Continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return

        # Start training in worker thread
        self.start_training_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.status_label.setText("Training in progress...")

        self.worker = IncrementalTrainingWorker(self.trainer, self.new_transactions)
        self.worker.progress_updated.connect(self.update_training_progress)
        self.worker.training_completed.connect(self.training_completed)
        self.worker.error_occurred.connect(self.training_error)
        self.worker.start()

    def update_training_progress(self, message: str):
        """Update training progress"""
        self.status_label.setText(message)

    def training_completed(self, result: IncrementalTrainingResult):
        """Handle training completion"""
        self.progress_bar.setVisible(False)
        self.start_training_btn.setEnabled(True)

        if result.success:
            self.status_label.setText("✅ Training completed successfully!")

            # Display results
            self.results_widget.display_results(result)

            # Refresh data summary
            self.refresh_data_summary()

            # Clear new data
            self.new_transactions = []

            QMessageBox.information(
                self,
                "Training Complete",
                f"Incremental training completed successfully!\n\n"
                f"• {result.session.new_labels_added} new labels added\n"
                f"• {len(result.categories_after)} total categories learned\n"
                f"• Model accuracy: {result.performance_metrics.get('category_accuracy', 0):.3f}"
            )
        else:
            self.status_label.setText("❌ Training failed!")
            self.results_widget.display_results(result)

            QMessageBox.critical(
                self,
                "Training Failed",
                f"Incremental training failed:\n\n{result.message}"
            )

    def training_error(self, error_message: str):
        """Handle training error"""
        self.progress_bar.setVisible(False)
        self.start_training_btn.setEnabled(True)
        self.status_label.setText("❌ Training error occurred!")

        QMessageBox.critical(
            self,
            "Training Error",
            f"An error occurred during training:\n\n{error_message}"
        )

    def refresh_training_history(self):
        """Refresh the training history table"""
        try:
            history = self.trainer.get_training_history()
            sessions = history.get("training_sessions", [])

            self.history_table.setRowCount(len(sessions))

            for row, session in enumerate(sessions):
                self.history_table.setItem(row, 0, QTableWidgetItem(session.get("session_id", "")))

                timestamp = session.get("timestamp", "")
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp)
                        timestamp = dt.strftime("%Y-%m-%d %H:%M")
                    except:
                        pass
                self.history_table.setItem(row, 1, QTableWidgetItem(timestamp))

                self.history_table.setItem(row, 2, QTableWidgetItem(str(session.get("transactions_processed", 0))))
                self.history_table.setItem(row, 3, QTableWidgetItem(str(session.get("new_labels_added", 0))))
                self.history_table.setItem(row, 4, QTableWidgetItem(str(len(session.get("categories_learned", [])))))

                accuracy = session.get("performance_metrics", {}).get("category_accuracy", 0)
                if isinstance(accuracy, (int, float)):
                    accuracy_str = f"{accuracy:.3f}"
                else:
                    accuracy_str = str(accuracy)
                self.history_table.setItem(row, 5, QTableWidgetItem(accuracy_str))

        except Exception as e:
            self.logger.error(f"Error refreshing training history: {str(e)}")


def show_incremental_training_dialog(parent=None):
    """Show the incremental training dialog"""
    dialog = IncrementalTrainingDialog(parent)
    return dialog.exec()
