"""
Incremental ML Training System
Handles incremental learning while retaining previous knowledge
"""

import json
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import joblib
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score

from ..core.logger import get_logger


@dataclass
class TrainingSession:
    """Represents a single training session"""
    session_id: str
    timestamp: str
    transactions_processed: int
    new_labels_added: int
    existing_labels_updated: int
    model_retrained: bool
    performance_metrics: Dict[str, float]
    categories_learned: List[str]
    data_version: str


@dataclass
class IncrementalTrainingResult:
    """Result of incremental training"""
    success: bool
    session: Optional[TrainingSession]
    message: str
    performance_metrics: Dict[str, float]
    categories_before: List[str]
    categories_after: List[str]
    new_categories: List[str]


class IncrementalMLTrainer:
    """
    Incremental ML Training System
    
    Features:
    - Retains knowledge from previous training sessions
    - Learns new transaction patterns without forgetting old ones
    - Tracks training history and data versioning
    - Provides clear feedback on incremental vs fresh training
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        self.ml_data_dir = self.config_dir / "ml_data"
        self.ml_models_dir = self.config_dir / "ml_models"
        
        # Ensure directories exist
        self.ml_data_dir.mkdir(parents=True, exist_ok=True)
        self.ml_models_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.training_data_file = self.ml_data_dir / "unique_transactions.csv"
        self.training_history_file = self.ml_data_dir / "training_history.json"
        self.category_model_file = self.ml_models_dir / "category_model.joblib"
        self.subcategory_model_file = self.ml_models_dir / "subcategory_model.joblib"
        self.label_encoders_file = self.ml_models_dir / "label_encoders.joblib"
        
        # Initialize training history
        self._ensure_training_history()
    
    def _ensure_training_history(self):
        """Ensure training history file exists"""
        if not self.training_history_file.exists():
            history = {
                "version": "2.0",
                "created_at": datetime.now().isoformat(),
                "training_sessions": [],
                "total_sessions": 0,
                "total_labeled_transactions": 0,
                "last_training_date": None,
                "model_performance": {
                    "category_accuracy": None,
                    "subcategory_accuracy": None,
                    "last_evaluation": None
                },
                "incremental_training": {
                    "enabled": True,
                    "data_versioning": True,
                    "retain_previous_knowledge": True
                }
            }
            
            with open(self.training_history_file, 'w') as f:
                json.dump(history, f, indent=2)
    
    def get_training_history(self) -> Dict[str, Any]:
        """Get current training history"""
        try:
            with open(self.training_history_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Error reading training history: {str(e)}")
            return {}
    
    def get_existing_training_data(self) -> Tuple[pd.DataFrame, bool]:
        """
        Get existing training data
        
        Returns:
            Tuple of (dataframe, has_existing_data)
        """
        try:
            if self.training_data_file.exists():
                df = pd.read_csv(self.training_data_file)
                has_data = len(df) > 0
                self.logger.info(f"Found existing training data: {len(df)} transactions")
                return df, has_data
            else:
                self.logger.info("No existing training data found")
                return pd.DataFrame(), False
        except Exception as e:
            self.logger.error(f"Error reading training data: {str(e)}")
            return pd.DataFrame(), False
    
    def analyze_new_data(self, new_transactions: List[Dict]) -> Dict[str, Any]:
        """
        Analyze new transaction data for incremental training
        
        Args:
            new_transactions: List of new transaction dictionaries
            
        Returns:
            Analysis results
        """
        existing_df, has_existing = self.get_existing_training_data()
        
        analysis = {
            "total_new_transactions": len(new_transactions),
            "has_existing_data": has_existing,
            "existing_transaction_count": len(existing_df) if has_existing else 0,
            "new_categories": [],
            "existing_categories": [],
            "category_overlap": [],
            "is_incremental": has_existing
        }
        
        if has_existing:
            existing_categories = set(existing_df['category'].dropna().unique())
            analysis["existing_categories"] = list(existing_categories)
        else:
            existing_categories = set()
        
        # Analyze new data
        new_df = pd.DataFrame(new_transactions)
        if 'category' in new_df.columns:
            new_categories = set(new_df['category'].dropna().unique())
            analysis["new_categories"] = list(new_categories)
            analysis["category_overlap"] = list(existing_categories.intersection(new_categories))
        
        return analysis
    
    def prepare_incremental_training_data(self, new_transactions: List[Dict]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Prepare data for incremental training
        
        Args:
            new_transactions: New transaction data
            
        Returns:
            Tuple of (combined_dataframe, preparation_info)
        """
        existing_df, has_existing = self.get_existing_training_data()
        new_df = pd.DataFrame(new_transactions)
        
        preparation_info = {
            "existing_count": len(existing_df) if has_existing else 0,
            "new_count": len(new_df),
            "total_count": 0,
            "is_incremental": has_existing
        }
        
        if has_existing:
            # Combine existing and new data
            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            self.logger.info(f"Incremental training: {len(existing_df)} existing + {len(new_df)} new = {len(combined_df)} total")
        else:
            # Fresh training
            combined_df = new_df
            self.logger.info(f"Fresh training: {len(new_df)} transactions")
        
        preparation_info["total_count"] = len(combined_df)
        
        return combined_df, preparation_info
    
    def train_incremental_model(self, new_transactions: List[Dict]) -> IncrementalTrainingResult:
        """
        Perform incremental training
        
        Args:
            new_transactions: New labeled transaction data
            
        Returns:
            Training result
        """
        try:
            # Analyze the data
            analysis = self.analyze_new_data(new_transactions)
            
            # Prepare training data
            combined_df, prep_info = self.prepare_incremental_training_data(new_transactions)
            
            if len(combined_df) == 0:
                return IncrementalTrainingResult(
                    success=False,
                    session=None,
                    message="No training data available",
                    performance_metrics={},
                    categories_before=[],
                    categories_after=[],
                    new_categories=[]
                )
            
            # Filter labeled data
            labeled_df = combined_df[
                (combined_df['category'].notna()) & 
                (combined_df['category'] != '') &
                (combined_df['is_manually_labeled'] == True)
            ].copy()
            
            if len(labeled_df) < 2:
                return IncrementalTrainingResult(
                    success=False,
                    session=None,
                    message="Insufficient labeled data for training (need at least 2 samples)",
                    performance_metrics={},
                    categories_before=analysis.get("existing_categories", []),
                    categories_after=[],
                    new_categories=analysis.get("new_categories", [])
                )
            
            # Train the models
            performance_metrics = self._train_models(labeled_df)
            
            # Save updated training data
            combined_df.to_csv(self.training_data_file, index=False)
            
            # Create training session record
            session = TrainingSession(
                session_id=f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                timestamp=datetime.now().isoformat(),
                transactions_processed=len(combined_df),
                new_labels_added=len(new_transactions),
                existing_labels_updated=0,  # TODO: Track updates
                model_retrained=True,
                performance_metrics=performance_metrics,
                categories_learned=list(labeled_df['category'].unique()),
                data_version="2.0"
            )
            
            # Update training history
            self._update_training_history(session)
            
            return IncrementalTrainingResult(
                success=True,
                session=session,
                message=f"Incremental training completed successfully. Processed {len(new_transactions)} new transactions.",
                performance_metrics=performance_metrics,
                categories_before=analysis.get("existing_categories", []),
                categories_after=list(labeled_df['category'].unique()),
                new_categories=analysis.get("new_categories", [])
            )
            
        except Exception as e:
            self.logger.error(f"Error in incremental training: {str(e)}", exc_info=True)
            return IncrementalTrainingResult(
                success=False,
                session=None,
                message=f"Training failed: {str(e)}",
                performance_metrics={},
                categories_before=[],
                categories_after=[],
                new_categories=[]
            )
    
    def _train_models(self, labeled_df: pd.DataFrame) -> Dict[str, float]:
        """Train the ML models"""
        # Prepare features
        features = self._prepare_features(labeled_df)
        
        # Prepare labels
        categories = labeled_df['category'].values
        subcategories = labeled_df['sub_category'].fillna('').values
        
        # Initialize or load label encoders
        label_encoders = self._get_or_create_label_encoders(categories, subcategories)
        
        # Encode labels
        encoded_categories = label_encoders['category'].transform(categories)
        encoded_subcategories = label_encoders['subcategory'].transform(subcategories)
        
        # Split data with adaptive strategy based on data size
        unique_categories, category_counts = np.unique(encoded_categories, return_counts=True)
        min_category_count = np.min(category_counts)
        num_categories = len(unique_categories)

        if len(features) < 20 or num_categories > len(features) * 0.3:
            # For small datasets or when we have too many categories relative to samples,
            # use all data for training and testing
            X_train = X_test = features
            y_cat_train = y_cat_test = encoded_categories
            y_sub_train = y_sub_test = encoded_subcategories
            self.logger.info(f"Small dataset ({len(features)} samples, {num_categories} categories): Using all data for both training and testing")
        else:
            # Calculate appropriate test size
            # Ensure test set has at least 1 sample per category, but not more than 30% of data
            min_test_size = max(num_categories, 2)  # At least 1 per category or 2 minimum
            max_test_size = max(int(len(features) * 0.3), min_test_size)
            test_size = min(min_test_size, max_test_size)

            # Convert to fraction if needed
            if test_size >= len(features):
                # Use all data for both training and testing
                X_train = X_test = features
                y_cat_train = y_cat_test = encoded_categories
                y_sub_train = y_sub_test = encoded_subcategories
                self.logger.info(f"Test size would be too large: Using all data for both training and testing")
            else:
                test_size_fraction = test_size / len(features)

                if min_category_count < 2:
                    # Cannot stratify - use simple random split
                    X_train, X_test, y_cat_train, y_cat_test, y_sub_train, y_sub_test = train_test_split(
                        features, encoded_categories, encoded_subcategories,
                        test_size=test_size_fraction, random_state=42, stratify=None
                    )
                    self.logger.info(f"Cannot stratify (min category count: {min_category_count}): Using random split with test_size={test_size_fraction:.2f}")
                else:
                    # Can stratify safely
                    X_train, X_test, y_cat_train, y_cat_test, y_sub_train, y_sub_test = train_test_split(
                        features, encoded_categories, encoded_subcategories,
                        test_size=test_size_fraction, random_state=42, stratify=encoded_categories
                    )
                    self.logger.info(f"Using stratified split with {num_categories} categories, test_size={test_size_fraction:.2f}")
        
        # Train category model
        cat_model = RandomForestClassifier(n_estimators=100, random_state=42)
        cat_model.fit(X_train, y_cat_train)
        
        # Train subcategory model
        sub_model = RandomForestClassifier(n_estimators=100, random_state=42)
        sub_model.fit(X_train, y_sub_train)
        
        # Evaluate models
        cat_pred = cat_model.predict(X_test)
        sub_pred = sub_model.predict(X_test)

        # Calculate accuracy (handle case where test set might be same as training set)
        cat_accuracy = accuracy_score(y_cat_test, cat_pred)
        sub_accuracy = accuracy_score(y_sub_test, sub_pred)

        # For very small datasets, also evaluate on training set for comparison
        if len(features) < 10:
            cat_train_pred = cat_model.predict(X_train)
            sub_train_pred = sub_model.predict(X_train)
            cat_train_accuracy = accuracy_score(y_cat_train, cat_train_pred)
            sub_train_accuracy = accuracy_score(y_sub_train, sub_train_pred)

            self.logger.info(f"Small dataset evaluation - Train accuracy: {cat_train_accuracy:.3f}, Test accuracy: {cat_accuracy:.3f}")

            # Use training accuracy if test accuracy is unreliable due to small test set
            if len(X_test) < 3:
                cat_accuracy = cat_train_accuracy
                sub_accuracy = sub_train_accuracy
        
        # Save models
        joblib.dump(cat_model, self.category_model_file)
        joblib.dump(sub_model, self.subcategory_model_file)
        joblib.dump(label_encoders, self.label_encoders_file)
        
        return {
            "category_accuracy": cat_accuracy,
            "subcategory_accuracy": sub_accuracy,
            "training_samples": len(X_train),
            "test_samples": len(X_test)
        }
    
    def _prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """Prepare features for training"""
        # Simple feature extraction for now
        # TODO: Implement more sophisticated feature engineering
        features = []
        
        for _, row in df.iterrows():
            desc = str(row.get('normalized_description', '')).lower()
            amount = float(row.get('max_amount', 0))
            
            # Basic features
            feature_vector = [
                len(desc),  # Description length
                amount,     # Transaction amount
                1 if 'upi' in desc else 0,  # UPI transaction
                1 if 'atm' in desc else 0,  # ATM transaction
                1 if 'transfer' in desc else 0,  # Transfer
                1 if row.get('transaction_types') == 'credit' else 0,  # Credit transaction
            ]
            
            features.append(feature_vector)
        
        return np.array(features)
    
    def _get_or_create_label_encoders(self, categories, subcategories) -> Dict[str, LabelEncoder]:
        """Get existing or create new label encoders"""
        if self.label_encoders_file.exists():
            try:
                encoders = joblib.load(self.label_encoders_file)
                
                # Update encoders with new labels
                all_categories = list(set(list(encoders['category'].classes_) + list(categories)))
                all_subcategories = list(set(list(encoders['subcategory'].classes_) + list(subcategories)))
                
                encoders['category'].classes_ = np.array(all_categories)
                encoders['subcategory'].classes_ = np.array(all_subcategories)
                
                return encoders
            except Exception as e:
                self.logger.warning(f"Could not load existing encoders: {e}")
        
        # Create new encoders
        cat_encoder = LabelEncoder()
        sub_encoder = LabelEncoder()
        
        cat_encoder.fit(categories)
        sub_encoder.fit(subcategories)
        
        return {
            'category': cat_encoder,
            'subcategory': sub_encoder
        }
    
    def _update_training_history(self, session: TrainingSession):
        """Update training history with new session"""
        history = self.get_training_history()
        
        # Add new session
        history['training_sessions'].append({
            'session_id': session.session_id,
            'timestamp': session.timestamp,
            'transactions_processed': session.transactions_processed,
            'new_labels_added': session.new_labels_added,
            'existing_labels_updated': session.existing_labels_updated,
            'model_retrained': session.model_retrained,
            'performance_metrics': session.performance_metrics,
            'categories_learned': session.categories_learned,
            'data_version': session.data_version
        })
        
        # Update summary
        history['total_sessions'] += 1
        history['total_labeled_transactions'] += session.new_labels_added
        history['last_training_date'] = session.timestamp
        history['model_performance'] = {
            'category_accuracy': session.performance_metrics.get('category_accuracy'),
            'subcategory_accuracy': session.performance_metrics.get('subcategory_accuracy'),
            'last_evaluation': session.timestamp
        }
        
        # Save updated history
        with open(self.training_history_file, 'w') as f:
            json.dump(history, f, indent=2)
