"""
Advanced Feature Engineering for ML Model Performance Improvement

This module implements sophisticated feature engineering techniques to address
the current 89.7% category accuracy and 83.2% subcategory accuracy issues.
"""

import re
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from dataclasses import dataclass

from .enhanced_upi_feature_extractor import EnhancedUPIFeatureExtractor, UPIComponents

@dataclass
class TransactionFeatures:
    """Comprehensive feature set for a transaction"""
    # Basic features
    description_length: int = 0
    word_count: int = 0
    amount: float = 0.0
    
    # UPI features
    upi_features: Dict[str, Any] = None
    
    # Temporal features
    day_of_week: int = 0
    hour_of_day: int = 0
    is_weekend: bool = False
    is_business_hours: bool = False
    
    # Amount-based features
    amount_category: str = ""
    is_round_amount: bool = False
    amount_frequency_score: float = 0.0
    
    # Text features
    has_numbers: bool = False
    has_special_chars: bool = False
    merchant_confidence: float = 0.0
    
    # Pattern features
    transaction_pattern: str = ""
    frequency_score: float = 0.0
    
    # Category hints
    category_hints: List[Tuple[str, str, float]] = None

class AdvancedFeatureEngineer:
    """Advanced feature engineering for transaction categorization"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.upi_extractor = EnhancedUPIFeatureExtractor()
        self._initialize_patterns()
        self._initialize_amount_patterns()
        self._initialize_temporal_patterns()
    
    def _initialize_patterns(self):
        """Initialize various pattern recognition systems"""
        # Merchant patterns for different categories
        self.category_patterns = {
            'Food & Dining': [
                r'zomato', r'swiggy', r'dominos', r'pizza', r'restaurant',
                r'food', r'cafe', r'hotel', r'dhaba', r'mess'
            ],
            'Transportation': [
                r'uber', r'ola', r'rapido', r'taxi', r'cab', r'auto',
                r'petrol', r'diesel', r'fuel', r'bunk', r'station'
            ],
            'Online Stores': [
                r'amazon', r'flipkart', r'meesho', r'myntra', r'ajio',
                r'shopping', r'store', r'mart', r'bazaar'
            ],
            'Recharge': [
                r'airtel', r'jio', r'vodafone', r'bsnl', r'mobile',
                r'recharge', r'prepaid', r'postpaid', r'topup'
            ],
            'Entertainment': [
                r'netflix', r'spotify', r'hotstar', r'prime', r'youtube',
                r'movie', r'cinema', r'theatre', r'music'
            ],
            'Bills': [
                r'electricity', r'water', r'gas', r'internet', r'broadband',
                r'bill', r'payment', r'utility'
            ]
        }
        
        # Person-to-person transfer patterns
        self.p2p_patterns = [
            r'\b(mr|mrs|ms|dr|prof)\s+[a-z]+',
            r'\b[a-z]+\s+(kumar|singh|sharma|gupta|agarwal|reddy|rao)\b',
            r'friend', r'family', r'brother', r'sister', r'father', r'mother',
            r'dad', r'mom', r'uncle', r'aunt', r'cousin'
        ]
        
        # Business transaction patterns
        self.business_patterns = [
            r'pvt\s*ltd', r'private\s*limited', r'company', r'corp',
            r'enterprises', r'services', r'solutions', r'technologies',
            r'agency', r'agencies', r'traders', r'suppliers'
        ]
    
    def _initialize_amount_patterns(self):
        """Initialize amount-based categorization patterns"""
        self.amount_category_rules = {
            'micro': (0, 50),      # Small purchases, tips
            'small': (50, 200),    # Snacks, transport
            'medium': (200, 1000), # Meals, shopping
            'large': (1000, 5000), # Major purchases
            'very_large': (5000, float('inf'))  # Investments, EMIs
        }
        
        # Amount patterns for specific categories
        self.amount_hints = {
            'Transportation': {
                'auto_rickshaw': (20, 100),
                'taxi_short': (50, 200),
                'taxi_long': (200, 500),
                'petrol_bike': (100, 300),
                'petrol_car': (500, 2000)
            },
            'Food & Dining': {
                'snacks': (10, 100),
                'fast_food': (100, 300),
                'restaurant': (300, 1000),
                'fine_dining': (1000, 3000)
            },
            'Recharge': {
                'mobile_topup': (10, 100),
                'mobile_plan': (100, 1000),
                'dth_recharge': (200, 500)
            }
        }
    
    def _initialize_temporal_patterns(self):
        """Initialize temporal pattern recognition"""
        self.temporal_hints = {
            'business_hours': (9, 18),
            'lunch_time': (12, 14),
            'dinner_time': (19, 22),
            'late_night': (22, 6)
        }
    
    def extract_comprehensive_features(self, description: str, amount: float, 
                                     date: Optional[datetime] = None) -> TransactionFeatures:
        """
        Extract comprehensive features from transaction
        
        Args:
            description: Transaction description
            amount: Transaction amount
            date: Transaction date
            
        Returns:
            TransactionFeatures object with all extracted features
        """
        features = TransactionFeatures()
        
        # Basic features
        features.description_length = len(description)
        features.word_count = len(description.split())
        features.amount = amount
        
        # UPI features
        features.upi_features = self.upi_extractor.generate_enhanced_features(description, amount)
        
        # Temporal features
        if date:
            features.day_of_week = date.weekday()
            features.hour_of_day = date.hour
            features.is_weekend = date.weekday() >= 5
            features.is_business_hours = 9 <= date.hour <= 18
        
        # Amount-based features
        features.amount_category = self._categorize_amount(amount)
        features.is_round_amount = self._is_round_amount(amount)
        
        # Text features
        features.has_numbers = bool(re.search(r'\d', description))
        features.has_special_chars = bool(re.search(r'[^a-zA-Z0-9\s]', description))
        
        # Pattern recognition
        features.transaction_pattern = self._identify_transaction_pattern(description)
        features.merchant_confidence = self._calculate_merchant_confidence(description)
        
        # Category hints
        features.category_hints = self._generate_category_hints(description, amount)
        
        return features
    
    def _categorize_amount(self, amount: float) -> str:
        """Categorize amount into predefined ranges"""
        for category, (min_amt, max_amt) in self.amount_category_rules.items():
            if min_amt <= amount < max_amt:
                return category
        return 'unknown'
    
    def _is_round_amount(self, amount: float) -> bool:
        """Check if amount is a round number (often indicates manual entry)"""
        return amount % 10 == 0 or amount % 50 == 0 or amount % 100 == 0
    
    def _identify_transaction_pattern(self, description: str) -> str:
        """Identify the type of transaction pattern"""
        desc_lower = description.lower()
        
        # UPI patterns
        if 'upi' in desc_lower:
            if any(pattern in desc_lower for pattern in ['paytm', 'phonepe', 'gpay']):
                return 'upi_app'
            else:
                return 'upi_generic'
        
        # Card patterns
        if any(pattern in desc_lower for pattern in ['card', 'pos', 'atm']):
            return 'card_transaction'
        
        # Transfer patterns
        if any(pattern in desc_lower for pattern in ['neft', 'rtgs', 'imps']):
            return 'bank_transfer'
        
        # Cash patterns
        if any(pattern in desc_lower for pattern in ['cash', 'withdrawal', 'deposit']):
            return 'cash_transaction'
        
        return 'unknown'
    
    def _calculate_merchant_confidence(self, description: str) -> float:
        """Calculate confidence in merchant identification"""
        confidence = 0.0
        desc_lower = description.lower()
        
        # Known merchant patterns
        known_merchants = ['zomato', 'swiggy', 'amazon', 'flipkart', 'paytm', 'uber', 'ola']
        for merchant in known_merchants:
            if merchant in desc_lower:
                confidence += 0.3
        
        # Business indicators
        for pattern in self.business_patterns:
            if re.search(pattern, desc_lower):
                confidence += 0.2
        
        # UPI structure indicates merchant
        if re.search(r'/[^/]+/xxxxx', desc_lower):
            confidence += 0.2
        
        # Person indicators reduce merchant confidence
        for pattern in self.p2p_patterns:
            if re.search(pattern, desc_lower):
                confidence -= 0.3
        
        return max(0.0, min(1.0, confidence))
    
    def _generate_category_hints(self, description: str, amount: float) -> List[Tuple[str, str, float]]:
        """Generate category hints with confidence scores"""
        hints = []
        desc_lower = description.lower()
        
        # Pattern-based hints
        for category, patterns in self.category_patterns.items():
            for pattern in patterns:
                if re.search(pattern, desc_lower):
                    confidence = 0.7  # Base confidence for pattern match
                    
                    # Boost confidence for amount consistency
                    if category in self.amount_hints:
                        for subcategory, (min_amt, max_amt) in self.amount_hints[category].items():
                            if min_amt <= amount <= max_amt:
                                confidence += 0.2
                                hints.append((category, subcategory, confidence))
                                break
                    else:
                        hints.append((category, 'General', confidence))
        
        # UPI-based hints
        upi_components = self.upi_extractor.extract_upi_components(description)
        category_hint = self.upi_extractor.get_category_hint(upi_components)
        if category_hint:
            hints.append((category_hint[0], category_hint[1], 0.8))
        
        # Amount-based hints
        amount_hints = self._get_amount_based_hints(amount)
        hints.extend(amount_hints)
        
        # Remove duplicates and sort by confidence
        unique_hints = {}
        for category, subcategory, confidence in hints:
            key = (category, subcategory)
            if key not in unique_hints or unique_hints[key] < confidence:
                unique_hints[key] = confidence
        
        return [(cat, subcat, conf) for (cat, subcat), conf in 
                sorted(unique_hints.items(), key=lambda x: x[1], reverse=True)]
    
    def _get_amount_based_hints(self, amount: float) -> List[Tuple[str, str, float]]:
        """Generate hints based on amount patterns"""
        hints = []
        
        # Common amount patterns
        if 10 <= amount <= 50:
            hints.append(('Transportation', 'Public Transport', 0.4))
            hints.append(('Food & Dining', 'Snacks', 0.3))
        elif 100 <= amount <= 300:
            hints.append(('Food & Dining', 'Fast Food', 0.4))
            hints.append(('Transportation', 'Auto Rickshaw', 0.3))
        elif 500 <= amount <= 2000:
            hints.append(('Food & Dining', 'Restaurant', 0.3))
            hints.append(('Transportation', 'Taxi', 0.3))
        elif amount > 5000:
            hints.append(('Investments', 'General', 0.3))
            hints.append(('EMI', 'General', 0.3))
        
        return hints
    
    def create_ml_feature_vector(self, features: TransactionFeatures) -> Dict[str, Any]:
        """
        Create feature vector suitable for ML model training
        
        Args:
            features: TransactionFeatures object
            
        Returns:
            Dictionary of features for ML model
        """
        feature_vector = {
            # Basic features
            'description_length': features.description_length,
            'word_count': features.word_count,
            'amount': features.amount,
            'amount_log': np.log1p(features.amount),  # Log transform for better distribution
            
            # Amount categories
            'is_micro_amount': features.amount_category == 'micro',
            'is_small_amount': features.amount_category == 'small',
            'is_medium_amount': features.amount_category == 'medium',
            'is_large_amount': features.amount_category == 'large',
            'is_very_large_amount': features.amount_category == 'very_large',
            'is_round_amount': features.is_round_amount,
            
            # Temporal features
            'day_of_week': features.day_of_week,
            'hour_of_day': features.hour_of_day,
            'is_weekend': features.is_weekend,
            'is_business_hours': features.is_business_hours,
            
            # Text features
            'has_numbers': features.has_numbers,
            'has_special_chars': features.has_special_chars,
            'merchant_confidence': features.merchant_confidence,
            
            # Pattern features
            'transaction_pattern': features.transaction_pattern,
            
            # Category hints (use highest confidence hint)
            'has_category_hint': len(features.category_hints or []) > 0,
            'top_hint_confidence': features.category_hints[0][2] if features.category_hints else 0.0,
        }
        
        # Add UPI features
        if features.upi_features:
            feature_vector.update(features.upi_features)
        
        # Add category hint as feature if available
        if features.category_hints:
            top_hint = features.category_hints[0]
            feature_vector['hint_category'] = top_hint[0]
            feature_vector['hint_subcategory'] = top_hint[1]
        else:
            feature_vector['hint_category'] = None
            feature_vector['hint_subcategory'] = None
        
        return feature_vector
