"""
Direct Data Access for Bank Statement Analyzer
Replaces session-based system with simple direct file access
"""

import json
import csv
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal

from ..models.transaction import RawTransaction, ProcessedTransaction
from .logger import get_logger


class ProcessedTransactionData:
    """Data class for processed transaction information"""
    def __init__(self, hash_id: str, description: str, normalized_description: str,
                 category: str, sub_category: str, confidence: float, frequency: int,
                 amount_range: str, is_manually_labeled: bool, labeled_by: str,
                 labeled_at: str, date: datetime, amount: float, transaction_type: str):
        self.hash_id = hash_id
        self.description = description
        self.normalized_description = normalized_description
        self.category = category
        self.sub_category = sub_category
        self.confidence = confidence
        self.frequency = frequency
        self.amount_range = amount_range
        self.is_manually_labeled = is_manually_labeled
        self.labeled_by = labeled_by
        self.labeled_at = labeled_at
        self.date = date
        self.amount = amount
        self.transaction_type = transaction_type


class DirectDataAccess:
    """
    Simple direct data access without sessions or caching
    Loads data directly from the most recent processed files
    """

    def __init__(self):
        self.logger = get_logger(__name__)

        # Data directories
        self.config_dir = Path("bank_analyzer_config")
        self.sessions_dir = self.config_dir / "sessions" / "sessions"
        self.processed_data_dir = self.config_dir / "processed_data"

    def get_latest_processed_transactions(self) -> List[ProcessedTransactionData]:
        """
        Get processed transactions from the most recent processed data CSV file
        This is the new stateless approach that replaces session-based loading

        Returns:
            List of processed transaction data
        """
        try:
            # Check if processed data directory exists
            if not self.processed_data_dir.exists():
                self.logger.warning("No processed data directory found")
                return []

            # Find the most recent processed data CSV file
            csv_files = list(self.processed_data_dir.glob("processed_transactions_*.csv"))
            if not csv_files:
                self.logger.warning("No processed transaction CSV files found")
                return []

            # Sort by modification time to get the latest
            latest_csv_file = max(csv_files, key=lambda f: f.stat().st_mtime)
            self.logger.info(f"Loading processed data from: {latest_csv_file}")

            # Load the CSV file using standard csv module
            transactions = []

            with open(latest_csv_file, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                for row in reader:
                    # Parse date
                    try:
                        date = datetime.fromisoformat(row['date']).date() if row.get('date') and row['date'].strip() else None
                    except:
                        date = None

                    # Create ProcessedTransactionData object
                    txn = ProcessedTransactionData(
                        hash_id=str(row.get('hash_id', '')),
                        description=str(row.get('description', '')),
                        normalized_description=str(row.get('normalized_description', '')),
                        category=str(row.get('category', '')),
                        sub_category=str(row.get('sub_category', '')),
                        confidence=float(row.get('confidence', 0.0)) if row.get('confidence') else 0.0,
                        frequency=int(row.get('frequency', 1)) if row.get('frequency') else 1,
                        amount_range=str(row.get('amount_range', '')),
                        is_manually_labeled=str(row.get('is_manually_labeled', 'False')).lower() == 'true',
                        labeled_by=str(row.get('labeled_by', '')),
                        labeled_at=str(row.get('labeled_at', '')),
                        date=date,
                        amount=float(row.get('amount', 0.0)) if row.get('amount') else 0.0,
                        transaction_type=str(row.get('transaction_type', ''))
                    )
                    transactions.append(txn)

            self.logger.info(f"Loaded {len(transactions)} processed transactions")
            return transactions

        except Exception as e:
            self.logger.error(f"Error loading processed transactions: {str(e)}")
            return []

    def has_processed_data(self) -> bool:
        """
        Check if processed data is available

        Returns:
            True if processed data CSV files exist
        """
        if not self.processed_data_dir.exists():
            return False

        csv_files = list(self.processed_data_dir.glob("processed_transactions_*.csv"))
        return len(csv_files) > 0

    def get_latest_raw_transactions(self) -> List[RawTransaction]:
        """
        Get raw transactions from the most recent session
        
        Returns:
            List of raw transactions
        """
        try:
            # Find the most recent session directory
            if not self.sessions_dir.exists():
                self.logger.warning("No sessions directory found")
                return []
            
            session_dirs = [d for d in self.sessions_dir.iterdir() if d.is_dir()]
            if not session_dirs:
                self.logger.warning("No session directories found")
                return []
            
            # Sort by modification time to get the latest
            latest_session_dir = max(session_dirs, key=lambda d: d.stat().st_mtime)
            
            # Load raw transactions from the latest session
            raw_transactions_file = latest_session_dir / "raw_transactions.json"
            
            if not raw_transactions_file.exists():
                self.logger.warning(f"No raw transactions file found in {latest_session_dir}")
                return []
            
            return self._load_raw_transactions_from_file(raw_transactions_file)
            
        except Exception as e:
            self.logger.error(f"Error getting latest raw transactions: {str(e)}")
            return []
    
    def _load_raw_transactions_from_file(self, file_path: Path) -> List[RawTransaction]:
        """Load raw transactions from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            transactions = []
            for txn_dict in data:
                # Convert date string back to date object
                txn_date = datetime.fromisoformat(txn_dict["date"]).date()
                
                # Create RawTransaction object
                txn = RawTransaction(
                    date=txn_date,
                    description=txn_dict["description"],
                    amount=Decimal(str(txn_dict["amount"])),
                    balance=Decimal(str(txn_dict["balance"])) if txn_dict.get("balance") else None,
                    transaction_type=txn_dict.get("transaction_type", ""),
                    reference_number=txn_dict.get("reference_number"),
                    cheque_number=txn_dict.get("cheque_number"),
                    branch_code=txn_dict.get("branch_code"),
                    source_file=txn_dict.get("source_file"),
                    source_line=txn_dict.get("source_line"),
                    bank_name=txn_dict.get("bank_name"),
                    account_number=txn_dict.get("account_number"),
                    is_processed=txn_dict.get("is_processed", False),
                    processing_errors=txn_dict.get("processing_errors", [])
                )
                transactions.append(txn)
            
            self.logger.info(f"Loaded {len(transactions)} raw transactions from {file_path}")
            return transactions
            
        except Exception as e:
            self.logger.error(f"Error loading raw transactions from {file_path}: {str(e)}")
            return []
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        Get information about the latest session
        
        Returns:
            Dictionary with session information
        """
        try:
            if not self.sessions_dir.exists():
                return {"session_count": 0, "latest_session": None}
            
            session_dirs = [d for d in self.sessions_dir.iterdir() if d.is_dir()]
            if not session_dirs:
                return {"session_count": 0, "latest_session": None}
            
            # Get the latest session
            latest_session_dir = max(session_dirs, key=lambda d: d.stat().st_mtime)
            
            # Try to get transaction count
            raw_transactions_file = latest_session_dir / "raw_transactions.json"
            transaction_count = 0
            
            if raw_transactions_file.exists():
                try:
                    with open(raw_transactions_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        transaction_count = len(data)
                except:
                    pass
            
            return {
                "session_count": len(session_dirs),
                "latest_session": {
                    "name": latest_session_dir.name,
                    "path": str(latest_session_dir),
                    "transaction_count": transaction_count,
                    "modified_time": datetime.fromtimestamp(latest_session_dir.stat().st_mtime)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting session info: {str(e)}")
            return {"session_count": 0, "latest_session": None}
