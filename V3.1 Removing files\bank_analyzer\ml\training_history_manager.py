"""
Training History Manager
Manages training session history, audit trails, and training analytics
"""

import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Optional, Any
from datetime import datetime
from dataclasses import dataclass, asdict
import logging

from ..core.logger import get_logger


@dataclass
class TrainingSessionRecord:
    """Record of a training session"""
    session_id: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    job_id: str = ""
    job_type: str = "retrain"  # "retrain", "incremental"
    status: str = "pending"  # "pending", "running", "completed", "failed"
    
    # Training data info
    total_transactions_selected: int = 0
    newly_labeled_count: int = 0
    previously_trained_count: int = 0
    selected_transaction_ids: List[str] = None
    
    # Results
    training_accuracy: float = 0.0
    model_version: str = ""
    error_message: str = ""
    
    # User info
    initiated_by: str = "user"
    notes: str = ""
    
    def __post_init__(self):
        if self.selected_transaction_ids is None:
            self.selected_transaction_ids = []


@dataclass
class TrainingAnalytics:
    """Analytics about training history"""
    total_sessions: int
    successful_sessions: int
    failed_sessions: int
    total_transactions_trained: int
    average_accuracy: float
    last_training_date: Optional[datetime]
    most_active_period: str
    training_frequency_days: float
    
    @property
    def success_rate(self) -> float:
        """Calculate training success rate"""
        if self.total_sessions == 0:
            return 0.0
        return (self.successful_sessions / self.total_sessions) * 100


class TrainingHistoryManager:
    """
    Manages training session history and provides analytics
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.history_file = self.data_dir / "training_history.json"
        self.analytics_file = self.data_dir / "training_analytics.json"
        
        # Load existing history
        self.training_sessions: List[TrainingSessionRecord] = self._load_training_history()
    
    def _load_training_history(self) -> List[TrainingSessionRecord]:
        """Load training history from file"""
        try:
            if not self.history_file.exists():
                return []
            
            with open(self.history_file, 'r') as f:
                data = json.load(f)
            
            sessions = []
            for session_data in data:
                # Convert datetime strings back to datetime objects
                if 'started_at' in session_data:
                    session_data['started_at'] = datetime.fromisoformat(session_data['started_at'])
                if 'completed_at' in session_data and session_data['completed_at']:
                    session_data['completed_at'] = datetime.fromisoformat(session_data['completed_at'])
                
                sessions.append(TrainingSessionRecord(**session_data))
            
            return sessions
            
        except Exception as e:
            self.logger.error(f"Error loading training history: {str(e)}")
            return []
    
    def _save_training_history(self):
        """Save training history to file"""
        try:
            # Convert to serializable format
            data = []
            for session in self.training_sessions:
                session_dict = asdict(session)
                # Convert datetime objects to strings
                if session_dict['started_at']:
                    session_dict['started_at'] = session_dict['started_at'].isoformat()
                if session_dict['completed_at']:
                    session_dict['completed_at'] = session_dict['completed_at'].isoformat()
                data.append(session_dict)
            
            with open(self.history_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving training history: {str(e)}")
    
    def start_training_session(self, session_id: str, job_id: str, job_type: str,
                             selected_transaction_ids: List[str], 
                             newly_labeled_count: int, previously_trained_count: int,
                             initiated_by: str = "user", notes: str = "") -> TrainingSessionRecord:
        """
        Start a new training session record
        
        Args:
            session_id: Unique session identifier
            job_id: Associated training job ID
            job_type: Type of training ("retrain", "incremental")
            selected_transaction_ids: List of selected transaction hash IDs
            newly_labeled_count: Number of newly labeled transactions
            previously_trained_count: Number of previously trained transactions
            initiated_by: User who initiated the training
            notes: Optional notes about the session
            
        Returns:
            Training session record
        """
        session = TrainingSessionRecord(
            session_id=session_id,
            started_at=datetime.now(),
            job_id=job_id,
            job_type=job_type,
            status="running",
            total_transactions_selected=len(selected_transaction_ids),
            newly_labeled_count=newly_labeled_count,
            previously_trained_count=previously_trained_count,
            selected_transaction_ids=selected_transaction_ids.copy(),
            initiated_by=initiated_by,
            notes=notes
        )
        
        self.training_sessions.append(session)
        self._save_training_history()
        
        self.logger.info(f"Started training session: {session_id}")
        return session
    
    def complete_training_session(self, session_id: str, success: bool, 
                                accuracy: float = 0.0, model_version: str = "",
                                error_message: str = ""):
        """
        Mark a training session as completed
        
        Args:
            session_id: Session identifier
            success: Whether training was successful
            accuracy: Training accuracy if successful
            model_version: Model version if successful
            error_message: Error message if failed
        """
        for session in self.training_sessions:
            if session.session_id == session_id:
                session.completed_at = datetime.now()
                session.status = "completed" if success else "failed"
                session.training_accuracy = accuracy
                session.model_version = model_version
                session.error_message = error_message
                break
        
        self._save_training_history()
        self.logger.info(f"Completed training session: {session_id} (success: {success})")
    
    def get_training_analytics(self) -> TrainingAnalytics:
        """
        Get comprehensive training analytics
        
        Returns:
            Training analytics summary
        """
        if not self.training_sessions:
            return TrainingAnalytics(
                total_sessions=0,
                successful_sessions=0,
                failed_sessions=0,
                total_transactions_trained=0,
                average_accuracy=0.0,
                last_training_date=None,
                most_active_period="N/A",
                training_frequency_days=0.0
            )
        
        successful_sessions = [s for s in self.training_sessions if s.status == "completed"]
        failed_sessions = [s for s in self.training_sessions if s.status == "failed"]
        
        total_transactions = sum(s.total_transactions_selected for s in successful_sessions)
        avg_accuracy = sum(s.training_accuracy for s in successful_sessions) / len(successful_sessions) if successful_sessions else 0.0
        
        # Find last training date
        completed_sessions = [s for s in self.training_sessions if s.completed_at]
        last_training = max(completed_sessions, key=lambda x: x.completed_at).completed_at if completed_sessions else None
        
        # Calculate training frequency
        if len(completed_sessions) > 1:
            first_training = min(completed_sessions, key=lambda x: x.completed_at).completed_at
            days_span = (last_training - first_training).days
            frequency = days_span / len(completed_sessions) if days_span > 0 else 0.0
        else:
            frequency = 0.0
        
        return TrainingAnalytics(
            total_sessions=len(self.training_sessions),
            successful_sessions=len(successful_sessions),
            failed_sessions=len(failed_sessions),
            total_transactions_trained=total_transactions,
            average_accuracy=avg_accuracy,
            last_training_date=last_training,
            most_active_period="Recent",  # Could be enhanced with actual analysis
            training_frequency_days=frequency
        )
    
    def get_recent_sessions(self, limit: int = 10) -> List[TrainingSessionRecord]:
        """Get recent training sessions"""
        sorted_sessions = sorted(self.training_sessions, key=lambda x: x.started_at, reverse=True)
        return sorted_sessions[:limit]
    
    def get_session_by_id(self, session_id: str) -> Optional[TrainingSessionRecord]:
        """Get a specific training session by ID"""
        for session in self.training_sessions:
            if session.session_id == session_id:
                return session
        return None
    
    def export_training_history(self, output_file: str):
        """Export training history to CSV"""
        try:
            data = []
            for session in self.training_sessions:
                data.append({
                    'session_id': session.session_id,
                    'started_at': session.started_at.isoformat() if session.started_at else '',
                    'completed_at': session.completed_at.isoformat() if session.completed_at else '',
                    'job_type': session.job_type,
                    'status': session.status,
                    'total_transactions': session.total_transactions_selected,
                    'newly_labeled': session.newly_labeled_count,
                    'previously_trained': session.previously_trained_count,
                    'accuracy': session.training_accuracy,
                    'model_version': session.model_version,
                    'initiated_by': session.initiated_by,
                    'notes': session.notes
                })
            
            df = pd.DataFrame(data)
            df.to_csv(output_file, index=False)
            self.logger.info(f"Exported training history to: {output_file}")
            
        except Exception as e:
            self.logger.error(f"Error exporting training history: {str(e)}")
            raise
