"""
Offline NLTK Data Manager
Handles NLTK data loading with offline fallback and bundled data support
"""

import os
import sys
from pathlib import Path
from typing import Set, List, Optional
import logging

from ..core.logger import get_logger


class OfflineNLTKManager:
    """
    Manages NLTK data with offline capabilities and bundled data fallback
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.nltk_available = False
        self.bundled_data_path = Path(__file__).parent / "nltk_data"
        
        # Try to import NLTK
        try:
            import nltk
            self.nltk = nltk
            self.nltk_available = True
            self._setup_nltk_data_path()
        except ImportError:
            self.logger.info("NLTK not available - using offline text processing")
            self.nltk = None
    
    def _setup_nltk_data_path(self):
        """Setup NLTK data path to include our bundled data"""
        if not self.nltk_available:
            return
        
        # Add our bundled data path to NLTK's search paths
        bundled_path = str(self.bundled_data_path)
        if bundled_path not in self.nltk.data.path:
            self.nltk.data.path.insert(0, bundled_path)
            self.logger.info(f"Added bundled NLTK data path: {bundled_path}")
    
    def get_stopwords(self, language: str = 'english') -> Set[str]:
        """
        Get stopwords with offline fallback
        
        Args:
            language: Language for stopwords (default: 'english')
            
        Returns:
            Set of stopwords
        """
        # Try NLTK first (with bundled data)
        if self.nltk_available:
            try:
                from nltk.corpus import stopwords
                return set(stopwords.words(language))
            except Exception as e:
                self.logger.debug(f"NLTK stopwords failed: {e}")
        
        # Try bundled data directly
        try:
            return self._load_bundled_stopwords(language)
        except Exception as e:
            self.logger.debug(f"Bundled stopwords failed: {e}")
        
        # Fallback to hardcoded stopwords
        return self._get_fallback_stopwords()
    
    def _load_bundled_stopwords(self, language: str = 'english') -> Set[str]:
        """Load stopwords from bundled data"""
        stopwords_file = self.bundled_data_path / "corpora" / "stopwords" / language
        
        if not stopwords_file.exists():
            raise FileNotFoundError(f"Bundled stopwords file not found: {stopwords_file}")
        
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = {line.strip() for line in f if line.strip()}
        
        self.logger.info(f"Loaded {len(stopwords)} stopwords from bundled data")
        return stopwords
    
    def _get_fallback_stopwords(self) -> Set[str]:
        """Get hardcoded fallback stopwords"""
        return {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'i', 'me', 'my', 'we', 'our', 'you',
            'your', 'this', 'these', 'they', 'them', 'their', 'have', 'had',
            'but', 'or', 'if', 'while', 'when', 'where', 'why', 'how', 'all',
            'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
            'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very',
            'can', 'could', 'should', 'would', 'may', 'might', 'must', 'shall',
            'am', 'been', 'being', 'do', 'does', 'did', 'doing', 'get', 'got',
            'getting', 'go', 'going', 'gone', 'went', 'come', 'came', 'coming'
        }
    
    def tokenize(self, text: str) -> List[str]:
        """
        Tokenize text with offline fallback
        
        Args:
            text: Text to tokenize
            
        Returns:
            List of tokens
        """
        if not text:
            return []
        
        # Try NLTK tokenization first
        if self.nltk_available:
            try:
                from nltk.tokenize import word_tokenize
                return word_tokenize(text)
            except Exception as e:
                self.logger.debug(f"NLTK tokenization failed: {e}")
        
        # Fallback to simple tokenization
        return self._simple_tokenize(text)
    
    def _simple_tokenize(self, text: str) -> List[str]:
        """Simple tokenization without NLTK"""
        import string
        import re
        
        # Convert to lowercase
        text = text.lower()
        
        # Replace punctuation with spaces
        text = text.translate(str.maketrans(string.punctuation, ' ' * len(string.punctuation)))
        
        # Split on whitespace and filter empty strings
        tokens = [token for token in text.split() if token]
        
        return tokens
    
    def get_stemmer(self):
        """
        Get stemmer with offline fallback
        
        Returns:
            Stemmer instance
        """
        if self.nltk_available:
            try:
                from nltk.stem import PorterStemmer
                return PorterStemmer()
            except Exception as e:
                self.logger.debug(f"NLTK stemmer failed: {e}")
        
        # Return offline stemmer
        return OfflineStemmer()
    
    def is_nltk_available(self) -> bool:
        """Check if NLTK is available and working"""
        return self.nltk_available
    
    def download_nltk_data(self, packages: List[str], quiet: bool = True) -> bool:
        """
        Attempt to download NLTK data with timeout and error handling
        
        Args:
            packages: List of NLTK packages to download
            quiet: Whether to suppress download messages
            
        Returns:
            True if successful, False otherwise
        """
        if not self.nltk_available:
            return False
        
        try:
            import socket
            # Set a reasonable timeout
            original_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(10)
            
            success = True
            for package in packages:
                try:
                    self.nltk.download(package, quiet=quiet)
                    self.logger.info(f"Successfully downloaded NLTK package: {package}")
                except Exception as e:
                    self.logger.warning(f"Failed to download NLTK package {package}: {e}")
                    success = False
            
            # Restore original timeout
            socket.setdefaulttimeout(original_timeout)
            return success
            
        except Exception as e:
            self.logger.error(f"Error downloading NLTK data: {e}")
            return False


class OfflineStemmer:
    """Simple offline stemmer implementation"""
    
    def __init__(self):
        # Common English suffixes for basic stemming
        self.suffixes = [
            'ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion', 'ness', 'ment', 
            'able', 'ible', 'ful', 'less', 'ous', 'ive', 'al', 'ic', 'ical',
            'ize', 'ise', 'fy', 'ify', 'ary', 'ery', 'ory', 'ity', 'ty'
        ]
    
    def stem(self, word: str) -> str:
        """
        Basic stemming by removing common suffixes
        
        Args:
            word: Word to stem
            
        Returns:
            Stemmed word
        """
        if not word or len(word) <= 3:
            return word
        
        word = word.lower()
        
        # Try to remove suffixes (longest first)
        for suffix in sorted(self.suffixes, key=len, reverse=True):
            if word.endswith(suffix) and len(word) > len(suffix) + 2:
                return word[:-len(suffix)]
        
        return word


# Global instance for easy access
nltk_manager = OfflineNLTKManager()
