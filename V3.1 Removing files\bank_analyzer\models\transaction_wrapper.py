#!/usr/bin/env python3
"""
Transaction Wrapper - Compatibility layer for different transaction data formats
Handles conversion between dict objects and transaction objects with proper attributes
"""

from typing import Dict, Any, Union
from decimal import Decimal
from datetime import datetime
from .transaction import RawTransaction


class TransactionWrapper:
    """
    Wrapper class that provides a consistent interface for transaction data
    regardless of whether it comes from dict (CSV) or transaction objects
    """
    
    def __init__(self, data: Union[Dict[str, Any], RawTransaction]):
        """
        Initialize wrapper with either dict or transaction object
        
        Args:
            data: Either a dictionary from CSV or a RawTransaction object
        """
        self._data = data
        self._is_dict = isinstance(data, dict)
    
    @property
    def description(self) -> str:
        """Get transaction description"""
        if self._is_dict:
            return str(self._data.get('description', ''))
        return str(getattr(self._data, 'description', ''))
    
    @property
    def amount(self) -> Union[Decimal, float]:
        """Get transaction amount"""
        if self._is_dict:
            amount_val = self._data.get('amount', 0)
            if isinstance(amount_val, str):
                try:
                    return float(amount_val)
                except (ValueError, TypeError):
                    return 0.0
            return float(amount_val) if amount_val else 0.0
        
        amount = getattr(self._data, 'amount', 0)
        if isinstance(amount, Decimal):
            return float(amount)
        return float(amount) if amount else 0.0
    
    @property
    def date(self) -> str:
        """Get transaction date as string"""
        if self._is_dict:
            date_val = self._data.get('date', '')
            if isinstance(date_val, datetime):
                return date_val.strftime('%Y-%m-%d')
            return str(date_val)
        
        date_val = getattr(self._data, 'date', '')
        if isinstance(date_val, datetime):
            return date_val.strftime('%Y-%m-%d')
        return str(date_val)
    
    @property
    def transaction_type(self) -> str:
        """Get transaction type"""
        if self._is_dict:
            return str(self._data.get('transaction_type', 'Unknown'))
        return str(getattr(self._data, 'transaction_type', 'Unknown'))
    
    @property
    def bank_name(self) -> str:
        """Get bank name"""
        if self._is_dict:
            return str(self._data.get('bank_name', ''))
        return str(getattr(self._data, 'bank_name', ''))
    
    @property
    def source_file(self) -> str:
        """Get source file"""
        if self._is_dict:
            return str(self._data.get('source_file', ''))
        return str(getattr(self._data, 'source_file', ''))
    
    @property
    def category(self) -> str:
        """Get category"""
        if self._is_dict:
            return str(self._data.get('category', ''))
        return str(getattr(self._data, 'category', ''))
    
    @property
    def subcategory(self) -> str:
        """Get subcategory"""
        if self._is_dict:
            return str(self._data.get('subcategory', ''))
        return str(getattr(self._data, 'subcategory', ''))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            'description': self.description,
            'amount': self.amount,
            'date': self.date,
            'transaction_type': self.transaction_type,
            'bank_name': self.bank_name,
            'source_file': self.source_file,
            'category': self.category,
            'subcategory': self.subcategory
        }
    
    def __str__(self) -> str:
        """String representation"""
        return f"TransactionWrapper({self.description[:50]}..., {self.amount})"
    
    def __repr__(self) -> str:
        """Detailed representation"""
        return f"TransactionWrapper(description='{self.description}', amount={self.amount}, date='{self.date}')"


def wrap_transactions(transactions: list) -> list:
    """
    Wrap a list of transactions (dict or objects) into TransactionWrapper objects
    
    Args:
        transactions: List of transaction data (dicts or objects)
        
    Returns:
        List of TransactionWrapper objects
    """
    wrapped = []
    for txn in transactions:
        try:
            wrapped.append(TransactionWrapper(txn))
        except Exception as e:
            # Log error but continue with other transactions
            print(f"Warning: Could not wrap transaction {txn}: {e}")
            continue
    
    return wrapped


def unwrap_transactions(wrapped_transactions: list) -> list:
    """
    Convert TransactionWrapper objects back to dictionaries
    
    Args:
        wrapped_transactions: List of TransactionWrapper objects
        
    Returns:
        List of transaction dictionaries
    """
    unwrapped = []
    for wrapper in wrapped_transactions:
        try:
            if isinstance(wrapper, TransactionWrapper):
                unwrapped.append(wrapper.to_dict())
            else:
                # Already unwrapped or different format
                unwrapped.append(wrapper)
        except Exception as e:
            print(f"Warning: Could not unwrap transaction {wrapper}: {e}")
            continue
    
    return unwrapped
