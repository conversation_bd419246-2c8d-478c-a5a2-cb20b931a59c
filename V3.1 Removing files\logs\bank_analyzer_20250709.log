2025-07-09 22:20:32 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-07-09 22:20:32 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250709.log
2025-07-09 22:20:32 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-07-09 22:20:32 - __main__ - INFO - main:89 - Starting Bank Statement Analyzer application
2025-07-09 22:20:32 - __main__ - INFO - main:102 - ⚠️ NLTK not available: No module named 'nltk' - using offline text processing
2025-07-09 22:20:32 - bank_analyzer.core.directory_manager - INFO - __init__:56 - Directory Manager initialized with organized structure
2025-07-09 22:20:32 - bank_analyzer.ui.main_window - INFO - check_existing_processed_data:431 - Found 3 processed files across all directories, latest: data\processed_statements\transactions_Indian_Bank_20250710_030216.csv with 1085 transactions
2025-07-09 22:20:32 - bank_analyzer.ui.main_window - INFO - __init__:95 - Bank Analyzer main window initialized
2025-07-09 22:20:32 - __main__ - INFO - main:131 - Bank Statement Analyzer application started successfully
2025-07-09 22:20:34 - __main__ - INFO - main:135 - Bank Statement Analyzer application exited with code: 0
2025-07-09 22:26:28 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-07-09 22:26:28 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250709.log
2025-07-09 22:26:28 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-07-09 22:26:28 - __main__ - INFO - main:89 - Starting Bank Statement Analyzer application
2025-07-09 22:26:28 - __main__ - INFO - main:98 - ✅ NLTK data available for text processing
2025-07-09 22:26:28 - bank_analyzer.core.directory_manager - INFO - __init__:56 - Directory Manager initialized with organized structure
2025-07-09 22:26:28 - bank_analyzer.ui.main_window - INFO - check_existing_processed_data:431 - Found 3 processed files across all directories, latest: data\processed_statements\transactions_Indian_Bank_20250710_030216.csv with 1085 transactions
2025-07-09 22:26:28 - bank_analyzer.ui.main_window - INFO - __init__:95 - Bank Analyzer main window initialized
2025-07-09 22:26:28 - __main__ - INFO - main:131 - Bank Statement Analyzer application started successfully
2025-07-09 22:26:44 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 3182 transactions, 1948 labelled
2025-07-09 22:26:44 - bank_analyzer.ui.model_training_window - INFO - __init__:98 - Model Training Window initialized
2025-07-09 22:27:08 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 3182 transactions, 1948 labelled
2025-07-09 22:27:43 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 1948 labelled
2025-07-09 22:27:58 - bank_analyzer.ml.model_state_manager - INFO - unlock_model:239 - Model unlocked: Unlocked by user for retraining
2025-07-09 22:28:45 - bank_analyzer.ui.main_window - INFO - _load_processed_transactions:644 - Loaded 149 processed transactions for categorization
2025-07-09 22:28:46 - bank_analyzer.ui.main_window - INFO - _load_processed_file:535 - Loaded processed data from: Z:\bank_statement_analyzer_standalone\V3\data\processed_statements\transactions_State_Bank_of_India_20250710_030045.csv
2025-07-09 22:28:50 - bank_analyzer.ui.manual_labeling_window - INFO - load_categories_from_json:168 - Loaded 7 credit and 34 debit categories from JSON
2025-07-09 22:28:50 - bank_analyzer.ui.manual_labeling_window - INFO - __init__:64 - Manual Labeling Window initialized
2025-07-09 22:28:54 - bank_analyzer.ui.manual_labeling_window - INFO - load_csv_file:857 - Loaded 149 transactions from Z:\bank_statement_analyzer_standalone\V3\data\processed_statements\transactions_State_Bank_of_India_20250710_030045.csv
2025-07-09 23:38:55 - bank_analyzer.ui.manual_labeling_window - INFO - save_categories_to_json:2031 - Categories saved to bank_analyzer_config\ml_data\ml_categories.json
2025-07-09 23:40:47 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:31 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:32 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:32 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:32 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:33 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:33 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:33 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:33 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:33 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:33 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:34 - bank_analyzer.ui.model_training_window - INFO - auto_load_labelled_data:308 - Loaded 2097 transactions, 2040 labelled
2025-07-09 23:41:36 - bank_analyzer.ml.model_state_manager - INFO - start_training:258 - Model training started
2025-07-09 23:41:36 - bank_analyzer.ml.training_history_manager - INFO - start_training_session:166 - Started training session: training_20250709_234136
2025-07-09 23:41:39 - bank_analyzer.ml.model_state_manager - INFO - complete_training:323 - Training completed. New version: v20250709_234139
2025-07-09 23:41:39 - bank_analyzer.ml.training_history_manager - INFO - complete_training_session:192 - Completed training session: training_20250709_234136 (success: True)
2025-07-09 23:42:26 - bank_analyzer.ui.main_window - INFO - _load_processed_transactions:644 - Loaded 149 processed transactions for categorization
2025-07-09 23:42:28 - bank_analyzer.ui.main_window - INFO - _load_processed_file:535 - Loaded processed data from: Z:\bank_statement_analyzer_standalone\V3\data\processed_statements\transactions_State_Bank_of_India_20250710_030045.csv
2025-07-09 23:42:31 - bank_analyzer.ui.main_window - INFO - _load_processed_transactions:644 - Loaded 149 processed transactions for categorization
2025-07-09 23:42:31 - bank_analyzer.core.transaction_data_manager - INFO - __init__:105 - Transaction Data Manager initialized
2025-07-09 23:42:31 - bank_analyzer.ui.hybrid_categorization_qt - INFO - _initialize_components_async:369 - Starting asynchronous component initialization...
2025-07-09 23:42:32 - bank_analyzer.ml.manual_label_priority_system - INFO - _load_caches:517 - Loaded manual label caches: 1913 exact, 1913 normalized, 855 fuzzy
2025-07-09 23:42:32 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:119 - Using NLTK PorterStemmer
2025-07-09 23:42:32 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:131 - Enhanced stopwords with 132 additional NLTK stopwords
2025-07-09 23:42:32 - bank_analyzer.ml.ml_categorizer - INFO - __init__:107 - TextPreprocessor initialized with 198 stopwords (NLTK enhanced mode)
2025-07-09 23:42:32 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:639 - ML models loaded successfully (version: 1.0)
2025-07-09 23:42:32 - bank_analyzer.core.categorizer - WARNING - _load_existing_categories:72 - Categories file not found, using default categories
2025-07-09 23:42:32 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:119 - Using NLTK PorterStemmer
2025-07-09 23:42:32 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:131 - Enhanced stopwords with 132 additional NLTK stopwords
2025-07-09 23:42:32 - bank_analyzer.ml.ml_categorizer - INFO - __init__:107 - TextPreprocessor initialized with 198 stopwords (NLTK enhanced mode)
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:639 - ML models loaded successfully (version: 1.0)
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:119 - Using NLTK PorterStemmer
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:131 - Enhanced stopwords with 132 additional NLTK stopwords
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - __init__:107 - TextPreprocessor initialized with 198 stopwords (NLTK enhanced mode)
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:639 - ML models loaded successfully (version: 1.0)
2025-07-09 23:42:33 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250709_234233.json
2025-07-09 23:42:33 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 0 patterns
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:119 - Using NLTK PorterStemmer
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:131 - Enhanced stopwords with 132 additional NLTK stopwords
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - __init__:107 - TextPreprocessor initialized with 198 stopwords (NLTK enhanced mode)
2025-07-09 23:42:33 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:639 - ML models loaded successfully (version: 1.0)
2025-07-09 23:42:33 - bank_analyzer.ml.hybrid_prediction_engine - INFO - _initialize_system:111 - Initializing hybrid prediction engine...
2025-07-09 23:42:33 - bank_analyzer.ml.manual_label_priority_system - INFO - bulk_load_manual_labels:217 - Manual label cache is fresh, skipping bulk load
2025-07-09 23:42:33 - bank_analyzer.ml.hybrid_prediction_engine - INFO - _initialize_system:115 - Loaded 1913 manual labels into priority cache
2025-07-09 23:42:33 - bank_analyzer.ml.hybrid_prediction_engine - INFO - _initialize_system:119 - Initializing hierarchical classifier...
2025-07-09 23:42:33 - bank_analyzer.ml.hybrid_prediction_engine - INFO - _initialize_system:122 - Hybrid prediction engine initialized successfully
2025-07-09 23:42:33 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:275 - Loaded 50 categories from main application
2025-07-09 23:42:33 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:308 - Loaded 181 ML-specific categories
2025-07-09 23:42:33 - bank_analyzer.ml.enhanced_training_data_manager - INFO - __init__:97 - Enhanced Training Data Manager initialized
2025-07-09 23:42:33 - bank_analyzer.ui.hybrid_categorization_qt - INFO - _on_initialization_complete:433 - ✅ All components initialized successfully
2025-07-09 23:42:33 - bank_analyzer.ui.hybrid_categorization_qt - INFO - _handle_session_management:1074 - Processing 149 transactions from live data
2025-07-09 23:42:34 - bank_analyzer.ui.hybrid_categorization_qt - INFO - process_ml_categorization:1823 - Using Hybrid Prediction Engine for categorization
2025-07-09 23:42:34 - bank_analyzer.ui.hybrid_categorization_qt - INFO - process_ml_categorization:1853 - Started ML processing of 149 transactions
2025-07-09 23:42:34 - bank_analyzer.ui.hybrid_categorization_qt - INFO - _run_background_validation:1129 - Background validation found 10 potential issues in first 10 transactions
2025-07-09 23:42:36 - bank_analyzer.ui.hybrid_categorization_qt - INFO - on_ml_processing_completed:1977 - ML processing complete: 19 categorized, 130 uncategorized
2025-07-09 23:42:36 - bank_analyzer.ui.hybrid_categorization_qt - INFO - validate_transaction_pipeline:1301 - Pipeline validation for after_ml: {'raw_transactions': 149, 'categorized': 19, 'uncategorized': 130, 'ai_categorized': 0, 'manual_categorized': 0, 'total_processed': 149}
2025-07-09 23:42:36 - bank_analyzer.ui.hybrid_categorization_qt - INFO - save_session:2662 - Session saved: data\categorization_sessions\session_20250709_2342_2c2e0f4ce1b0.json (total: 149 transactions)
2025-07-09 23:42:49 - __main__ - INFO - main:135 - Bank Statement Analyzer application exited with code: 0
