{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# 🏦 Complete Bank Statement ML Training\n", "\n", "## 🚀 One-Click ML Training for Your Bank Statement Analyzer\n", "\n", "### 📋 Quick Start:\n", "1. **Enable GPU**: Runtime → Change runtime type → GPU\n", "2. **Run all cells**: Runtime → Run all\n", "3. **Upload CSV** when prompted\n", "4. **Download models** when complete\n", "5. **Copy to** `bank_analyzer_config/ml_models/`\n", "\n", "### 📁 Upload Your File:\n", "- **colab_training_data.csv** (recommended)\n", "- **unique_transactions.csv** (alternative)\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# 📦 SETUP: Install Dependencies\n", "print(\"🚀 Setting up ML training environment...\")\n", "!pip install -q scikit-learn pandas numpy nltk joblib matp<PERSON><PERSON>b seaborn\n", "\n", "import nltk\n", "nltk.download('stopwords', quiet=True)\n", "nltk.download('punkt', quiet=True)\n", "print(\"✅ Setup complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports"}, "outputs": [], "source": ["# 📚 IMPORTS: Load All Libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, accuracy_score, confusion_matrix\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.pipeline import Pipeline\n", "import joblib\n", "import re\n", "import json\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "print(\"✅ Libraries imported!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload"}, "outputs": [], "source": ["# 📁 UPLOAD: Your Training Data\n", "from google.colab import files\n", "import io\n", "\n", "print(\"📤 UPLOAD YOUR TRAINING DATA\")\n", "print(\"=\" * 40)\n", "print(\"Upload: colab_training_data.csv\")\n", "print(\"Or: unique_transactions.csv\")\n", "print(\"\")\n", "\n", "uploaded = files.upload()\n", "\n", "for filename in uploaded.keys():\n", "    print(f\"\\n📊 Loading: {filename}\")\n", "    df = pd.read_csv(io.BytesIO(uploaded[filename]))\n", "    break\n", "\n", "print(f\"\\n✅ Data loaded!\")\n", "print(f\"📈 Shape: {df.shape}\")\n", "print(f\"📋 Columns: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "analyze"}, "outputs": [], "source": ["# 🔍 ANALYZE: Data Quality Check\n", "print(\"🔍 DATA ANALYSIS\")\n", "print(\"=\" * 40)\n", "\n", "total_transactions = len(df)\n", "print(f\"📊 Total: {total_transactions:,} transactions\")\n", "\n", "# Find labeled data\n", "if 'is_manually_labeled' in df.columns:\n", "    labeled_df = df[df['is_manually_labeled'] == True].copy()\n", "else:\n", "    labeled_df = df[df['category'].notna() & (df['category'] != '')].copy()\n", "\n", "print(f\"📋 Training data: {len(labeled_df):,}\")\n", "\n", "if len(labeled_df) > 0:\n", "    print(f\"🎯 Categories: {labeled_df['category'].nunique()}\")\n", "    \n", "    # Show distribution\n", "    category_counts = labeled_df['category'].value_counts()\n", "    print(\"\\n📈 Top Categories:\")\n", "    for i, (cat, count) in enumerate(category_counts.head(8).items()):\n", "        pct = count/len(labeled_df)*100\n", "        print(f\"  {i+1}. {cat}: {count} ({pct:.1f}%)\")\n", "    \n", "    # Visualize\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    category_counts.head(8).plot(kind='bar', ax=ax1, color='skyblue')\n", "    ax1.set_title('Category Distribution')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    \n", "    top5 = category_counts.head(5)\n", "    ax2.pie(top5.values, labels=top5.index, autopct='%1.1f%%')\n", "    ax2.set_title('Top 5 Categories')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    if len(labeled_df) >= 50:\n", "        print(\"\\n✅ Good data for training!\")\n", "    else:\n", "        print(\"\\n⚠️ Limited data - results may vary\")\n", "else:\n", "    print(\"❌ No labeled data found!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "preprocess"}, "outputs": [], "source": ["# 🔧 PREPROCESS: Clean Data\n", "def clean_description(text):\n", "    if pd.isna(text):\n", "        return \"\"\n", "    text = str(text).lower()\n", "    text = re.sub(r'[^a-zA-Z0-9\\s]', ' ', text)\n", "    return ' '.join(text.split())\n", "\n", "if len(labeled_df) > 0:\n", "    print(\"🔧 PREPROCESSING\")\n", "    print(\"=\" * 30)\n", "    \n", "    # Clean descriptions\n", "    labeled_df['clean_description'] = labeled_df['description'].apply(clean_description)\n", "    \n", "    # Remove empty\n", "    labeled_df = labeled_df[labeled_df['clean_description'].str.len() > 0].copy()\n", "    \n", "    # Filter categories with enough examples\n", "    category_counts = labeled_df['category'].value_counts()\n", "    valid_categories = category_counts[category_counts >= 2].index\n", "    labeled_df = labeled_df[labeled_df['category'].isin(valid_categories)].copy()\n", "    \n", "    print(f\"📊 Final data: {len(labeled_df):,} transactions\")\n", "    print(f\"🎯 Categories: {labeled_df['category'].nunique()}\")\n", "    \n", "    if len(labeled_df) >= 10:\n", "        X = labeled_df['clean_description']\n", "        y = labeled_df['category']\n", "        print(\"✅ Ready for training!\")\n", "    else:\n", "        print(\"❌ Insufficient data\")\n", "else:\n", "    print(\"❌ No data to process\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train"}, "outputs": [], "source": ["# 🤖 TRAIN: ML Models\n", "if 'X' in locals() and len(X) > 10:\n", "    print(\"🤖 TRAINING ML MODELS\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Split data\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=0.2, random_state=42, stratify=y\n", "    )\n", "    \n", "    print(f\"📊 Training: {len(X_train)} samples\")\n", "    print(f\"📊 Testing: {len(X_test)} samples\")\n", "    \n", "    # Define models\n", "    models = {\n", "        'Random Forest': Pipeline([\n", "            ('tfidf', TfidfVectorizer(max_features=1000, ngram_range=(1, 2), stop_words='english')),\n", "            ('rf', RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1))\n", "        ]),\n", "        'SVM': Pipeline([\n", "            ('tfidf', TfidfVectorizer(max_features=1000, ngram_range=(1, 2), stop_words='english')),\n", "            ('svm', SVC(kernel='linear', probability=True, random_state=42))\n", "        ]),\n", "        'Logistic Regression': Pipeline([\n", "            ('tfidf', TfidfVectorizer(max_features=1000, ngram_range=(1, 2), stop_words='english')),\n", "            ('lr', LogisticRegression(random_state=42, max_iter=1000))\n", "        ])\n", "    }\n", "    \n", "    # Train and evaluate\n", "    results = {}\n", "    for name, model in models.items():\n", "        print(f\"\\n🔄 Training {name}...\")\n", "        model.fit(X_train, y_train)\n", "        y_pred = model.predict(X_test)\n", "        accuracy = accuracy_score(y_test, y_pred)\n", "        results[name] = {'model': model, 'accuracy': accuracy, 'predictions': y_pred}\n", "        print(f\"✅ {name}: {accuracy:.3f} ({accuracy*100:.1f}%)\")\n", "    \n", "    # Select best model\n", "    best_name = max(results.keys(), key=lambda k: results[k]['accuracy'])\n", "    best_model = results[best_name]['model']\n", "    best_accuracy = results[best_name]['accuracy']\n", "    \n", "    print(f\"\\n🏆 BEST: {best_name} ({best_accuracy*100:.1f}%)\")\n", "    \n", "    # Show detailed results\n", "    print(f\"\\n📊 Classification Report:\")\n", "    print(classification_report(y_test, results[best_name]['predictions']))\n", "    \n", "    # Confusion matrix\n", "    cm = confusion_matrix(y_test, results[best_name]['predictions'])\n", "    plt.figure(figsize=(10, 8))\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')\n", "    plt.title(f'Confusion Matrix - {best_name}')\n", "    plt.xlabel('Predicted')\n", "    plt.ylabel('Actual')\n", "    plt.show()\n", "    \n", "else:\n", "    print(\"❌ No data for training!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save"}, "outputs": [], "source": ["# 💾 SAVE: Prepare Models for Download\n", "if 'best_model' in locals():\n", "    print(\"💾 PREPARING MODELS\")\n", "    print(\"=\" * 30)\n", "    \n", "    # Create label encoder\n", "    label_encoder = LabelEncoder()\n", "    label_encoder.fit(y)\n", "    \n", "    # Save main model\n", "    model_filename = f'category_model_{best_name.lower().replace(\" \", \"_\")}.joblib'\n", "    joblib.dump(best_model, model_filename)\n", "    print(f\"✅ Saved: {model_filename}\")\n", "    \n", "    # Save encoders and metadata\n", "    encoders = {\n", "        'category_encoder': label_encoder,\n", "        'model_type': best_name,\n", "        'accuracy': float(best_accuracy),\n", "        'categories': list(label_encoder.classes_),\n", "        'training_samples': len(X_train),\n", "        'test_samples': len(X_test)\n", "    }\n", "    joblib.dump(encoders, 'label_encoders.joblib')\n", "    print(\"✅ Saved: label_encoders.joblib\")\n", "    \n", "    # Model info JSON\n", "    model_info = {\n", "        'model_type': best_name,\n", "        'accuracy': float(best_accuracy),\n", "        'categories_count': len(label_encoder.classes_),\n", "        'training_samples': len(X_train),\n", "        'categories': list(label_encoder.classes_),\n", "        'training_date': pd.Timestamp.now().isoformat()\n", "    }\n", "    \n", "    with open('model_info.json', 'w') as f:\n", "        json.dump(model_info, f, indent=2)\n", "    print(\"✅ Saved: model_info.json\")\n", "    \n", "    print(f\"\\n📊 MODEL SUMMARY:\")\n", "    print(f\"  🤖 Type: {best_name}\")\n", "    print(f\"  🎯 Accuracy: {best_accuracy*100:.1f}%\")\n", "    print(f\"  📊 Categories: {len(label_encoder.classes_)}\")\n", "    print(f\"  📝 Training: {len(X_train):,} samples\")\n", "    \n", "else:\n", "    print(\"❌ No model to save!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "download"}, "outputs": [], "source": ["# 📥 DOWNLOAD: Get Your Trained Models\n", "if 'model_filename' in locals():\n", "    print(\"📥 DOWNLOADING MODELS\")\n", "    print(\"=\" * 40)\n", "    \n", "    files_to_download = [\n", "        model_filename,\n", "        'label_encoders.joblib',\n", "        'model_info.json'\n", "    ]\n", "    \n", "    for filename in files_to_download:\n", "        try:\n", "            print(f\"📤 Downloading: {filename}\")\n", "            files.download(filename)\n", "            print(f\"✅ Downloaded: {filename}\")\n", "        except Exception as e:\n", "            print(f\"❌ Failed: {filename} - {e}\")\n", "            print(\"   Try right-clicking file in browser\")\n", "    \n", "    print(f\"\\n🎉 TRAINING COMPLETE!\")\n", "    print(f\"=\" * 40)\n", "    print(f\"🏆 Model: {best_name} ({best_accuracy*100:.1f}% accuracy)\")\n", "    print(f\"\")\n", "    print(f\"📋 NEXT STEPS:\")\n", "    print(f\"1. Check Downloads folder\")\n", "    print(f\"2. Copy to: bank_analyzer_config/ml_models/\")\n", "    print(f\"3. Rename: {model_filename} → category_model.joblib\")\n", "    print(f\"4. Test in bank analyzer!\")\n", "    \n", "else:\n", "    print(\"❌ No models to download!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test"}, "outputs": [], "source": ["# 🧪 TEST: Try Your Model (Optional)\n", "if 'best_model' in locals():\n", "    print(\"🧪 TESTING MODEL\")\n", "    print(\"=\" * 30)\n", "    \n", "    test_descriptions = [\n", "        \"salary payment from company\",\n", "        \"grocery store purchase\",\n", "        \"atm cash withdrawal\",\n", "        \"restaurant dinner\",\n", "        \"electricity bill payment\",\n", "        \"fuel station gas\"\n", "    ]\n", "    \n", "    for desc in test_descriptions:\n", "        clean_desc = clean_description(desc)\n", "        predicted = best_model.predict([clean_desc])[0]\n", "        confidence = best_model.predict_proba([clean_desc]).max()\n", "        print(f\"'{desc}' → {predicted} ({confidence:.2f})\")\n", "    \n", "    print(\"\\n✅ Model ready for production!\")\n", "else:\n", "    print(\"❌ No model to test!\")"]}, {"cell_type": "markdown", "metadata": {"id": "conclusion"}, "source": ["# 🎉 Congratulations!\n", "\n", "You've successfully trained ML models for your bank statement analyzer!\n", "\n", "## 📁 Files Downloaded:\n", "- **Main model**: `category_model_*.joblib`\n", "- **Encoders**: `label_encoders.joblib`\n", "- **Info**: `model_info.json`\n", "\n", "## 🚀 Installation:\n", "1. Copy files to `bank_analyzer_config/ml_models/`\n", "2. <PERSON><PERSON> main model to `category_model.joblib`\n", "3. Restart your bank analyzer\n", "4. <PERSON><PERSON> improved categorization!\n", "\n", "---\n", "**Happy analyzing! 📊**"]}]}