{"Shared Money": {"sample_count": 210, "unique_descriptions": 210, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Youtube Income": {"sample_count": 45, "unique_descriptions": 45, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Online Stores": {"sample_count": 64, "unique_descriptions": 64, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Grooming": {"sample_count": 5, "unique_descriptions": 5, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Business": {"sample_count": 308, "unique_descriptions": 308, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "House Hold Expenses": {"sample_count": 176, "unique_descriptions": 176, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Bank Charges": {"sample_count": 37, "unique_descriptions": 37, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Transpotation": {"sample_count": 26, "unique_descriptions": 26, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Salary": {"sample_count": 344, "unique_descriptions": 344, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Emi": {"sample_count": 69, "unique_descriptions": 69, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Bike Maintence": {"sample_count": 11, "unique_descriptions": 11, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Savings": {"sample_count": 53, "unique_descriptions": 53, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Investments": {"sample_count": 187, "unique_descriptions": 187, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "College": {"sample_count": 6, "unique_descriptions": 6, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Cash Limit": {"sample_count": 127, "unique_descriptions": 127, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Recharge": {"sample_count": 49, "unique_descriptions": 49, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Rewards": {"sample_count": 22, "unique_descriptions": 22, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Borrowed": {"sample_count": 51, "unique_descriptions": 51, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Appliances": {"sample_count": 24, "unique_descriptions": 24, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Business payouts": {"sample_count": 53, "unique_descriptions": 53, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Service Expenses": {"sample_count": 9, "unique_descriptions": 9, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Celebration": {"sample_count": 1, "unique_descriptions": 1, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Returned": {"sample_count": 93, "unique_descriptions": 93, "diversity_ratio": 1.0, "ml_ready": true, "recommendation": "ML"}, "Ev": {"sample_count": 7, "unique_descriptions": 7, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Intrest": {"sample_count": 1, "unique_descriptions": 1, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Refunds": {"sample_count": 10, "unique_descriptions": 10, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Unexpected": {"sample_count": 2, "unique_descriptions": 2, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Investment Returns": {"sample_count": 3, "unique_descriptions": 3, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Loans": {"sample_count": 4, "unique_descriptions": 4, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Gf": {"sample_count": 3, "unique_descriptions": 3, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Online Storage": {"sample_count": 5, "unique_descriptions": 5, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "School": {"sample_count": 1, "unique_descriptions": 1, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Learning": {"sample_count": 1, "unique_descriptions": 1, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "School Fees": {"sample_count": 1, "unique_descriptions": 1, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}, "Bank Intrest": {"sample_count": 1, "unique_descriptions": 1, "diversity_ratio": 1.0, "ml_ready": false, "recommendation": "Pattern_Matching"}}