"""
Intelligent Category Router

This module implements a sophisticated routing system that intelligently decides
whether to use GPU deep learning, pattern matching, or manual label lookup
based on category confidence, data availability, and performance metrics.
"""

import json
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import pandas as pd
from collections import defaultdict

from .gpu_deep_learning_categorizer import GPUDeepLearningCategorizer
from .manual_label_priority_system import ManualLabelPrioritySystem
from ..core.categorizer import TransactionCategorizer
from ..core.logger import get_logger
from ..models.transaction import RawTransaction


@dataclass
class CategorizationResult:
    """Result of transaction categorization with routing information"""
    category: str
    sub_category: str
    confidence: float
    method_used: str  # "Manual_Label", "GPU_ML", "Pattern_Matching", "Rule_Based"
    processing_time: float
    additional_info: Dict[str, Any]


@dataclass
class CategoryPerformanceMetrics:
    """Performance metrics for a specific category"""
    category: str
    total_predictions: int
    correct_predictions: int
    accuracy: float
    avg_confidence: float
    ml_usage_count: int
    pattern_usage_count: int
    manual_usage_count: int
    last_updated: datetime


class IntelligentCategoryRouter:
    """
    Intelligent routing system that optimizes categorization strategy
    based on real-time performance metrics and data availability
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        
        # Initialize components
        self.gpu_categorizer = GPUDeepLearningCategorizer(f"{config_dir}/ml_models")
        self.manual_label_system = ManualLabelPrioritySystem(config_dir)
        self.rule_categorizer = TransactionCategorizer()
        
        # Performance tracking
        self.category_metrics: Dict[str, CategoryPerformanceMetrics] = {}
        self.global_metrics = {
            'total_categorizations': 0,
            'manual_label_hits': 0,
            'ml_predictions': 0,
            'pattern_predictions': 0,
            'rule_predictions': 0,
            'avg_processing_time': 0.0
        }
        
        # Configuration thresholds
        self.config = {
            'min_ml_confidence': 0.85,  # Minimum confidence for ML predictions
            'min_category_accuracy': 0.90,  # Minimum category accuracy for ML usage
            'min_samples_for_ml': 50,  # Minimum samples needed for ML
            'confidence_decay_factor': 0.95,  # Decay factor for confidence over time
            'performance_window': 100,  # Number of recent predictions to consider
            'fallback_confidence_threshold': 0.70  # Threshold for pattern matching fallback
        }
        
        # Load existing metrics
        self.load_performance_metrics()
        
        # Try to load GPU model
        self.gpu_model_available = self.gpu_categorizer.load_model()
        if self.gpu_model_available:
            self.logger.info("GPU deep learning model loaded successfully")
        else:
            self.logger.warning("GPU model not available, using fallback methods")
    
    def categorize_transaction(self, transaction: Dict[str, Any]) -> CategorizationResult:
        """
        Intelligently categorize a transaction using the best available method
        """
        start_time = datetime.now()
        description = transaction.get('description', '')
        amount = transaction.get('amount', 0.0)
        
        # Step 1: Check manual label cache (highest priority)
        manual_result = self.manual_label_system.lookup_manual_label(description)
        if manual_result.found:
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = CategorizationResult(
                category=manual_result.entry.category,
                sub_category=manual_result.entry.sub_category,
                confidence=manual_result.confidence,
                method_used="Manual_Label",
                processing_time=processing_time,
                additional_info={
                    'match_type': manual_result.match_type,
                    'frequency': manual_result.entry.frequency,
                    'last_used': manual_result.entry.last_accessed.isoformat()
                }
            )
            
            self.update_global_metrics('manual_label_hits')
            return result
        
        # Step 2: Try GPU deep learning (if available and category is ML-ready)
        if self.gpu_model_available:
            try:
                predicted_category, confidence, ml_info = self.gpu_categorizer.predict(description)
                
                # Check if this category should use ML
                if self.should_use_ml_for_category(predicted_category, confidence):
                    processing_time = (datetime.now() - start_time).total_seconds()
                    
                    # Get sub-category from pattern matching or rules
                    sub_category = self.get_subcategory_fallback(description, predicted_category)
                    
                    result = CategorizationResult(
                        category=predicted_category,
                        sub_category=sub_category,
                        confidence=confidence,
                        method_used="GPU_ML",
                        processing_time=processing_time,
                        additional_info={
                            'top_predictions': ml_info.get('top_predictions', []),
                            'model_type': 'GPU_Deep_Learning',
                            'processed_text': ml_info.get('processed_text', '')
                        }
                    )
                    
                    self.update_global_metrics('ml_predictions')
                    self.update_category_metrics(predicted_category, True, confidence, 'ML')
                    return result
                    
            except Exception as e:
                self.logger.warning(f"GPU ML prediction failed: {e}")
        
        # Step 3: Pattern matching and rule-based categorization
        # Convert dictionary to RawTransaction object for rule categorizer
        from datetime import datetime as dt
        from decimal import Decimal

        raw_transaction = RawTransaction(
            date=dt.fromisoformat(transaction['date']).date() if transaction.get('date') else dt.now().date(),
            description=transaction.get('description', ''),
            amount=Decimal(str(transaction.get('amount', 0.0))),
            balance=Decimal(str(transaction.get('balance', 0.0))) if transaction.get('balance') else None,
            transaction_type=transaction.get('transaction_type', '')
        )

        rule_result = self.rule_categorizer.categorize_transaction(raw_transaction)
        rule_category = rule_result.category if hasattr(rule_result, 'category') else rule_result
        
        if rule_category and rule_category != "Uncategorized":
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Estimate confidence based on rule strength
            confidence = self.estimate_rule_confidence(description, rule_category)
            
            result = CategorizationResult(
                category=rule_category,
                sub_category="General",  # Default sub-category
                confidence=confidence,
                method_used="Pattern_Matching",
                processing_time=processing_time,
                additional_info={
                    'rule_based': True,
                    'fallback_reason': 'ML_not_confident_or_unavailable'
                }
            )
            
            self.update_global_metrics('pattern_predictions')
            self.update_category_metrics(rule_category, True, confidence, 'Pattern')
            return result
        
        # Step 4: Final fallback - return uncategorized
        processing_time = (datetime.now() - start_time).total_seconds()
        
        result = CategorizationResult(
            category="Uncategorized",
            sub_category="Unknown",
            confidence=0.0,
            method_used="Fallback",
            processing_time=processing_time,
            additional_info={
                'reason': 'No_method_succeeded',
                'description_length': len(description)
            }
        )
        
        self.update_global_metrics('rule_predictions')
        return result
    
    def should_use_ml_for_category(self, category: str, confidence: float) -> bool:
        """
        Determine if ML should be used for this category based on performance metrics
        """
        # Check minimum confidence threshold
        if confidence < self.config['min_ml_confidence']:
            return False
        
        # Check if GPU model considers this category ML-ready
        if not self.gpu_categorizer.is_category_ml_ready(category):
            return False
        
        # Check historical performance for this category
        if category in self.category_metrics:
            metrics = self.category_metrics[category]
            if metrics.accuracy < self.config['min_category_accuracy']:
                return False
        
        return True
    
    def get_subcategory_fallback(self, description: str, category: str) -> str:
        """
        Get sub-category using pattern matching when ML doesn't provide it
        """
        # This could be enhanced with additional sub-category classification
        # For now, return a default based on category
        subcategory_defaults = {
            'Salary': 'Monthly Salary',
            'Business': 'General Business',
            'Investments': 'General Investment',
            'House Hold Expenses': 'Daily Expenses',
            'Transpotation': 'General Transport',
            'Shared Money': 'General Transfer'
        }
        
        return subcategory_defaults.get(category, 'General')
    
    def estimate_rule_confidence(self, description: str, category: str) -> float:
        """
        Estimate confidence for rule-based categorization
        """
        # Simple heuristic based on description characteristics
        base_confidence = 0.75
        
        # Boost confidence for specific patterns
        if any(keyword in description.lower() for keyword in ['salary', 'upi', 'imps']):
            base_confidence += 0.1
        
        # Reduce confidence for very short descriptions
        if len(description) < 20:
            base_confidence -= 0.15
        
        return max(0.0, min(1.0, base_confidence))
    
    def update_category_metrics(self, category: str, correct: bool, confidence: float, method: str):
        """Update performance metrics for a category"""
        if category not in self.category_metrics:
            self.category_metrics[category] = CategoryPerformanceMetrics(
                category=category,
                total_predictions=0,
                correct_predictions=0,
                accuracy=0.0,
                avg_confidence=0.0,
                ml_usage_count=0,
                pattern_usage_count=0,
                manual_usage_count=0,
                last_updated=datetime.now()
            )
        
        metrics = self.category_metrics[category]
        metrics.total_predictions += 1
        
        if correct:
            metrics.correct_predictions += 1
        
        metrics.accuracy = metrics.correct_predictions / metrics.total_predictions
        
        # Update average confidence (exponential moving average)
        alpha = 0.1
        metrics.avg_confidence = alpha * confidence + (1 - alpha) * metrics.avg_confidence
        
        # Update method usage counts
        if method == 'ML':
            metrics.ml_usage_count += 1
        elif method == 'Pattern':
            metrics.pattern_usage_count += 1
        elif method == 'Manual':
            metrics.manual_usage_count += 1
        
        metrics.last_updated = datetime.now()
    
    def update_global_metrics(self, metric_type: str):
        """Update global performance metrics"""
        self.global_metrics['total_categorizations'] += 1
        self.global_metrics[metric_type] += 1

    def save_performance_metrics(self):
        """Save performance metrics to disk"""
        metrics_path = self.config_dir / "performance_monitoring" / "router_metrics.json"
        metrics_path.parent.mkdir(parents=True, exist_ok=True)

        # Convert metrics to serializable format
        serializable_metrics = {}
        for category, metrics in self.category_metrics.items():
            serializable_metrics[category] = {
                'category': metrics.category,
                'total_predictions': metrics.total_predictions,
                'correct_predictions': metrics.correct_predictions,
                'accuracy': metrics.accuracy,
                'avg_confidence': metrics.avg_confidence,
                'ml_usage_count': metrics.ml_usage_count,
                'pattern_usage_count': metrics.pattern_usage_count,
                'manual_usage_count': metrics.manual_usage_count,
                'last_updated': metrics.last_updated.isoformat()
            }

        data = {
            'category_metrics': serializable_metrics,
            'global_metrics': self.global_metrics,
            'config': self.config,
            'last_saved': datetime.now().isoformat()
        }

        with open(metrics_path, 'w') as f:
            json.dump(data, f, indent=2)

    def load_performance_metrics(self):
        """Load performance metrics from disk"""
        metrics_path = self.config_dir / "performance_monitoring" / "router_metrics.json"

        if not metrics_path.exists():
            return

        try:
            with open(metrics_path, 'r') as f:
                data = json.load(f)

            # Load category metrics
            for category, metrics_data in data.get('category_metrics', {}).items():
                self.category_metrics[category] = CategoryPerformanceMetrics(
                    category=metrics_data['category'],
                    total_predictions=metrics_data['total_predictions'],
                    correct_predictions=metrics_data['correct_predictions'],
                    accuracy=metrics_data['accuracy'],
                    avg_confidence=metrics_data['avg_confidence'],
                    ml_usage_count=metrics_data['ml_usage_count'],
                    pattern_usage_count=metrics_data['pattern_usage_count'],
                    manual_usage_count=metrics_data['manual_usage_count'],
                    last_updated=datetime.fromisoformat(metrics_data['last_updated'])
                )

            # Load global metrics
            self.global_metrics.update(data.get('global_metrics', {}))

            # Load config
            self.config.update(data.get('config', {}))

            self.logger.info("Performance metrics loaded successfully")

        except Exception as e:
            self.logger.warning(f"Error loading performance metrics: {e}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        total_cats = self.global_metrics['total_categorizations']

        if total_cats == 0:
            return {'message': 'No categorizations performed yet'}

        # Calculate method distribution
        method_distribution = {
            'manual_labels': (self.global_metrics['manual_label_hits'] / total_cats) * 100,
            'gpu_ml': (self.global_metrics['ml_predictions'] / total_cats) * 100,
            'pattern_matching': (self.global_metrics['pattern_predictions'] / total_cats) * 100,
            'rule_based': (self.global_metrics['rule_predictions'] / total_cats) * 100
        }

        # Top performing categories
        top_categories = sorted(
            [(cat, metrics.accuracy) for cat, metrics in self.category_metrics.items()
             if metrics.total_predictions >= 5],
            key=lambda x: x[1],
            reverse=True
        )[:10]

        # Categories needing improvement
        low_performing = [
            (cat, metrics.accuracy) for cat, metrics in self.category_metrics.items()
            if metrics.total_predictions >= 10 and metrics.accuracy < 0.8
        ]

        return {
            'total_categorizations': total_cats,
            'method_distribution': method_distribution,
            'gpu_model_available': self.gpu_model_available,
            'top_performing_categories': top_categories,
            'categories_needing_improvement': low_performing,
            'avg_processing_time': self.global_metrics.get('avg_processing_time', 0),
            'category_count': len(self.category_metrics),
            'ml_ready_categories': len([
                cat for cat in self.category_metrics.keys()
                if self.gpu_categorizer.is_category_ml_ready(cat)
            ]) if self.gpu_model_available else 0
        }

    def retrain_gpu_model(self, csv_path: str) -> Dict[str, Any]:
        """Retrain the GPU model with new data"""
        if not self.gpu_model_available:
            self.logger.info("Training new GPU model...")
        else:
            self.logger.info("Retraining existing GPU model...")

        try:
            # Train the model
            training_results = self.gpu_categorizer.train_model(csv_path)

            # Update availability status
            self.gpu_model_available = True

            # Reset category metrics for retrained categories
            ml_ready_categories = training_results['metadata']['ml_ready_categories']
            for category in ml_ready_categories:
                if category in self.category_metrics:
                    # Reset but keep some historical data
                    metrics = self.category_metrics[category]
                    metrics.ml_usage_count = 0
                    metrics.last_updated = datetime.now()

            self.logger.info("GPU model training completed successfully")
            return training_results

        except Exception as e:
            self.logger.error(f"GPU model training failed: {e}")
            return {'error': str(e)}

    def optimize_thresholds(self):
        """
        Automatically optimize confidence thresholds based on performance data
        """
        if len(self.category_metrics) < 5:
            return  # Need more data

        # Analyze performance patterns
        ml_accuracies = [
            metrics.accuracy for metrics in self.category_metrics.values()
            if metrics.ml_usage_count > 10
        ]

        if ml_accuracies:
            avg_ml_accuracy = np.mean(ml_accuracies)

            # Adjust ML confidence threshold based on performance
            if avg_ml_accuracy > 0.95:
                self.config['min_ml_confidence'] = max(0.80, self.config['min_ml_confidence'] - 0.05)
            elif avg_ml_accuracy < 0.85:
                self.config['min_ml_confidence'] = min(0.95, self.config['min_ml_confidence'] + 0.05)

            self.logger.info(f"Optimized ML confidence threshold to {self.config['min_ml_confidence']:.2f}")

    def get_category_routing_strategy(self, category: str) -> Dict[str, Any]:
        """Get the current routing strategy for a specific category"""
        if category not in self.category_metrics:
            return {
                'strategy': 'Unknown',
                'reason': 'Category not seen before',
                'recommendation': 'Will use pattern matching initially'
            }

        metrics = self.category_metrics[category]
        ml_ready = self.gpu_categorizer.is_category_ml_ready(category) if self.gpu_model_available else False

        if metrics.manual_usage_count > metrics.ml_usage_count + metrics.pattern_usage_count:
            strategy = 'Manual_Label_Priority'
            reason = 'Frequently manually labeled'
        elif ml_ready and metrics.accuracy > self.config['min_category_accuracy']:
            strategy = 'GPU_ML_Primary'
            reason = f'High ML accuracy ({metrics.accuracy:.2f})'
        else:
            strategy = 'Pattern_Matching_Primary'
            reason = 'ML not ready or low accuracy'

        return {
            'strategy': strategy,
            'reason': reason,
            'accuracy': metrics.accuracy,
            'total_predictions': metrics.total_predictions,
            'ml_ready': ml_ready,
            'avg_confidence': metrics.avg_confidence
        }
