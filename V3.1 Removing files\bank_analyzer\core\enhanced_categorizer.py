"""
Enhanced categorization engine with improved confidence levels
Based on actual user data patterns and Indian banking context
"""

import re
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging

from .categorizer import TransactionCategorizer, CategoryRule
from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger


class EnhancedTransactionCategorizer(TransactionCategorizer):
    """
    Enhanced categorizer with improved confidence levels and Indian-specific patterns
    """
    
    def __init__(self, main_app_data_dir: str = "data"):
        super().__init__(main_app_data_dir)
        
        # Load user's historical patterns
        self.user_patterns = self._analyze_user_patterns()
        
        # Enhanced rules with higher confidence weights
        self.enhanced_rules = self._create_enhanced_rules()
        
        # Combine with existing rules
        self.rules.extend(self.enhanced_rules)
        
        # Indian-specific merchant patterns
        self.indian_merchants = self._load_indian_merchant_patterns()
        
        # Amount-based categorization
        self.amount_patterns = self._create_amount_based_patterns()
    
    def _analyze_user_patterns(self) -> Dict[str, Any]:
        """Analyze user's historical transaction patterns to improve categorization"""
        patterns = {
            'category_keywords': {},
            'amount_ranges': {},
            'transaction_modes': {},
            'common_descriptions': {}
        }
        
        try:
            expenses_file = self.main_app_data_dir / "expenses" / "expenses.csv"
            if expenses_file.exists():
                df = pd.read_csv(expenses_file)
                
                # Analyze category-specific keywords from notes
                for _, row in df.iterrows():
                    category = row['category']
                    sub_category = row['sub_category']
                    notes = str(row.get('notes', '')).lower()
                    
                    key = f"{category}/{sub_category}"
                    if key not in patterns['category_keywords']:
                        patterns['category_keywords'][key] = []
                    
                    # Extract meaningful words from notes
                    words = re.findall(r'\b\w{3,}\b', notes)
                    patterns['category_keywords'][key].extend(words)
                
                # Analyze amount ranges per category
                category_amounts = df.groupby(['category', 'sub_category'])['amount'].agg(['min', 'max', 'mean']).to_dict('index')
                patterns['amount_ranges'] = category_amounts
                
                # Analyze transaction modes per category
                mode_patterns = df.groupby(['category', 'sub_category'])['transaction_mode'].apply(list).to_dict()
                patterns['transaction_modes'] = mode_patterns
                
                self.logger.info(f"Analyzed {len(df)} historical transactions for pattern learning")
        
        except Exception as e:
            self.logger.warning(f"Could not analyze user patterns: {str(e)}")
        
        return patterns
    
    def _create_enhanced_rules(self) -> List[CategoryRule]:
        """Create enhanced rules with higher confidence based on Indian context"""
        enhanced_rules = []
        
        # Food & Dining - Enhanced with Indian context
        enhanced_rules.extend([
            # Restaurants - Indian specific
            CategoryRule("Food & Dining", "Restaurants", 
                        ["restaurant", "cafe", "dhaba", "hotel", "canteen", "mess", "dine", "dining", "eatery", "bistro", "grill", "punjabi", "south indian", "chinese", "biryani"],
                        [r"\b(restaurant|cafe|dhaba|hotel|canteen|mess|dine|dining)\b", r"\b(food|meal|lunch|dinner|breakfast)\b.*\b(out|restaurant)\b", r"\b(punjabi|south\s*indian|chinese|biryani|thali)\b"],
                        1.2),  # Higher confidence weight
            
            # Groceries - Indian specific
            CategoryRule("Food & Dining", "Groceries",
                        ["grocery", "supermarket", "market", "store", "mart", "bazaar", "provision", "kirana", "vegetables", "fruits", "sabzi", "mandi", "wholesale"],
                        [r"\b(grocery|supermarket|market|kirana|sabzi|mandi)\b", r"\b(big\s*bazaar|reliance|dmart|more|spencer|food\s*world)\b", r"\b(vegetables|fruits|dal|rice|wheat|atta)\b"],
                        1.3),
            
            # Fast Food - Enhanced
            CategoryRule("Food & Dining", "Fast Food",
                        ["mcdonald", "kfc", "pizza", "burger", "domino", "subway", "fast food", "pizza hut", "burger king"],
                        [r"\b(mcd|kfc|pizza|burger|domino|subway|pizza\s*hut|burger\s*king)\b"],
                        1.4),
            
            # Delivery - Indian apps
            CategoryRule("Food & Dining", "Delivery",
                        ["zomato", "swiggy", "uber eats", "food delivery", "delivery", "foodpanda", "dunzo"],
                        [r"\b(zomato|swiggy|uber\s*eats|foodpanda|dunzo)\b", r"\bupi.*\b(zomato|swiggy)\b"],
                        1.5),  # Very high confidence for delivery apps
        ])
        
        # Transportation - Indian specific
        enhanced_rules.extend([
            # Fuel - Indian oil companies
            CategoryRule("Transportation", "Fuel",
                        ["petrol", "diesel", "fuel", "gas", "hp", "ioc", "bpcl", "shell", "essar", "reliance petroleum", "bharat petroleum", "indian oil"],
                        [r"\b(petrol|diesel|fuel|gas)\b", r"\b(hp|ioc|bpcl|shell|essar|reliance\s*petroleum|bharat\s*petroleum|indian\s*oil)\b"],
                        1.4),
            
            # Public Transport - Indian specific
            CategoryRule("Transportation", "Public Transport",
                        ["metro", "bus", "train", "railway", "transport", "ticket", "bmtc", "best", "dtc", "irctc", "tatkal", "sleeper", "ac"],
                        [r"\b(metro|bus|train|railway|bmtc|best|dtc|irctc)\b", r"\b(transport|ticket|tatkal|sleeper)\b"],
                        1.3),
            
            # Taxi/Uber - Enhanced
            CategoryRule("Transportation", "Taxi/Uber",
                        ["uber", "ola", "taxi", "cab", "ride", "rapido", "auto"],
                        [r"\b(uber|ola|taxi|cab|rapido|auto)\b", r"\bupi.*\b(uber|ola)\b"],
                        1.5),
        ])
        
        # Bills & Utilities - Indian specific
        enhanced_rules.extend([
            # Electricity - Indian electricity boards
            CategoryRule("Bills & Utilities", "Electricity",
                        ["electricity", "power", "electric", "bescom", "mseb", "kseb", "tneb", "wbsedcl", "pspcl", "uppcl"],
                        [r"\b(electricity|power|electric|bescom|mseb|kseb|tneb|wbsedcl|pspcl|uppcl)\b"],
                        1.5),
            
            # Internet - Indian ISPs
            CategoryRule("Bills & Utilities", "Internet",
                        ["internet", "broadband", "wifi", "airtel", "jio", "bsnl", "act", "hathway", "tikona", "railwire"],
                        [r"\b(internet|broadband|wifi|airtel|jio|bsnl|act|hathway|tikona|railwire)\b"],
                        1.4),
            
            # Phone - Indian telecom
            CategoryRule("Bills & Utilities", "Phone",
                        ["mobile", "phone", "recharge", "prepaid", "postpaid", "airtel", "jio", "vi", "vodafone", "idea", "bsnl"],
                        [r"\b(mobile|phone|recharge|prepaid|postpaid)\b", r"\b(airtel|jio|vi|vodafone|idea|bsnl)\b"],
                        1.4),
        ])
        
        # Shopping - Indian e-commerce
        enhanced_rules.extend([
            # Electronics - Indian platforms
            CategoryRule("Shopping", "Electronics",
                        ["amazon", "flipkart", "electronics", "mobile", "laptop", "computer", "myntra", "snapdeal", "paytm mall", "croma", "vijay sales"],
                        [r"\b(amazon|flipkart|myntra|snapdeal|paytm\s*mall|croma|vijay\s*sales)\b", r"\b(electronics|mobile|laptop|computer|smartphone|tablet)\b"],
                        1.3),
            
            # Clothing - Indian brands
            CategoryRule("Shopping", "Clothing",
                        ["clothing", "fashion", "apparel", "shirt", "dress", "shoes", "myntra", "ajio", "lifestyle", "max", "westside", "pantaloons"],
                        [r"\b(clothing|fashion|apparel|shirt|dress|shoes|myntra|ajio|lifestyle|max|westside|pantaloons)\b"],
                        1.2),
        ])
        
        # Healthcare - Indian specific
        enhanced_rules.extend([
            # Pharmacy - Indian chains
            CategoryRule("Healthcare", "Pharmacy",
                        ["pharmacy", "medicine", "drug", "chemist", "apollo", "medplus", "netmeds", "1mg", "pharmeasy"],
                        [r"\b(pharmacy|medicine|drug|chemist|apollo|medplus|netmeds|1mg|pharmeasy)\b"],
                        1.4),
            
            # Doctor - Indian context
            CategoryRule("Healthcare", "Doctor",
                        ["doctor", "clinic", "hospital", "medical", "consultation", "apollo", "fortis", "max healthcare", "manipal"],
                        [r"\b(doctor|clinic|hospital|medical|consultation)\b", r"\b(apollo|fortis|max\s*healthcare|manipal)\b"],
                        1.3),
        ])
        
        return enhanced_rules
    
    def _load_indian_merchant_patterns(self) -> Dict[str, Tuple[str, str, float]]:
        """Load Indian-specific merchant patterns for high-confidence matching"""
        return {
            # Food delivery
            "zomato": ("Food & Dining", "Delivery", 0.95),
            "swiggy": ("Food & Dining", "Delivery", 0.95),
            "uber eats": ("Food & Dining", "Delivery", 0.90),
            
            # Transportation
            "uber": ("Transportation", "Taxi/Uber", 0.95),
            "ola": ("Transportation", "Taxi/Uber", 0.95),
            "rapido": ("Transportation", "Taxi/Uber", 0.90),
            
            # E-commerce
            "amazon": ("Shopping", "Electronics", 0.85),
            "flipkart": ("Shopping", "Electronics", 0.85),
            "myntra": ("Shopping", "Clothing", 0.90),
            
            # Utilities
            "airtel": ("Bills & Utilities", "Internet", 0.90),
            "jio": ("Bills & Utilities", "Internet", 0.90),
            "bsnl": ("Bills & Utilities", "Internet", 0.85),
            
            # Fuel
            "indian oil": ("Transportation", "Fuel", 0.95),
            "bharat petroleum": ("Transportation", "Fuel", 0.95),
            "hp": ("Transportation", "Fuel", 0.90),
            "shell": ("Transportation", "Fuel", 0.90),
            
            # Groceries
            "big bazaar": ("Food & Dining", "Groceries", 0.90),
            "dmart": ("Food & Dining", "Groceries", 0.90),
            "reliance fresh": ("Food & Dining", "Groceries", 0.90),
            "more": ("Food & Dining", "Groceries", 0.85),
            
            # Healthcare
            "apollo pharmacy": ("Healthcare", "Pharmacy", 0.95),
            "medplus": ("Healthcare", "Pharmacy", 0.90),
            "1mg": ("Healthcare", "Pharmacy", 0.90),
        }
    
    def _create_amount_based_patterns(self) -> Dict[str, List[Tuple[float, float, str, str, float]]]:
        """Create amount-based categorization patterns"""
        return {
            # (min_amount, max_amount, category, sub_category, confidence_boost)
            "small_amounts": [
                (1, 100, "Transportation", "Public Transport", 0.3),  # Bus, metro fares
                (10, 50, "Food & Dining", "Snacks", 0.2),  # Tea, coffee, snacks
            ],
            "medium_amounts": [
                (100, 500, "Food & Dining", "Fast Food", 0.2),  # Fast food meals
                (200, 800, "Transportation", "Taxi/Uber", 0.2),  # Cab rides
                (500, 2000, "Food & Dining", "Restaurants", 0.1),  # Restaurant meals
            ],
            "large_amounts": [
                (1000, 5000, "Food & Dining", "Groceries", 0.2),  # Grocery shopping
                (2000, 10000, "Shopping", "Clothing", 0.1),  # Clothing purchases
                (5000, 50000, "Shopping", "Electronics", 0.1),  # Electronics
            ]
        }
    
    def categorize_transaction(self, raw_transaction: RawTransaction) -> ProcessedTransaction:
        """Enhanced categorization with multiple confidence boosting techniques"""
        processed = ProcessedTransaction()
        processed.update_from_raw(raw_transaction)
        
        # Step 1: Check for exact merchant matches (highest confidence)
        merchant_match = self._check_merchant_patterns(raw_transaction.description)
        if merchant_match:
            category, sub_category, confidence = merchant_match
            processed.category = category
            processed.sub_category = sub_category
            processed.confidence_score = confidence
            # Don't override transaction type - it was correctly set in update_from_raw()
            # processed.type = "Expense" if raw_transaction.amount < 0 else "Income"
        else:
            # Step 2: Use enhanced rule-based categorization
            suggestions = self._get_enhanced_category_suggestions(raw_transaction.description, raw_transaction.amount)
            
            if suggestions:
                best_suggestion = suggestions[0]
                processed.category = best_suggestion['category']
                processed.sub_category = best_suggestion['sub_category']
                # Don't override transaction type - it was correctly set in update_from_raw()
                # processed.type = best_suggestion['transaction_type']
                processed.confidence_score = best_suggestion['confidence']
                processed.suggested_categories = suggestions[:3]
            else:
                # Step 3: Fallback to user pattern matching
                pattern_match = self._match_user_patterns(raw_transaction.description)
                if pattern_match:
                    processed.category = pattern_match['category']
                    processed.sub_category = pattern_match['sub_category']
                    processed.confidence_score = pattern_match['confidence']
                else:
                    # Default categorization
                    processed.category = "Other"
                    processed.sub_category = "Miscellaneous"
                    processed.confidence_score = 0.1
        
        # Step 4: Apply amount-based confidence boost
        processed.confidence_score = self._apply_amount_boost(
            processed.confidence_score, 
            abs(float(raw_transaction.amount)), 
            processed.category, 
            processed.sub_category
        )
        
        # Step 5: Determine transaction mode with higher confidence
        processed.transaction_mode = self._enhanced_transaction_mode_detection(raw_transaction.description)
        
        return processed
    
    def _check_merchant_patterns(self, description: str) -> Optional[Tuple[str, str, float]]:
        """Check for exact merchant pattern matches"""
        description_lower = description.lower()
        
        for merchant, (category, sub_category, confidence) in self.indian_merchants.items():
            if merchant.lower() in description_lower:
                return category, sub_category, confidence
        
        return None
    
    def _get_enhanced_category_suggestions(self, description: str, amount: Decimal) -> List[Dict[str, Any]]:
        """Get category suggestions with enhanced confidence calculation"""
        if not description:
            return []
        
        description_lower = description.lower()
        suggestions = []
        
        for rule in self.rules:
            confidence = 0.0
            
            # Enhanced keyword matching with partial matches
            keyword_score = 0.0
            for keyword in rule.keywords:
                if keyword.lower() in description_lower:
                    # Exact match gets full score
                    keyword_score += 1.0
                elif any(word in description_lower for word in keyword.lower().split()):
                    # Partial match gets reduced score
                    keyword_score += 0.5
            
            if keyword_score > 0:
                confidence += (keyword_score / len(rule.keywords)) * 0.7  # Increased weight
            
            # Enhanced pattern matching
            pattern_score = 0.0
            for pattern in rule.patterns:
                try:
                    if re.search(pattern, description_lower, re.IGNORECASE):
                        pattern_score += 1.0
                except re.error:
                    continue
            
            if pattern_score > 0:
                confidence += (pattern_score / len(rule.patterns)) * 0.3
            
            # Apply rule weight and additional boosts
            confidence *= rule.confidence_weight
            
            # Boost confidence for UPI transactions with clear merchant names
            if "upi" in description_lower and any(merchant in description_lower for merchant in self.indian_merchants.keys()):
                confidence *= 1.2
            
            # Boost confidence for transactions with clear amount patterns
            amount_float = float(abs(amount)) if amount else 0
            if self._matches_amount_pattern(amount_float, rule.category, rule.sub_category):
                confidence *= 1.1
            
            if confidence > 0:
                suggestions.append({
                    'category': rule.category,
                    'sub_category': rule.sub_category,
                    'transaction_type': rule.transaction_type,
                    'confidence': min(confidence, 0.98),  # Cap at 98% to leave room for manual verification
                    'matched_keywords': keyword_score,
                    'matched_patterns': pattern_score
                })
        
        # Sort by confidence score
        suggestions.sort(key=lambda x: x['confidence'], reverse=True)
        
        return suggestions
    
    def _match_user_patterns(self, description: str) -> Optional[Dict[str, Any]]:
        """Match against user's historical patterns"""
        description_lower = description.lower()
        
        for category_key, keywords in self.user_patterns['category_keywords'].items():
            if not keywords:
                continue
            
            matches = sum(1 for keyword in keywords if keyword in description_lower)
            if matches > 0:
                confidence = min(0.6 + (matches * 0.1), 0.8)  # Base confidence with boost
                category, sub_category = category_key.split('/')
                
                return {
                    'category': category,
                    'sub_category': sub_category,
                    'confidence': confidence
                }
        
        return None
    
    def _matches_amount_pattern(self, amount: float, category: str, sub_category: str) -> bool:
        """Check if amount matches expected patterns for the category"""
        for pattern_type, patterns in self.amount_patterns.items():
            for min_amt, max_amt, cat, subcat, _ in patterns:
                if (min_amt <= amount <= max_amt and 
                    cat == category and subcat == sub_category):
                    return True
        return False
    
    def _apply_amount_boost(self, base_confidence: float, amount: float, category: str, sub_category: str) -> float:
        """Apply amount-based confidence boost"""
        for pattern_type, patterns in self.amount_patterns.items():
            for min_amt, max_amt, cat, subcat, boost in patterns:
                if (min_amt <= amount <= max_amt and 
                    cat == category and subcat == sub_category):
                    return min(base_confidence + boost, 0.98)
        
        return base_confidence
    
    def _enhanced_transaction_mode_detection(self, description: str) -> str:
        """Enhanced transaction mode detection with higher confidence"""
        if not description:
            return "Other"
        
        description_lower = description.lower()
        
        # UPI patterns with specific apps
        upi_patterns = [
            (r'\bupi.*\b(paytm|phonepe|gpay|google\s*pay|bhim|amazon\s*pay)', "UPI"),
            (r'\b(paytm|phonepe|gpay|google\s*pay|bhim).*\bupi\b', "UPI"),
            (r'\bupi\b', "UPI")
        ]
        
        for pattern, mode in upi_patterns:
            if re.search(pattern, description_lower):
                return mode
        
        # Card patterns
        card_patterns = [
            (r'\b(credit\s*card|cc|visa|mastercard|rupay)\b', "Credit Card"),
            (r'\b(debit\s*card|dc|atm)\b', "Debit Card"),
            (r'\bpos\b', "Debit Card"),
        ]
        
        for pattern, mode in card_patterns:
            if re.search(pattern, description_lower):
                return mode
        
        # Net banking patterns
        if any(term in description_lower for term in ['neft', 'rtgs', 'imps', 'net banking', 'online transfer']):
            return "Net Banking"
        
        # Cash patterns
        if any(term in description_lower for term in ['cash', 'withdrawal', 'atm withdrawal']):
            return "Cash"
        
        # Cheque patterns
        if any(term in description_lower for term in ['cheque', 'check', 'chq']):
            return "Cheque"
        
        return "Other"
