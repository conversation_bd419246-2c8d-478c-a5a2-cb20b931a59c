"""
Hierarchical Classification System

This module implements a hierarchical classification approach that learns
category→subcategory mapping preferences from manual labels and uses
active learning for disagreement cases.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime
from collections import defaultdict, Counter
import json
from pathlib import Path

from .enhanced_feature_engineering import EnhancedFeatureEngineer, ExtractedFeatures
from .data_preparation import TransactionDataPreparator
from ..core.logger import get_logger

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.preprocessing import LabelEncoder
    from sklearn.model_selection import cross_val_score
    from sklearn.metrics import classification_report, accuracy_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class HierarchicalPrediction:
    """Result of hierarchical classification"""
    category: str
    subcategory: str
    category_confidence: float
    subcategory_confidence: float
    combined_confidence: float
    prediction_path: List[str]
    alternative_paths: List[Tuple[str, str, float]]


@dataclass
class CategoryHierarchy:
    """Represents the learned category hierarchy"""
    category_to_subcategories: Dict[str, Set[str]] = field(default_factory=dict)
    subcategory_to_category: Dict[str, str] = field(default_factory=dict)
    category_frequencies: Dict[str, int] = field(default_factory=dict)
    subcategory_frequencies: Dict[str, int] = field(default_factory=dict)
    transition_probabilities: Dict[str, Dict[str, float]] = field(default_factory=dict)


@dataclass
class ActiveLearningCandidate:
    """Candidate transaction for active learning"""
    hash_id: str
    description: str
    predicted_category: str
    predicted_subcategory: str
    confidence: float
    uncertainty_score: float
    disagreement_type: str  # "category", "subcategory", "both"
    features: ExtractedFeatures


class HierarchicalClassifier:
    """
    Hierarchical classification system that learns category→subcategory mappings
    and uses active learning for uncertain cases
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        
        # Initialize components
        self.feature_engineer = EnhancedFeatureEngineer()
        self.data_preparator = TransactionDataPreparator()
        
        # Models
        self.category_model = None
        self.subcategory_models: Dict[str, Any] = {}
        self.category_encoder = LabelEncoder()
        self.subcategory_encoders: Dict[str, LabelEncoder] = {}
        
        # Hierarchy
        self.hierarchy = CategoryHierarchy()
        
        # Configuration
        self.config = {
            "min_samples_per_category": 5,
            "min_samples_per_subcategory": 3,
            "uncertainty_threshold": 0.7,
            "active_learning_batch_size": 20,
            "hierarchy_weight": 0.3,  # Weight for hierarchy consistency
            "feature_weight": 0.7,    # Weight for feature-based prediction
        }
        
        # Active learning
        self.active_learning_candidates: List[ActiveLearningCandidate] = []
        
        # Model state
        self.is_trained = False
        self.model_version = "hierarchical_v1.0"
        
        # Load existing hierarchy
        self._load_hierarchy()
    
    def learn_hierarchy(self, training_data: pd.DataFrame) -> CategoryHierarchy:
        """
        Learn category hierarchy from manual labels
        
        Args:
            training_data: DataFrame with manual labels
            
        Returns:
            Learned category hierarchy
        """
        try:
            self.logger.info("Learning category hierarchy from manual labels...")
            
            # Reset hierarchy
            self.hierarchy = CategoryHierarchy()
            
            # Build category→subcategory mappings
            for _, row in training_data.iterrows():
                category = row['category']
                subcategory = row['sub_category']
                
                # Update mappings
                if category not in self.hierarchy.category_to_subcategories:
                    self.hierarchy.category_to_subcategories[category] = set()
                
                self.hierarchy.category_to_subcategories[category].add(subcategory)
                self.hierarchy.subcategory_to_category[subcategory] = category
                
                # Update frequencies
                self.hierarchy.category_frequencies[category] = \
                    self.hierarchy.category_frequencies.get(category, 0) + 1
                self.hierarchy.subcategory_frequencies[subcategory] = \
                    self.hierarchy.subcategory_frequencies.get(subcategory, 0) + 1
            
            # Calculate transition probabilities
            for category, subcategories in self.hierarchy.category_to_subcategories.items():
                total_count = self.hierarchy.category_frequencies[category]
                self.hierarchy.transition_probabilities[category] = {}
                
                for subcategory in subcategories:
                    subcat_count = self.hierarchy.subcategory_frequencies[subcategory]
                    probability = subcat_count / total_count
                    self.hierarchy.transition_probabilities[category][subcategory] = probability
            
            # Save hierarchy
            self._save_hierarchy()
            
            self.logger.info(f"Learned hierarchy: {len(self.hierarchy.category_to_subcategories)} categories, "
                           f"{len(self.hierarchy.subcategory_to_category)} subcategories")
            
            return self.hierarchy
            
        except Exception as e:
            self.logger.error(f"Error learning hierarchy: {str(e)}")
            return self.hierarchy
    
    def train_hierarchical_models(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Train hierarchical classification models
        
        Args:
            training_data: Training data with features and labels
            
        Returns:
            Training results
        """
        try:
            if not SKLEARN_AVAILABLE:
                raise ImportError("scikit-learn not available for hierarchical classification")
            
            self.logger.info("Training hierarchical classification models...")
            
            # Learn hierarchy first
            self.learn_hierarchy(training_data)
            
            # Extract features for all training data
            features_list = []
            categories = []
            subcategories = []
            
            for _, row in training_data.iterrows():
                # Extract features
                features = self.feature_engineer.extract_features(
                    description=row['description'],
                    amount=float(row.get('avg_amount', row.get('amount', 0))),
                    transaction_date=pd.to_datetime(row.get('date', datetime.now())).date(),
                    transaction_type=row.get('transaction_type', 'unknown')
                )
                
                feature_vector = self.feature_engineer.features_to_vector(features)
                features_list.append(feature_vector)
                categories.append(row['category'])
                subcategories.append(row['sub_category'])
            
            X = np.array(features_list)
            
            # Train category model
            self.logger.info("Training category classifier...")
            self.category_model = RandomForestClassifier(
                n_estimators=100,
                random_state=42,
                class_weight='balanced'
            )
            
            # Encode categories
            y_category = self.category_encoder.fit_transform(categories)
            self.category_model.fit(X, y_category)
            
            # Evaluate category model
            category_scores = cross_val_score(self.category_model, X, y_category, cv=5)
            category_accuracy = category_scores.mean()
            
            # Train subcategory models for each category
            self.logger.info("Training subcategory classifiers...")
            subcategory_accuracies = {}
            
            for category in self.hierarchy.category_to_subcategories.keys():
                # Filter data for this category
                category_mask = np.array(categories) == category
                if np.sum(category_mask) < self.config['min_samples_per_category']:
                    self.logger.warning(f"Insufficient samples for category {category}")
                    continue
                
                X_category = X[category_mask]
                y_subcategory = np.array(subcategories)[category_mask]
                
                # Check if we have enough subcategories
                unique_subcats = np.unique(y_subcategory)
                if len(unique_subcats) < 2:
                    self.logger.warning(f"Only one subcategory for {category}, skipping model")
                    continue
                
                # Train subcategory model
                subcategory_model = RandomForestClassifier(
                    n_estimators=50,
                    random_state=42,
                    class_weight='balanced'
                )
                
                # Encode subcategories for this category
                subcategory_encoder = LabelEncoder()
                y_subcategory_encoded = subcategory_encoder.fit_transform(y_subcategory)
                
                subcategory_model.fit(X_category, y_subcategory_encoded)
                
                # Store model and encoder
                self.subcategory_models[category] = subcategory_model
                self.subcategory_encoders[category] = subcategory_encoder
                
                # Evaluate subcategory model
                if len(unique_subcats) >= 2:
                    subcat_scores = cross_val_score(subcategory_model, X_category, 
                                                  y_subcategory_encoded, cv=min(3, len(unique_subcats)))
                    subcategory_accuracies[category] = subcat_scores.mean()
            
            self.is_trained = True
            
            # Return training results
            results = {
                "success": True,
                "category_accuracy": category_accuracy,
                "subcategory_accuracies": subcategory_accuracies,
                "categories_trained": len(self.hierarchy.category_to_subcategories),
                "subcategory_models_trained": len(self.subcategory_models),
                "model_version": self.model_version,
                "training_samples": len(training_data)
            }
            
            self.logger.info(f"Hierarchical training completed. Category accuracy: {category_accuracy:.3f}")
            return results
            
        except Exception as e:
            self.logger.error(f"Error training hierarchical models: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def predict_hierarchical(self, description: str, amount: float = 0.0, 
                           transaction_date: datetime = None, 
                           transaction_type: str = "unknown") -> Optional[HierarchicalPrediction]:
        """
        Make hierarchical prediction for a transaction
        
        Args:
            description: Transaction description
            amount: Transaction amount
            transaction_date: Transaction date
            transaction_type: Transaction type
            
        Returns:
            HierarchicalPrediction or None if prediction fails
        """
        try:
            if not self.is_trained or not SKLEARN_AVAILABLE:
                return None
            
            # Extract features
            features = self.feature_engineer.extract_features(
                description=description,
                amount=amount,
                transaction_date=transaction_date.date() if transaction_date else datetime.now().date(),
                transaction_type=transaction_type
            )
            
            feature_vector = self.feature_engineer.features_to_vector(features).reshape(1, -1)
            
            # Predict category
            category_proba = self.category_model.predict_proba(feature_vector)[0]
            category_pred = self.category_model.predict(feature_vector)[0]
            category = self.category_encoder.inverse_transform([category_pred])[0]
            category_confidence = float(np.max(category_proba))
            
            # Predict subcategory using category-specific model
            subcategory = "Miscellaneous"
            subcategory_confidence = 0.5
            
            if category in self.subcategory_models:
                subcategory_model = self.subcategory_models[category]
                subcategory_encoder = self.subcategory_encoders[category]
                
                subcat_proba = subcategory_model.predict_proba(feature_vector)[0]
                subcat_pred = subcategory_model.predict(feature_vector)[0]
                subcategory = subcategory_encoder.inverse_transform([subcat_pred])[0]
                subcategory_confidence = float(np.max(subcat_proba))
            
            # Apply hierarchy consistency check
            if subcategory in self.hierarchy.subcategory_to_category:
                expected_category = self.hierarchy.subcategory_to_category[subcategory]
                if expected_category != category:
                    # Hierarchy inconsistency - adjust confidence
                    hierarchy_penalty = 0.3
                    category_confidence *= (1 - hierarchy_penalty)
                    subcategory_confidence *= (1 - hierarchy_penalty)
            
            # Calculate combined confidence
            combined_confidence = (category_confidence * 0.6 + subcategory_confidence * 0.4)
            
            # Generate alternative paths
            alternative_paths = self._generate_alternative_paths(
                feature_vector, category_proba, category, features
            )
            
            return HierarchicalPrediction(
                category=category,
                subcategory=subcategory,
                category_confidence=category_confidence,
                subcategory_confidence=subcategory_confidence,
                combined_confidence=combined_confidence,
                prediction_path=[category, subcategory],
                alternative_paths=alternative_paths
            )
            
        except Exception as e:
            self.logger.error(f"Error in hierarchical prediction: {str(e)}")
            return None

    def identify_active_learning_candidates(self, transactions: List[Dict[str, Any]]) -> List[ActiveLearningCandidate]:
        """
        Identify transactions that would benefit from active learning

        Args:
            transactions: List of transaction dictionaries

        Returns:
            List of active learning candidates
        """
        try:
            candidates = []

            for txn in transactions:
                # Make prediction
                prediction = self.predict_hierarchical(
                    description=txn['description'],
                    amount=float(txn.get('amount', 0)),
                    transaction_date=pd.to_datetime(txn.get('date', datetime.now())),
                    transaction_type=txn.get('transaction_type', 'unknown')
                )

                if not prediction:
                    continue

                # Calculate uncertainty score
                uncertainty_score = self._calculate_uncertainty_score(prediction)

                # Check if candidate meets criteria
                if uncertainty_score >= self.config['uncertainty_threshold']:
                    # Extract features for candidate
                    features = self.feature_engineer.extract_features(
                        description=txn['description'],
                        amount=float(txn.get('amount', 0)),
                        transaction_date=pd.to_datetime(txn.get('date', datetime.now())).date(),
                        transaction_type=txn.get('transaction_type', 'unknown')
                    )

                    # Determine disagreement type
                    disagreement_type = self._determine_disagreement_type(prediction)

                    candidate = ActiveLearningCandidate(
                        hash_id=txn.get('hash_id', ''),
                        description=txn['description'],
                        predicted_category=prediction.category,
                        predicted_subcategory=prediction.subcategory,
                        confidence=prediction.combined_confidence,
                        uncertainty_score=uncertainty_score,
                        disagreement_type=disagreement_type,
                        features=features
                    )

                    candidates.append(candidate)

            # Sort by uncertainty score (highest first)
            candidates.sort(key=lambda x: x.uncertainty_score, reverse=True)

            # Limit to batch size
            return candidates[:self.config['active_learning_batch_size']]

        except Exception as e:
            self.logger.error(f"Error identifying active learning candidates: {str(e)}")
            return []

    def _generate_alternative_paths(self, feature_vector: np.ndarray,
                                  category_proba: np.ndarray,
                                  predicted_category: str,
                                  features: ExtractedFeatures) -> List[Tuple[str, str, float]]:
        """Generate alternative prediction paths"""
        try:
            alternatives = []

            # Get top 3 category predictions
            top_indices = np.argsort(category_proba)[-3:][::-1]

            for idx in top_indices:
                category = self.category_encoder.inverse_transform([idx])[0]
                if category == predicted_category:
                    continue

                category_conf = category_proba[idx]

                # Get best subcategory for this category
                if category in self.subcategory_models:
                    subcategory_model = self.subcategory_models[category]
                    subcategory_encoder = self.subcategory_encoders[category]

                    subcat_proba = subcategory_model.predict_proba(feature_vector)[0]
                    subcat_pred = subcategory_model.predict(feature_vector)[0]
                    subcategory = subcategory_encoder.inverse_transform([subcat_pred])[0]
                    subcat_conf = np.max(subcat_proba)

                    combined_conf = category_conf * 0.6 + subcat_conf * 0.4
                else:
                    subcategory = "Miscellaneous"
                    combined_conf = category_conf * 0.6 + 0.5 * 0.4

                alternatives.append((category, subcategory, float(combined_conf)))

            return alternatives

        except Exception as e:
            self.logger.debug(f"Error generating alternative paths: {str(e)}")
            return []

    def _calculate_uncertainty_score(self, prediction: HierarchicalPrediction) -> float:
        """Calculate uncertainty score for active learning"""
        try:
            # Factors contributing to uncertainty:
            # 1. Low confidence scores
            # 2. Close alternative predictions
            # 3. Hierarchy inconsistency

            confidence_uncertainty = 1.0 - prediction.combined_confidence

            # Alternative path uncertainty
            alt_uncertainty = 0.0
            if prediction.alternative_paths:
                best_alt_conf = max(alt[2] for alt in prediction.alternative_paths)
                alt_uncertainty = best_alt_conf  # Higher if alternatives are confident

            # Hierarchy consistency uncertainty
            hierarchy_uncertainty = 0.0
            if (prediction.subcategory in self.hierarchy.subcategory_to_category and
                self.hierarchy.subcategory_to_category[prediction.subcategory] != prediction.category):
                hierarchy_uncertainty = 0.3

            # Combined uncertainty score
            uncertainty = (0.5 * confidence_uncertainty +
                         0.3 * alt_uncertainty +
                         0.2 * hierarchy_uncertainty)

            return min(uncertainty, 1.0)

        except Exception as e:
            self.logger.debug(f"Error calculating uncertainty score: {str(e)}")
            return 0.0

    def _determine_disagreement_type(self, prediction: HierarchicalPrediction) -> str:
        """Determine type of disagreement for active learning"""
        try:
            # Check category confidence
            low_category_conf = prediction.category_confidence < 0.7

            # Check subcategory confidence
            low_subcategory_conf = prediction.subcategory_confidence < 0.7

            # Check hierarchy consistency
            hierarchy_inconsistent = (
                prediction.subcategory in self.hierarchy.subcategory_to_category and
                self.hierarchy.subcategory_to_category[prediction.subcategory] != prediction.category
            )

            if low_category_conf and low_subcategory_conf:
                return "both"
            elif low_category_conf or hierarchy_inconsistent:
                return "category"
            elif low_subcategory_conf:
                return "subcategory"
            else:
                return "confidence"

        except Exception as e:
            self.logger.debug(f"Error determining disagreement type: {str(e)}")
            return "unknown"

    def _load_hierarchy(self):
        """Load saved hierarchy from disk"""
        try:
            hierarchy_file = self.data_dir / "hierarchical_models" / "hierarchy.json"
            if hierarchy_file.exists():
                with open(hierarchy_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Reconstruct hierarchy
                self.hierarchy.category_to_subcategories = {
                    k: set(v) for k, v in data.get('category_to_subcategories', {}).items()
                }
                self.hierarchy.subcategory_to_category = data.get('subcategory_to_category', {})
                self.hierarchy.category_frequencies = data.get('category_frequencies', {})
                self.hierarchy.subcategory_frequencies = data.get('subcategory_frequencies', {})
                self.hierarchy.transition_probabilities = data.get('transition_probabilities', {})

                self.logger.info("Loaded saved hierarchy")

        except Exception as e:
            self.logger.debug(f"Error loading hierarchy: {str(e)}")

    def _save_hierarchy(self):
        """Save hierarchy to disk"""
        try:
            hierarchy_dir = self.data_dir / "hierarchical_models"
            hierarchy_dir.mkdir(parents=True, exist_ok=True)

            hierarchy_file = hierarchy_dir / "hierarchy.json"

            # Convert to serializable format
            data = {
                'category_to_subcategories': {
                    k: list(v) for k, v in self.hierarchy.category_to_subcategories.items()
                },
                'subcategory_to_category': self.hierarchy.subcategory_to_category,
                'category_frequencies': self.hierarchy.category_frequencies,
                'subcategory_frequencies': self.hierarchy.subcategory_frequencies,
                'transition_probabilities': self.hierarchy.transition_probabilities,
                'saved_at': datetime.now().isoformat()
            }

            with open(hierarchy_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.debug("Saved hierarchy to disk")

        except Exception as e:
            self.logger.error(f"Error saving hierarchy: {str(e)}")

    def get_hierarchy_stats(self) -> Dict[str, Any]:
        """Get statistics about the learned hierarchy"""
        return {
            "total_categories": len(self.hierarchy.category_to_subcategories),
            "total_subcategories": len(self.hierarchy.subcategory_to_category),
            "avg_subcategories_per_category": (
                sum(len(subcats) for subcats in self.hierarchy.category_to_subcategories.values()) /
                max(1, len(self.hierarchy.category_to_subcategories))
            ),
            "category_frequencies": self.hierarchy.category_frequencies,
            "subcategory_frequencies": self.hierarchy.subcategory_frequencies,
            "models_trained": {
                "category_model": self.category_model is not None,
                "subcategory_models": len(self.subcategory_models),
                "total_models": 1 + len(self.subcategory_models) if self.category_model else len(self.subcategory_models)
            },
            "is_trained": self.is_trained,
            "model_version": self.model_version
        }
