"""
Integrated categorization system that combines ML and rule-based approaches
Provides seamless integration with existing bank analyzer workflow
"""

import logging
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import time

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.categorizer import TransactionCategorizer
from ..core.logger import get_logger
from .ml_categorizer import MLTransactionCategorizer, MLPrediction

from .data_preparation import TransactionDataPreparator
from .training_data_manager import TrainingDataManager
from .model_trainer import ModelTrainer
from .enhanced_merchant_mapper import EnhancedMerchantMapper


@dataclass
class IntegratedPrediction:
    """Combined prediction from ML and rule-based systems"""
    category: str
    sub_category: str
    confidence: float
    source: str  # "ml", "rules", "hybrid"
    ml_prediction: Optional[MLPrediction] = None
    rule_confidence: float = 0.0
    final_confidence: float = 0.0


class IntegratedCategorizer:
    """
    Integrated categorization system combining ML and rule-based approaches
    Provides fallback mechanisms and confidence-based selection
    """
    
    def __init__(self, main_app_data_dir: str = "data"):
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.rule_categorizer = TransactionCategorizer(main_app_data_dir)
        self.ml_categorizer = MLTransactionCategorizer()
        self.data_preparator = TransactionDataPreparator()
        self.training_manager = TrainingDataManager()
        self.model_trainer = ModelTrainer()
        self.merchant_mapper = EnhancedMerchantMapper()
        
        # Configuration
        self.config = {
            "use_ml": True,
            "ml_confidence_threshold": 0.6,
            "rule_confidence_threshold": 0.5,
            "hybrid_weight_ml": 0.7,
            "hybrid_weight_rules": 0.3,
            "auto_learn_enabled": True,
            "min_confidence_for_auto_learn": 0.8
        }
        
        # Statistics
        self.stats = {
            "total_categorized": 0,
            "ml_used": 0,
            "rules_used": 0,
            "hybrid_used": 0,
            "auto_learned": 0
        }
    
    def categorize_transaction(self, raw_transaction: RawTransaction) -> ProcessedTransaction:
        """
        Categorize a transaction using integrated approach
        
        Args:
            raw_transaction: Raw transaction to categorize
            
        Returns:
            Processed transaction with categorization
        """
        self.stats["total_categorized"] += 1
        
        # Get predictions from all systems
        ml_prediction = None
        rule_prediction = None

        try:
            # Try ML prediction first
            if self.config["use_ml"] and self.ml_categorizer.is_trained:
                ml_prediction = self.ml_categorizer.predict_category(raw_transaction.description)

            # Get rule-based prediction as fallback
            rule_prediction = self.rule_categorizer.categorize_transaction(raw_transaction)

            # Combine predictions
            integrated_prediction = self._combine_predictions(
                raw_transaction, ml_prediction, rule_prediction
            )
            
            # Create processed transaction
            processed = ProcessedTransaction()
            processed.update_from_raw(raw_transaction)
            processed.category = integrated_prediction.category
            processed.sub_category = integrated_prediction.sub_category
            processed.confidence_score = integrated_prediction.final_confidence
            processed.notes = f"Categorized by {integrated_prediction.source}"
            
            # Store ML prediction info if available
            if integrated_prediction.ml_prediction:
                processed.suggested_categories = [{
                    "category": integrated_prediction.ml_prediction.category,
                    "sub_category": integrated_prediction.ml_prediction.sub_category,
                    "confidence": integrated_prediction.ml_prediction.confidence,
                    "source": "ml"
                }]
            
            # Auto-learning: if confidence is high, add to training data
            if (self.config["auto_learn_enabled"] and 
                integrated_prediction.final_confidence >= self.config["min_confidence_for_auto_learn"]):
                self._auto_learn_transaction(raw_transaction, integrated_prediction)
            
            return processed
            
        except Exception as e:
            self.logger.error(f"Error in integrated categorization: {str(e)}")
            
            # Fallback to rule-based only
            if rule_prediction:
                return rule_prediction
            else:
                # Ultimate fallback
                processed = ProcessedTransaction()
                processed.update_from_raw(raw_transaction)
                processed.category = "Other"
                processed.sub_category = "Miscellaneous"
                processed.confidence_score = 0.1
                processed.notes = "Fallback categorization"
                return processed
    
    def _combine_predictions(self, raw_transaction: RawTransaction,
                           ml_prediction: Optional[MLPrediction],
                           rule_prediction: ProcessedTransaction) -> IntegratedPrediction:
        """
        Combine ML and rule-based predictions

        Args:
            raw_transaction: Original transaction
            ml_prediction: ML prediction result
            rule_prediction: Rule-based prediction result

        Returns:
            Combined prediction
        """
        ml_confidence = ml_prediction.confidence if ml_prediction else 0.0
        rule_confidence = rule_prediction.confidence_score

        ml_threshold = self.config["ml_confidence_threshold"]
        rule_threshold = self.config["rule_confidence_threshold"]

        # Decision logic - prioritize ML, then rules
        if ml_prediction and ml_confidence >= ml_threshold:
            if rule_confidence >= rule_threshold:
                # Both systems have confident predictions
                if ml_confidence > rule_confidence + 0.2:
                    # ML is significantly more confident
                    self.stats["ml_used"] += 1
                    return IntegratedPrediction(
                        category=ml_prediction.category,
                        sub_category=ml_prediction.sub_category,
                        confidence=ml_confidence,
                        source="ml",
                        ml_prediction=ml_prediction,
                        rule_confidence=rule_confidence,
                        final_confidence=ml_confidence
                    )
                elif rule_confidence > ml_confidence + 0.2:
                    # Rules are significantly more confident
                    self.stats["rules_used"] += 1
                    return IntegratedPrediction(
                        category=rule_prediction.category,
                        sub_category=rule_prediction.sub_category,
                        confidence=rule_confidence,
                        source="rules",
                        ml_prediction=ml_prediction,
                        rule_confidence=rule_confidence,
                        final_confidence=rule_confidence
                    )
                else:
                    # Similar confidence, use hybrid approach
                    self.stats["hybrid_used"] += 1
                    return self._create_hybrid_prediction(ml_prediction, rule_prediction)
            else:
                # Only ML is confident
                self.stats["ml_used"] += 1
                return IntegratedPrediction(
                    category=ml_prediction.category,
                    sub_category=ml_prediction.sub_category,
                    confidence=ml_confidence,
                    source="ml",
                    ml_prediction=ml_prediction,
                    rule_confidence=rule_confidence,
                    final_confidence=ml_confidence
                )
        elif rule_confidence >= rule_threshold:
            # Only rules are confident
            self.stats["rules_used"] += 1
            return IntegratedPrediction(
                category=rule_prediction.category,
                sub_category=rule_prediction.sub_category,
                confidence=rule_confidence,
                source="rules",
                ml_prediction=ml_prediction,
                rule_confidence=rule_confidence,
                final_confidence=rule_confidence
            )
        else:
            # Neither system is confident, use rules as fallback
            self.stats["rules_used"] += 1
            return IntegratedPrediction(
                category=rule_prediction.category,
                sub_category=rule_prediction.sub_category,
                confidence=rule_confidence,
                source="rules",
                ml_prediction=ml_prediction,
                rule_confidence=rule_confidence,
                final_confidence=max(rule_confidence, 0.1)  # Minimum confidence
            )
    
    def _create_hybrid_prediction(self, ml_prediction: MLPrediction, 
                                rule_prediction: ProcessedTransaction) -> IntegratedPrediction:
        """
        Create hybrid prediction by combining ML and rule-based results
        
        Args:
            ml_prediction: ML prediction
            rule_prediction: Rule-based prediction
            
        Returns:
            Hybrid prediction
        """
        ml_weight = self.config["hybrid_weight_ml"]
        rule_weight = self.config["hybrid_weight_rules"]
        
        # Calculate weighted confidence
        weighted_confidence = (ml_prediction.confidence * ml_weight + 
                             rule_prediction.confidence_score * rule_weight)
        
        # Choose category based on higher confidence
        if ml_prediction.confidence > rule_prediction.confidence_score:
            category = ml_prediction.category
            sub_category = ml_prediction.sub_category
        else:
            category = rule_prediction.category
            sub_category = rule_prediction.sub_category
        
        return IntegratedPrediction(
            category=category,
            sub_category=sub_category,
            confidence=weighted_confidence,
            source="hybrid",
            ml_prediction=ml_prediction,
            rule_confidence=rule_prediction.confidence_score,
            final_confidence=weighted_confidence
        )
    
    def _auto_learn_transaction(self, raw_transaction: RawTransaction, 
                              prediction: IntegratedPrediction):
        """
        Automatically add high-confidence predictions to training data
        
        Args:
            raw_transaction: Original transaction
            prediction: High-confidence prediction
        """
        try:
            # Extract unique transaction
            unique_transactions = self.data_preparator.extract_unique_transactions([raw_transaction])
            
            if unique_transactions:
                # Get the unique transaction
                unique_txn = list(unique_transactions.values())[0]
                
                # Add to training data if not already labeled
                existing_transactions = self.data_preparator.load_unique_transactions()
                
                if unique_txn.hash_id not in existing_transactions:
                    # New transaction, add with auto-label
                    unique_txn.category = prediction.category
                    unique_txn.sub_category = prediction.sub_category
                    unique_txn.confidence = prediction.final_confidence
                    unique_txn.is_manually_labeled = False
                    unique_txn.labeled_by = f"auto_{prediction.source}"
                    unique_txn.labeled_at = datetime.now()
                    
                    # Merge with existing data
                    merged_transactions = self.data_preparator.merge_unique_transactions({
                        unique_txn.hash_id: unique_txn
                    })
                    
                    # Save
                    self.data_preparator.save_unique_transactions(merged_transactions)
                    
                    self.stats["auto_learned"] += 1
                    self.logger.debug(f"Auto-learned transaction: {unique_txn.description[:50]}")
            
        except Exception as e:
            self.logger.error(f"Error in auto-learning: {str(e)}")
    
    def categorize_batch(self, raw_transactions: List[RawTransaction]) -> List[ProcessedTransaction]:
        """
        Categorize a batch of transactions
        
        Args:
            raw_transactions: List of raw transactions
            
        Returns:
            List of processed transactions
        """
        processed_transactions = []
        
        for raw_transaction in raw_transactions:
            try:
                processed = self.categorize_transaction(raw_transaction)
                processed_transactions.append(processed)
            except Exception as e:
                self.logger.error(f"Error categorizing transaction: {str(e)}")
                # Create fallback processed transaction
                processed = ProcessedTransaction()
                processed.update_from_raw(raw_transaction)
                processed.category = "Other"
                processed.sub_category = "Miscellaneous"
                processed.confidence_score = 0.1
                processed.notes = f"Error in categorization: {str(e)}"
                processed_transactions.append(processed)
        
        return processed_transactions
    
    def extract_and_prepare_training_data(self, raw_transactions: List[RawTransaction]) -> Dict[str, Any]:
        """
        Extract unique transactions and prepare for training
        
        Args:
            raw_transactions: List of raw transactions
            
        Returns:
            Extraction results
        """
        try:
            # Extract unique transactions
            unique_transactions = self.data_preparator.extract_unique_transactions(raw_transactions)
            
            # Merge with existing data
            merged_transactions = self.data_preparator.merge_unique_transactions(unique_transactions)
            
            # Save
            success = self.data_preparator.save_unique_transactions(merged_transactions)
            
            return {
                "success": success,
                "new_unique_transactions": len(unique_transactions),
                "total_unique_transactions": len(merged_transactions),
                "ready_for_labeling": len([t for t in merged_transactions.values() if not t.is_manually_labeled])
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting training data: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get status of the integrated categorization system"""
        ml_info = self.ml_categorizer.get_model_info()
        training_status = self.model_trainer.get_training_status()
        labeling_stats = self.training_manager.get_labeling_stats()
        
        return {
            "ml_available": ml_info["sklearn_available"],
            "ml_trained": ml_info["is_trained"],
            "model_version": ml_info.get("model_version", "None"),
            "training_in_progress": training_status["is_training"],
            "auto_retrain_enabled": training_status["auto_retrain_enabled"],
            "can_auto_retrain": training_status["can_auto_retrain"],
            "total_unique_transactions": labeling_stats.total_unique_transactions,
            "labeled_transactions": labeling_stats.labeled_transactions,
            "labeling_progress": labeling_stats.labeling_progress,
            "categorization_stats": self.stats.copy(),
            "config": self.config.copy()
        }
    
    def update_config(self, **kwargs) -> bool:
        """
        Update configuration
        
        Args:
            **kwargs: Configuration parameters to update
            
        Returns:
            True if updated successfully
        """
        try:
            self.config.update(kwargs)
            self.logger.info(f"Updated integrated categorizer config: {kwargs}")
            return True
        except Exception as e:
            self.logger.error(f"Error updating config: {str(e)}")
            return False
    
    def trigger_model_training(self) -> Optional[str]:
        """
        Trigger model training if conditions are met
        
        Returns:
            Job ID if training started, None otherwise
        """
        try:
            # Check if we have sufficient training data
            labeling_stats = self.training_manager.get_labeling_stats()
            
            if labeling_stats.labeled_transactions < 10:
                self.logger.warning("Insufficient training data for model training")
                return None
            
            # Create and start training job
            job_id = self.model_trainer.create_training_job("manual")
            
            if self.model_trainer.start_training(job_id, async_training=True):
                self.logger.info(f"Started model training job: {job_id}")
                return job_id
            else:
                self.logger.error("Failed to start model training")
                return None
                
        except Exception as e:
            self.logger.error(f"Error triggering model training: {str(e)}")
            return None
    
    def check_and_trigger_auto_retrain(self) -> Optional[str]:
        """
        Check conditions and trigger automatic retraining if needed
        
        Returns:
            Job ID if auto-retraining started, None otherwise
        """
        try:
            return self.model_trainer.trigger_auto_retrain()
        except Exception as e:
            self.logger.error(f"Error in auto-retrain check: {str(e)}")
            return None

    def get_stats(self) -> Dict[str, Any]:
        """
        Get categorizer statistics

        Returns:
            Dictionary with categorization statistics
        """
        total = max(1, self.stats["total_categorized"])  # Avoid division by zero

        stats = {
            **self.stats,
            "ml_success_rate": (self.stats["ml_used"] / total) * 100,
            "rules_success_rate": (self.stats["rules_used"] / total) * 100,
            "hybrid_success_rate": (self.stats["hybrid_used"] / total) * 100
        }

        return stats

    def get_budget_status(self) -> Dict[str, Any]:
        """
        Get budget status information (simplified without SambaNova)

        Returns:
            Dictionary with budget status information
        """
        return {
            'warning_level': 'safe',
            'can_continue': True,
            'total_budget': 0,
            'daily_budget': 0,
            'total_used': 0,
            'daily_used': 0,
            'total_remaining': 0,
            'daily_remaining': 0,
            'total_usage_pct': 0,
            'daily_usage_pct': 0,
            'total_requests': 0,
            'daily_requests': 0
        }

    def get_merchant_mapping_stats(self) -> Dict[str, Any]:
        """
        Get merchant mapping statistics from the enhanced merchant mapper

        Returns:
            Dictionary with merchant mapping statistics
        """
        try:
            if hasattr(self, 'merchant_mapper'):
                return self.merchant_mapper.get_merchant_statistics()
            else:
                # Return empty stats if merchant mapper not available
                return {
                    'total_patterns': 0,
                    'active_patterns': 0,
                    'high_confidence_patterns': 0,
                    'ai_learned': 0,
                    'manual_learned': 0,
                    'hybrid_learned': 0,
                    'total_matches': 0,
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'cache_hit_rate': 0.0,
                    'patterns_created_today': 0,
                    'category_distribution': {},
                    'last_updated': None
                }
        except Exception as e:
            self.logger.error(f"Error getting merchant mapping stats: {str(e)}")
            # Return empty stats on error
            return {
                'total_patterns': 0,
                'active_patterns': 0,
                'high_confidence_patterns': 0,
                'ai_learned': 0,
                'manual_learned': 0,
                'hybrid_learned': 0,
                'total_matches': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'cache_hit_rate': 0.0,
                'patterns_created_today': 0,
                'category_distribution': {},
                'last_updated': None
            }

    def get_merchant_mapping_stats(self) -> Dict[str, Any]:
        """
        Get merchant mapping statistics (simplified version for compatibility)

        Returns:
            Dictionary with basic merchant mapping stats
        """
        # Return basic stats since we don't have the enhanced merchant mapper
        return {
            "total_patterns": 0,
            "cache_hit_rate": 0.0,
            "patterns_learned_today": 0,
            "total_transactions_processed": self.stats["total_categorized"],
            "cost_saved": 0.0
        }
