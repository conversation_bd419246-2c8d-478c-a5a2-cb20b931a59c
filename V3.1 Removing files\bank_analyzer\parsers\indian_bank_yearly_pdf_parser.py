"""
Indian Bank Yearly PDF Statement Parser
Specialized parser for Indian Bank yearly PDF statements (2024 format)
Handles large multi-page yearly statements without requiring "Indian Bank" text validation
"""

import re
import warnings
from pathlib import Path
from typing import List, Optional
from decimal import Decimal
from datetime import datetime

# Suppress warnings
warnings.filterwarnings('ignore')

try:
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

from .base_parser import BaseStatementParser
from ..models.transaction import RawTransaction
from ..core.logger import get_logger


class IndianBankYearlyPDFParser(BaseStatementParser):
    """
    Specialized parser for Indian Bank yearly PDF statements
    Handles the specific format used in yearly statements without text validation
    """
    
    def __init__(self, bank_name: str = "Indian Bank"):
        self.bank_name = bank_name
        self.logger = get_logger(__name__)
        self.parsing_errors = []
        self.supported_formats = ['.pdf']
        
        # Date formats used in Indian Bank yearly PDFs
        self.date_formats = [
            '%d %b %Y',      # 05 Jan 2024
            '%d/%m/%Y',      # 05/01/2024
            '%d-%m-%Y',      # 05-01-2024
            '%d.%m.%Y',      # 05.01.2024
        ]
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if this parser can handle the file"""
        if not PDF_AVAILABLE:
            return False
        
        if file_path.suffix.lower() != '.pdf':
            return False
        
        try:
            # Check for yearly statement indicators instead of "Indian Bank" text
            import PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                if len(pdf_reader.pages) > 0:
                    first_page_text = pdf_reader.pages[0].extract_text().lower()
                    
                    # Look for yearly statement patterns
                    yearly_indicators = [
                        'account statement',
                        'for period:',
                        '01 jan 2024 - 31 dec 2024',
                        'account holder name',
                        'account type',
                        'account number'
                    ]
                    
                    indicator_count = sum(1 for indicator in yearly_indicators if indicator in first_page_text)
                    
                    # If we find multiple indicators, it's likely a yearly statement
                    return indicator_count >= 3
        except:
            pass
        
        return False
    
    def parse(self, file_path: Path) -> List[RawTransaction]:
        """Parse Indian Bank yearly PDF statement"""
        if not PDF_AVAILABLE:
            self.logger.error("pdfplumber not available for PDF parsing")
            return []

        transactions = []

        try:
            self.logger.info(f"Parsing Indian Bank yearly PDF: {file_path}")

            with pdfplumber.open(file_path) as pdf:
                self.logger.info(f"PDF has {len(pdf.pages)} pages")

                for page_num, page in enumerate(pdf.pages):
                    self.logger.debug(f"Processing page {page_num + 1}/{len(pdf.pages)}")

                    # Extract text from page (this PDF is text-based, not table-based)
                    page_text = page.extract_text()
                    if page_text:
                        page_transactions = self._extract_from_text(page_text, page_num + 1, file_path)
                        transactions.extend(page_transactions)

                        if page_transactions:
                            self.logger.debug(f"Page {page_num + 1}: {len(page_transactions)} transactions")

            self.logger.info(f"Yearly PDF parser extracted {len(transactions)} transactions")
            return transactions

        except Exception as e:
            self.logger.error(f"Error in yearly PDF parser: {str(e)}")
            return []
    
    def _extract_from_table(self, table: List[List], page_num: int, file_path: Path) -> List[RawTransaction]:
        """Extract transactions from a PDF table"""
        transactions = []
        
        if not table or len(table) < 2:
            return transactions
        
        # Find header row
        header_row_idx = self._find_header_row(table)
        if header_row_idx == -1:
            return transactions
        
        self.logger.debug(f"Found header at row {header_row_idx} on page {page_num}")
        
        # Process data rows after header
        for row_idx in range(header_row_idx + 1, len(table)):
            row = table[row_idx]
            if not row:
                continue
            
            # Skip empty rows
            if not any(cell and str(cell).strip() for cell in row):
                continue
            
            # Try to extract transaction
            transaction = self._extract_transaction_from_row(row, page_num, row_idx, file_path)
            if transaction:
                transactions.append(transaction)
        
        return transactions
    
    def _find_header_row(self, table: List[List]) -> int:
        """Find the header row in the table"""
        for i, row in enumerate(table):
            if not row:
                continue
            
            row_text = ' '.join(str(cell).lower() if cell else '' for cell in row)
            
            # Look for header patterns
            if ('date' in row_text and 
                'transaction' in row_text and 
                ('debit' in row_text or 'credit' in row_text) and
                'balance' in row_text):
                return i
        
        return -1
    
    def _extract_transaction_from_row(self, row: List, page_num: int, row_idx: int, file_path: Path) -> Optional[RawTransaction]:
        """Extract a transaction from a table row"""
        try:
            # Convert all cells to strings
            cells = [str(cell).strip() if cell else '' for cell in row]
            
            if len(cells) < 4:
                return None
            
            # Find date (usually in first few columns)
            transaction_date = None
            date_col_idx = -1
            
            for i in range(min(3, len(cells))):
                if cells[i]:
                    parsed_date = self._parse_date(cells[i])
                    if parsed_date:
                        transaction_date = parsed_date
                        date_col_idx = i
                        break
            
            if not transaction_date:
                return None
            
            # Find description (usually after date)
            description = ""
            desc_start_idx = date_col_idx + 1
            
            # Collect description from multiple columns if needed
            for i in range(desc_start_idx, len(cells)):
                if cells[i] and not self._looks_like_amount(cells[i]):
                    if description:
                        description += " " + cells[i]
                    else:
                        description = cells[i]
                else:
                    break
            
            if not description or len(description) < 5:
                return None
            
            # Find amounts (look for debit/credit columns)
            debit_amount = None
            credit_amount = None
            
            for i, cell in enumerate(cells):
                if self._looks_like_amount(cell):
                    amount = self._parse_amount(cell)
                    if amount:
                        # Determine if this is debit or credit based on position or context
                        # In Indian Bank format, typically: Date | Description | Debit | Credit | Balance
                        if i < len(cells) - 1:  # Not the last column (balance)
                            if not debit_amount:
                                debit_amount = amount
                            elif not credit_amount:
                                credit_amount = amount
            
            # Must have at least one amount
            if not debit_amount and not credit_amount:
                return None
            
            # Determine transaction type and final amount
            if credit_amount and credit_amount > 0:
                transaction_type = "CREDIT"
                final_amount = credit_amount
            elif debit_amount and debit_amount > 0:
                transaction_type = "DEBIT"
                final_amount = debit_amount
            else:
                return None
            
            # Create transaction
            return RawTransaction(
                date=transaction_date.date() if hasattr(transaction_date, 'date') else transaction_date,
                description=description.strip(),
                amount=final_amount,
                transaction_type=transaction_type,
                source_file=str(file_path),
                source_line=f"Page{page_num}Row{row_idx}",
                bank_name=self.bank_name,
                reference_number=f"IB-PDF-P{page_num}R{row_idx}"
            )
            
        except Exception as e:
            self.logger.debug(f"Error extracting transaction from row {row_idx} on page {page_num}: {e}")
            return None
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string using Indian Bank formats"""
        if not date_str:
            return None
        
        date_str = date_str.strip()
        
        for fmt in self.date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        return None
    
    def _looks_like_amount(self, value: str) -> bool:
        """Check if a value looks like a monetary amount"""
        if not value:
            return False
        
        # Remove common currency symbols and separators
        cleaned = re.sub(r'[₹,\s]', '', value.strip())
        
        # Check if it matches amount patterns
        amount_patterns = [
            r'^\d+$',           # 1000
            r'^\d+\.\d{1,2}$',  # 1000.50
        ]
        
        for pattern in amount_patterns:
            if re.match(pattern, cleaned):
                return True
        
        return False
    
    def _parse_amount(self, amount_str: str) -> Optional[Decimal]:
        """Parse amount string to Decimal"""
        if not amount_str:
            return None

        try:
            # Remove currency symbols and clean up
            cleaned = re.sub(r'[₹INR,\s]', '', amount_str.strip())

            if not cleaned or cleaned in ['-', '']:
                return None

            return Decimal(cleaned)

        except (ValueError, TypeError):
            return None

    def _extract_from_text(self, page_text: str, page_num: int, file_path: Path) -> List[RawTransaction]:
        """Extract transactions from text-based PDF page"""
        transactions = []

        if not page_text:
            return transactions

        lines = page_text.split('\n')

        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Try to extract transaction from this line
            transaction = self._extract_transaction_from_text_line(line, page_num, line_num, file_path)
            if transaction:
                transactions.append(transaction)

        return transactions

    def _extract_transaction_from_text_line(self, line: str, page_num: int, line_num: int, file_path: Path) -> Optional[RawTransaction]:
        """Extract transaction from a single text line"""
        try:
            # Pattern for Indian Bank yearly PDF transactions:
            # "05 Jan 2024 IDIB000P152/Mr V - INR 100.00 INR 652.77"
            # "07 Jan 2024 PYTM0123456/Meesho INR 73.00 - INR 3,079.77"

            # Look for date at the beginning
            date_match = re.match(r'^(\d{1,2}\s+\w{3}\s+\d{4})\s+(.+)', line)
            if not date_match:
                return None

            date_str = date_match.group(1)
            rest_of_line = date_match.group(2)

            # Parse the date
            transaction_date = self._parse_date(date_str)
            if not transaction_date:
                return None

            # Extract amounts and description from the rest of the line
            # Pattern: Description [Debit] [Credit] Balance
            # Look for INR amounts at the end
            amount_pattern = r'INR\s+([\d,]+\.\d{2})'
            amounts = re.findall(amount_pattern, rest_of_line)

            if len(amounts) < 2:  # Need at least transaction amount and balance
                return None

            # The last amount is always the balance
            balance_amount = amounts[-1]

            # Determine debit/credit amounts
            debit_amount = None
            credit_amount = None

            if len(amounts) == 3:
                # Format: Description Debit Credit Balance
                debit_amount = self._parse_amount(f"INR {amounts[0]}")
                credit_amount = self._parse_amount(f"INR {amounts[1]}")
            elif len(amounts) == 2:
                # Format: Description Amount Balance (need to determine if debit or credit)
                transaction_amount = self._parse_amount(f"INR {amounts[0]}")

                # Look for "-" to determine if it's debit or credit
                # Pattern: "Description - INR amount" (credit) or "Description INR amount -" (debit)
                if ' - INR ' in rest_of_line:
                    credit_amount = transaction_amount
                elif 'INR ' in rest_of_line and ' - ' in rest_of_line:
                    debit_amount = transaction_amount
                else:
                    # Default to debit if unclear
                    debit_amount = transaction_amount

            # Extract description (everything before the first INR amount)
            # Find the position of the first INR amount
            first_inr_pos = rest_of_line.find('INR')
            if first_inr_pos > 0:
                description_part = rest_of_line[:first_inr_pos].strip()
            else:
                description_part = rest_of_line

            # Clean up description
            description = re.sub(r'\s*-\s*$', '', description_part).strip()  # Remove trailing dash
            description = re.sub(r'\s+', ' ', description)

            if not description or len(description) < 5:
                return None

            # Determine final transaction type and amount
            if credit_amount and credit_amount > 0:
                transaction_type = "CREDIT"
                final_amount = credit_amount
            elif debit_amount and debit_amount > 0:
                transaction_type = "DEBIT"
                final_amount = debit_amount
            else:
                return None

            # Create transaction
            return RawTransaction(
                date=transaction_date.date() if hasattr(transaction_date, 'date') else transaction_date,
                description=description,
                amount=final_amount,
                transaction_type=transaction_type,
                source_file=str(file_path),
                source_line=f"Page{page_num}Line{line_num}",
                bank_name=self.bank_name,
                reference_number=f"IB-PDF-P{page_num}L{line_num}"
            )

        except Exception as e:
            self.logger.debug(f"Error extracting transaction from line {line_num} on page {page_num}: {e}")
            return None
