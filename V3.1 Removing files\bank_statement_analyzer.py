#!/usr/bin/env python3
"""
Bank Statement Analyzer and Importer
A comprehensive application for analyzing bank statements and importing transactions
into the main Personal Finance Dashboard application.

Features:
- Parse multiple bank statement formats (PDF, CSV, Excel)
- Automatic transaction categorization
- Preview and review interface with editing capabilities
- Data validation and import to main application
- Modern PySide6 UI with comprehensive logging

Author: Personal Finance Dashboard
Version: 1.0.0
"""

import sys
import os
import logging
import warnings
from pathlib import Path
from datetime import datetime

# Suppress openpyxl styling warnings for cleaner output
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl.styles.stylesheet')

# Standalone bank analyzer - no external dependencies needed
current_dir = Path(__file__).parent

# Smart NLTK setup - check if data exists before downloading
try:
    sys.path.insert(0, str(current_dir))
    import nltk
    import socket

    # Check if NLTK data already exists
    def check_nltk_data():
        try:
            nltk.data.find('corpora/stopwords')
            nltk.data.find('tokenizers/punkt')
            return True
        except LookupError:
            return False

    if not check_nltk_data():
        print("📥 NLTK data not found, downloading...")
        # Set a reasonable timeout for downloads
        original_timeout = socket.getdefaulttimeout()
        socket.setdefaulttimeout(5)  # 5 second timeout

        try:
            # Try to download essential NLTK data
            nltk.download('stopwords', quiet=True)
            nltk.download('punkt', quiet=True)
            print("✅ NLTK data downloaded successfully")
        except Exception as download_error:
            print(f"⚠️ NLTK download failed: {download_error}")
            print("📱 Using offline text processing mode")
        finally:
            # Restore original timeout
            socket.setdefaulttimeout(original_timeout)
    else:
        print("✅ NLTK data already available")

except Exception as e:
    print(f"⚠️ NLTK setup failed: {e} - using offline text processing")

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# Import our modules
from bank_analyzer.ui.main_window import BankAnalyzerMainWindow
from bank_analyzer.core.logger import setup_logging


def main():
    """Main entry point for the Bank Statement Analyzer application"""
    
    # Setup logging with debug mode for troubleshooting
    # Check if debug mode is requested via command line argument
    debug_mode = "--debug" in sys.argv
    setup_logging(debug_mode=debug_mode)
    logger = logging.getLogger(__name__)

    if debug_mode:
        logger.info("🔧 DEBUG MODE ENABLED - All logs will show in terminal")
    logger.info("Starting Bank Statement Analyzer application")

    # NLTK is already initialized at startup, just log status
    try:
        import nltk
        # Check if data is available
        try:
            nltk.data.find('corpora/stopwords')
            nltk.data.find('tokenizers/punkt')
            logger.info("✅ NLTK data available for text processing")
        except LookupError:
            logger.info("⚠️ NLTK data not available - using offline text processing")
    except Exception as e:
        logger.info(f"⚠️ NLTK not available: {e} - using offline text processing")
    
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Bank Statement Analyzer")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Personal Finance Dashboard")
    
    # Set application icon if available
    icon_path = current_dir / "assets" / "icons" / "bank_analyzer.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # Enable high DPI scaling (suppress deprecation warnings)
    import warnings
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", DeprecationWarning)
        try:
            app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            # These attributes are deprecated in newer Qt versions
            pass
    
    try:
        # Create and show main window
        main_window = BankAnalyzerMainWindow()
        main_window.show()
        
        logger.info("Bank Statement Analyzer application started successfully")
        
        # Run the application
        exit_code = app.exec()
        logger.info(f"Bank Statement Analyzer application exited with code: {exit_code}")
        
        return exit_code
        
    except Exception as e:
        logger.error(f"Failed to start Bank Statement Analyzer application: {str(e)}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())
 