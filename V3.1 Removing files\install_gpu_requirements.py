#!/usr/bin/env python3
"""
GPU Requirements Installation Script
Automated installation of CUDA-enabled PyTorch and GPU dependencies
"""

import subprocess
import sys
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected")
        print("💡 Python 3.8+ required for GPU acceleration")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_nvidia_drivers():
    """Check if NVIDIA drivers are installed"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA drivers detected")
            # Extract CUDA version
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version:' in line:
                    cuda_version = line.split('CUDA Version:')[1].strip().split()[0]
                    print(f"🚀 System CUDA version: {cuda_version}")
                    return cuda_version
        return None
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ NVIDIA drivers not found")
        print("💡 Install NVIDIA drivers from: https://www.nvidia.com/drivers/")
        return None

def install_base_requirements():
    """Install base requirements first"""
    print("\n📦 Installing base requirements...")
    
    base_packages = [
        "pandas>=1.5.0",
        "numpy>=1.24.0",
        "scikit-learn>=1.3.0",
        "joblib>=1.3.0",
        "transformers>=4.30.0",
        "accelerate>=0.20.0",
        "datasets>=2.14.0",
        "nltk>=3.8.0",
        "tokenizers>=0.13.0",
        "requests>=2.31.0",
        "jsonschema>=4.17.0",
        "python-dateutil>=2.8.0",
        "colorama>=0.4.6",
        "tqdm>=4.65.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "psutil>=5.9.0"
    ]
    
    for package in base_packages:
        print(f"   Installing {package.split('>=')[0]}...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"   ⚠️ Warning: Failed to install {package}")
    
    print("✅ Base requirements installed")

def install_cuda_pytorch(cuda_version=None):
    """Install CUDA-enabled PyTorch"""
    print("\n🚀 Installing CUDA-enabled PyTorch...")
    
    # Determine CUDA version for PyTorch
    if cuda_version and cuda_version.startswith('12.'):
        torch_cuda = "cu121"  # CUDA 12.1
        print(f"   Using CUDA 12.1 PyTorch for system CUDA {cuda_version}")
    elif cuda_version and cuda_version.startswith('11.'):
        torch_cuda = "cu118"  # CUDA 11.8
        print(f"   Using CUDA 11.8 PyTorch for system CUDA {cuda_version}")
    else:
        torch_cuda = "cu121"  # Default to CUDA 12.1
        print("   Using default CUDA 12.1 PyTorch")
    
    # Uninstall existing PyTorch first
    print("   Removing existing PyTorch installations...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 
                       'torch', 'torchvision', 'torchaudio', '-y'], 
                      capture_output=True)
    except:
        pass
    
    # Install CUDA PyTorch
    pytorch_url = f"https://download.pytorch.org/whl/{torch_cuda}"
    pytorch_packages = [
        "torch>=2.5.0",
        "torchvision>=0.16.0", 
        "torchaudio>=2.1.0"
    ]
    
    print(f"   Installing from {pytorch_url}...")
    try:
        cmd = [sys.executable, '-m', 'pip', 'install'] + pytorch_packages + ['--index-url', pytorch_url]
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=600)
        print("✅ CUDA PyTorch installed successfully")
        return True
    except subprocess.TimeoutExpired:
        print("❌ Installation timed out")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        return False

def verify_cuda_installation():
    """Verify CUDA PyTorch installation"""
    print("\n🧪 Verifying CUDA installation...")
    
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        
        cuda_available = torch.cuda.is_available()
        print(f"🎮 CUDA available: {'✅ Yes' if cuda_available else '❌ No'}")
        
        if cuda_available:
            print(f"💻 GPU device: {torch.cuda.get_device_name(0)}")
            print(f"🚀 CUDA version: {torch.version.cuda}")
            
            # Test GPU operations
            print("🔬 Testing GPU operations...")
            x = torch.randn(100, 100).cuda()
            y = torch.randn(100, 100).cuda()
            z = torch.mm(x, y)
            print("✅ GPU tensor operations working!")
            
            return True
        else:
            print("❌ CUDA not available in PyTorch")
            return False
            
    except ImportError:
        print("❌ PyTorch not found")
        return False
    except Exception as e:
        print(f"❌ Error testing CUDA: {e}")
        return False

def main():
    """Main installation function"""
    print("🛠️ GPU REQUIREMENTS INSTALLER")
    print("=" * 50)
    print("Installing CUDA-enabled PyTorch for RTX 4050/4060 and similar GPUs")
    print()
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check NVIDIA drivers
    cuda_version = check_nvidia_drivers()
    if not cuda_version:
        print("\n❌ NVIDIA drivers required for GPU acceleration")
        print("💡 Install drivers first, then run this script again")
        return False
    
    # Install base requirements
    install_base_requirements()
    
    # Install CUDA PyTorch
    pytorch_success = install_cuda_pytorch(cuda_version)
    if not pytorch_success:
        print("\n❌ Failed to install CUDA PyTorch")
        print("💡 Try manual installation:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
        return False
    
    # Verify installation
    cuda_working = verify_cuda_installation()
    
    # Final summary
    print("\n" + "=" * 50)
    print("📋 INSTALLATION SUMMARY")
    print("-" * 20)
    
    if cuda_working:
        print("🎉 GPU requirements installed successfully!")
        print("✅ CUDA-enabled PyTorch working")
        print("✅ GPU acceleration ready")
        print("\n💡 Next steps:")
        print("1. Run: python train_gpu_model.py")
        print("2. Run: python quick_system_demo.py")
        print("3. Enjoy GPU-accelerated categorization! 🚀")
        return True
    else:
        print("⚠️ Installation completed but CUDA not working")
        print("💡 Try restarting your Python environment")
        print("💡 Or install manually with specific CUDA version")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n🔧 For manual installation, see requirements.txt")
        sys.exit(1)
