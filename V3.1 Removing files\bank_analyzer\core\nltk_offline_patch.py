"""
NLTK Offline Patch
Simple patch to disable NLTK downloads
"""

from pathlib import Path


def patch_nltk_downloads():
    """Patch NLTK to prevent downloads"""
    try:
        import nltk
        def no_download(*args, **kwargs):
            return True
        nltk.download = no_download
        return True
    except:
        return False


def create_offline_stopwords():
    """Create offline stopwords if they don't exist"""
    try:
        current_dir = Path(__file__).parent.parent
        stopwords_dir = current_dir / "ml" / "nltk_data" / "corpora" / "stopwords"
        stopwords_dir.mkdir(parents=True, exist_ok=True)
        
        english_file = stopwords_dir / "english"
        if not english_file.exists():
            stopwords = """i
me
my
myself
we
our
ours
ourselves
you
your
yours
yourself
yourselves
he
him
his
himself
she
her
hers
herself
it
its
itself
they
them
their
theirs
themselves
what
which
who
whom
this
that
these
those
am
is
are
was
were
be
been
being
have
has
had
having
do
does
did
doing
a
an
the
and
but
if
or
because
as
until
while
of
at
by
for
with
through
during
before
after
above
below
up
down
in
out
on
off
over
under
again
further
then
once
here
there
when
where
why
how
all
any
both
each
few
more
most
other
some
such
no
nor
not
only
own
same
so
than
too
very
s
t
can
will
just
don
should
now"""
            
            with open(english_file, 'w', encoding='utf-8') as f:
                f.write(stopwords)
        
        return True
    except Exception:
        return False


def get_offline_stopwords():
    """Get stopwords without any downloads"""
    # Try bundled data first
    try:
        current_dir = Path(__file__).parent.parent
        english_file = current_dir / "ml" / "nltk_data" / "corpora" / "stopwords" / "english"
        
        if english_file.exists():
            with open(english_file, 'r', encoding='utf-8') as f:
                return set(line.strip() for line in f if line.strip())
    except Exception:
        pass
    
    # Fallback to hardcoded stopwords
    return {
        'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
        'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
        'to', 'was', 'will', 'with', 'i', 'me', 'my', 'we', 'our', 'you',
        'your', 'this', 'these', 'they', 'them', 'their', 'have', 'had',
        'but', 'or', 'if', 'while', 'when', 'where', 'why', 'how', 'all',
        'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
        'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very',
        'can', 'could', 'should', 'would', 'may', 'might', 'must', 'shall',
        'am', 'been', 'being', 'do', 'does', 'did', 'doing', 'get', 'got',
        'getting', 'go', 'going', 'gone', 'went', 'come', 'came', 'coming'
    }


def simple_tokenize(text):
    """Simple tokenization without NLTK"""
    if not text:
        return []
    
    import string
    import re
    
    # Convert to lowercase
    text = text.lower()
    
    # Replace punctuation with spaces
    text = text.translate(str.maketrans(string.punctuation, ' ' * len(string.punctuation)))
    
    # Split on whitespace and filter empty strings
    tokens = [token for token in text.split() if token]
    
    return tokens


class OfflineStemmer:
    """Simple offline stemmer"""
    
    def __init__(self):
        self.suffixes = [
            'ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion', 'ness', 'ment', 
            'able', 'ible', 'ful', 'less', 'ous', 'ive', 'al', 'ic', 'ical'
        ]
    
    def stem(self, word):
        """Basic stemming"""
        if not word or len(word) <= 3:
            return word
        
        word = word.lower()
        for suffix in sorted(self.suffixes, key=len, reverse=True):
            if word.endswith(suffix) and len(word) > len(suffix) + 2:
                return word[:-len(suffix)]
        return word


# Note: Functions are available for import but not executed automatically
# Call patch_nltk_downloads() and create_offline_stopwords() manually when needed
