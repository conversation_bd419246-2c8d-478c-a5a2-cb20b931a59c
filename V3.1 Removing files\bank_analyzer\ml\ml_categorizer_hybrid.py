#!/usr/bin/env python3
"""
Hybrid ML Categorizer Wrapper
Maintains compatibility with existing API while using the new hybrid system
"""

from typing import Optional, NamedTuple
from pathlib import Path
import sys
import os

# Add current directory to path for hybrid system import
current_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(current_dir))

try:
    from hybrid_ml_categorizer import HybridMLCategorizer, PredictionResult
except ImportError:
    print("Error: Could not import hybrid_ml_categorizer")
    sys.exit(1)

class CategoryPrediction(NamedTuple):
    """Maintains compatibility with existing prediction format"""
    category: str
    sub_category: str
    confidence: float
    method: str = "hybrid"

class MLTransactionCategorizer:
    """
    Hybrid ML Transaction Categorizer
    Drop-in replacement for the original MLTransactionCategorizer
    """
    
    def __init__(self):
        self.hybrid_system = HybridMLCategorizer()
        self.is_initialized = False
        
    def initialize(self):
        """Initialize the hybrid system"""
        if not self.is_initialized:
            self.hybrid_system.initialize()
            self.is_initialized = True
    
    def predict_category(self, description: str, amount: float = 0.0, date: str = "2025-01-01") -> Optional[CategoryPrediction]:
        """
        Predict category for a transaction
        
        Args:
            description: Transaction description
            amount: Transaction amount (optional, defaults to 0.0)
            date: Transaction date (optional, defaults to current date)
            
        Returns:
            CategoryPrediction with category, subcategory, confidence, and method
        """
        if not self.is_initialized:
            self.initialize()
        
        try:
            result = self.hybrid_system.predict_category(description, amount, date)
            
            return CategoryPrediction(
                category=result.category,
                sub_category=result.sub_category,
                confidence=result.confidence,
                method=result.method
            )
        except Exception as e:
            print(f"Error in hybrid prediction: {e}")
            return None
    
    def get_model_info(self):
        """Get information about the hybrid model"""
        if not self.is_initialized:
            self.initialize()
            
        return {
            "type": "hybrid_ml_system",
            "exact_matches": len(self.hybrid_system.exact_match_lookup),
            "ml_models_loaded": self.hybrid_system.ml_category_model is not None,
            "subcategory_models": len(self.hybrid_system.ml_subcategory_models),
            "version": "1.0.0"
        }
    
    def train_models(self, force_retrain=False):
        """
        Training is not needed for hybrid system as it uses pre-labeled data
        This method is kept for API compatibility
        """
        if not self.is_initialized:
            self.initialize()
            
        # Validate the system works correctly
        validation_results = self.hybrid_system.validate_on_training_data()
        
        return {
            "success": validation_results["accuracy"] == 1.0,
            "accuracy": validation_results["accuracy"],
            "method_distribution": validation_results["method_distribution"],
            "message": "Hybrid system validated successfully" if validation_results["accuracy"] == 1.0 else "Validation failed"
        }

# For backward compatibility
class TransactionDataPreparator:
    """Dummy class for backward compatibility"""
    
    def get_training_data(self):
        """Return the labeled training data"""
        import pandas as pd
        return pd.read_csv("data/processed_statements/transactions_Indian_Bank_20250706_154905.csv")
