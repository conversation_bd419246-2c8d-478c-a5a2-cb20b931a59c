"""
Indian Bank Excel Statement Parser
Specialized parser for Indian Bank Excel statement format
Handles yearly statements with 1000+ transactions
"""

import pandas as pd
import re
import warnings
from pathlib import Path
from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime

# Suppress openpyxl styling warnings
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl.styles.stylesheet')

from .base_parser import BaseStatementParser
from ..models.transaction import RawTransaction
from ..core.logger import get_logger


class IndianBankExcelParser(BaseStatementParser):
    """
    Indian Bank Excel Statement Parser
    Optimized for Indian Bank's specific Excel format
    """
    
    def __init__(self, bank_name: str = "Indian Bank"):
        self.bank_name = bank_name
        self.logger = get_logger(__name__)
        self.parsing_errors = []
        self.supported_formats = ['.xlsx', '.xls', '.xlsm']
        
        # Indian Bank specific date formats
        self.date_formats = [
            '%d/%m/%Y',      # 01/07/2025
            '%d-%m-%Y',      # 01-07-2025
            '%d.%m.%Y',      # 01.07.2025
            '%d %m %Y',      # 01 07 2025
            '%d %b %Y',      # 01 Jul 2025
            '%d-%b-%Y',      # 01-Jul-2025
            '%d/%b/%Y',      # 01/Jul/2025
            '%Y-%m-%d',      # 2025-07-01
        ]
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if this parser can handle the file"""
        if file_path.suffix.lower() not in self.supported_formats:
            return False

        try:
            # Quick check to see if this looks like an Indian Bank Excel file
            import pandas as pd

            # Read just the first few rows to check for Indian Bank indicators
            df = pd.read_excel(file_path, nrows=20)

            # Convert all data to strings for searching
            all_text = ""
            for col in df.columns:
                all_text += str(col).lower() + " "

            for row in df.values:
                for cell in row:
                    if pd.notna(cell):
                        all_text += str(cell).lower() + " "

            # Look for Indian Bank indicators
            indian_bank_indicators = [
                'indian bank',
                'account statement',
                'account number',
                'account holder',
                'transaction details',
                'debits',
                'credits',
                'balance'
            ]

            # Count how many indicators we find
            indicator_count = sum(1 for indicator in indian_bank_indicators if indicator in all_text)

            # If we find multiple indicators, it's likely an Indian Bank file
            is_indian_bank = indicator_count >= 3

            if is_indian_bank:
                self.logger.info(f"Detected Indian Bank Excel file: {file_path}")
            else:
                self.logger.debug(f"Not an Indian Bank Excel file (indicators: {indicator_count}): {file_path}")

            return is_indian_bank

        except Exception as e:
            self.logger.debug(f"Error checking Excel file {file_path}: {str(e)}")
            # If we can't read the file, assume it's not compatible
            return False
    
    def parse(self, file_path: Path) -> List[RawTransaction]:
        """
        Parse Indian Bank Excel statement
        Optimized for yearly statements with many transactions
        """
        transactions = []
        
        try:
            self.logger.info(f"Parsing Indian Bank Excel statement: {file_path}")
            
            # Read Excel file
            df = pd.read_excel(file_path)
            
            self.logger.info(f"Excel file has {len(df)} rows and {len(df.columns)} columns")
            self.logger.debug(f"Columns: {list(df.columns)}")
            
            # Analyze the structure first
            self._analyze_excel_structure(df)
            
            # Extract transactions using Indian Bank specific logic
            transactions = self._extract_indian_bank_transactions(df, file_path)
            
            self.logger.info(f"Indian Bank Excel parser extracted {len(transactions)} transactions")
            return transactions
            
        except Exception as e:
            self.logger.error(f"Error in Indian Bank Excel parser: {str(e)}")
            return []
    
    def _analyze_excel_structure(self, df: pd.DataFrame):
        """Analyze the Excel structure to understand the format"""
        self.logger.debug("Analyzing Indian Bank Excel structure...")
        
        # Look at first few rows to identify header patterns
        for i in range(min(10, len(df))):
            row_values = [str(val) if pd.notna(val) else '' for val in df.iloc[i].values]
            row_text = ' '.join(row_values).lower()
            self.logger.debug(f"Row {i}: {row_text[:100]}...")
            
            # Check for Indian Bank specific headers
            if any(pattern in row_text for pattern in [
                'transaction date', 'txn date', 'date',
                'particulars', 'description', 'narration',
                'debit', 'withdrawal', 'dr',
                'credit', 'deposit', 'cr',
                'balance', 'running balance'
            ]):
                self.logger.debug(f"Found potential header row at index {i}")
    
    def _extract_indian_bank_transactions(self, df: pd.DataFrame, file_path: Path) -> List[RawTransaction]:
        """Extract transactions using Indian Bank specific logic"""
        transactions = []

        # Detect format type (yearly vs monthly)
        format_type = self._detect_format_type(df)
        self.logger.info(f"Detected format type: {format_type}")

        if format_type == "yearly":
            transactions = self._extract_yearly_format_transactions(df, file_path)
        else:
            transactions = self._extract_monthly_format_transactions(df, file_path)

        return transactions

    def _detect_format_type(self, df: pd.DataFrame) -> str:
        """Detect if this is yearly or monthly format"""
        # Look for yearly format indicators in more rows
        for i in range(min(150, len(df))):  # Check more rows
            row_values = [str(val) if pd.notna(val) else '' for val in df.iloc[i].values]
            row_text = ' '.join(row_values).lower()

            # Yearly format has specific header pattern with all these elements
            if ('date' in row_text and 'transaction details' in row_text and
                'debits' in row_text and 'credits' in row_text and 'balance' in row_text):
                return "yearly"

        return "monthly"

    def _extract_yearly_format_transactions(self, df: pd.DataFrame, file_path: Path) -> List[RawTransaction]:
        """Extract transactions from yearly format with specialized multi-row handling"""
        transactions = []

        self.logger.info("Processing Indian Bank yearly format with multi-row transaction support")

        # Process all rows with date-based transaction grouping
        i = 0
        while i < len(df):
            try:
                row_values = [str(val) if pd.notna(val) else '' for val in df.iloc[i].values]

                # Skip empty rows
                if not any(cell.strip() for cell in row_values if cell):
                    i += 1
                    continue

                # Skip header rows
                if self._is_yearly_header_row(row_values):
                    self.logger.debug(f"Skipping header row {i}")
                    i += 1
                    continue

                # Check if this row starts a new transaction (has date in column 1)
                date_str = row_values[1].strip() if len(row_values) > 1 else ""
                if date_str and self._parse_yearly_date(date_str):
                    # This is a transaction row - extract it and any continuation rows
                    transaction, rows_consumed = self._extract_multi_row_transaction(df, i, file_path)
                    if transaction:
                        transactions.append(transaction)
                        self.logger.debug(f"Extracted transaction from row {i}, consumed {rows_consumed} rows")
                    i += rows_consumed
                else:
                    # Not a transaction start row, skip it
                    i += 1

            except Exception as e:
                self.logger.debug(f"Error processing yearly row {i}: {e}")
                i += 1

        return transactions

    def _extract_monthly_format_transactions(self, df: pd.DataFrame, file_path: Path) -> List[RawTransaction]:
        """Extract transactions from monthly format (original logic)"""
        transactions = []

        # Skip initial rows that might contain account info/headers
        start_row = self._find_data_start_row(df)
        self.logger.debug(f"Starting monthly format data extraction from row {start_row}")

        for row_num in range(start_row, len(df)):
            try:
                row = df.iloc[row_num]
                row_values = [str(val) if pd.notna(val) else '' for val in row.values]

                # Skip empty rows
                if not any(cell.strip() for cell in row_values if cell):
                    continue

                # Skip obvious header/summary rows
                if self._is_header_or_summary_row(row_values):
                    continue

                # Extract transaction
                transaction = self._extract_indian_bank_transaction(row_values, row_num, file_path)
                if transaction:
                    transactions.append(transaction)

            except Exception as e:
                self.logger.debug(f"Error processing monthly row {row_num}: {e}")
                continue

        return transactions
    
    def _find_data_start_row(self, df: pd.DataFrame) -> int:
        """Find the row where actual transaction data starts"""
        for i in range(min(20, len(df))):  # Check first 20 rows
            row_values = [str(val) if pd.notna(val) else '' for val in df.iloc[i].values]
            
            # Look for a row that has date-like pattern in first few columns
            for j in range(min(3, len(row_values))):
                if self._looks_like_date(row_values[j]):
                    # Check if this row also has description and amount
                    if self._has_transaction_pattern(row_values):
                        return i
        
        return 0  # Default to start from beginning
    
    def _looks_like_date(self, value: str) -> bool:
        """Check if a value looks like a date"""
        if not value or value.strip() == '':
            return False
        
        value = str(value).strip()
        
        # Common date patterns
        date_patterns = [
            r'^\d{1,2}/\d{1,2}/\d{4}$',     # 1/7/2025 or 01/07/2025
            r'^\d{1,2}-\d{1,2}-\d{4}$',     # 1-7-2025 or 01-07-2025
            r'^\d{4}-\d{1,2}-\d{1,2}$',     # 2025-7-1 or 2025-07-01
            r'^\d{1,2}\.\d{1,2}\.\d{4}$',   # 1.7.2025 or 01.07.2025
        ]
        
        for pattern in date_patterns:
            if re.match(pattern, value):
                return True
        
        return False
    
    def _has_transaction_pattern(self, row_values: List[str]) -> bool:
        """Check if row has typical transaction pattern"""
        # Should have at least 4-5 columns: Date, Description, Amount(s), Balance
        if len(row_values) < 4:
            return False
        
        # First column should be date-like
        if not self._looks_like_date(row_values[0]):
            return False
        
        # Should have at least one amount-like value
        amount_count = 0
        for value in row_values[2:]:  # Skip date and description columns
            if self._looks_like_amount(value):
                amount_count += 1
        
        return amount_count >= 1
    
    def _looks_like_amount(self, value: str) -> bool:
        """Check if a value looks like a monetary amount"""
        if not value or value.strip() == '' or value.strip() == '-':
            return False
        
        value = str(value).strip()
        
        # Remove common currency symbols and commas
        value = re.sub(r'[₹,\s]', '', value)
        
        # Check if it's a valid number
        try:
            amount = float(value)
            return 0.01 <= amount <= ********.99
        except ValueError:
            return False
    
    def _is_header_or_summary_row(self, row_values: List[str]) -> bool:
        """Check if this is a header or summary row to skip"""
        row_text = ' '.join(row_values).lower()
        
        # Indian Bank specific skip patterns
        skip_patterns = [
            'transaction date', 'txn date', 'date',
            'particulars', 'description', 'narration',
            'debit', 'withdrawal', 'dr amount',
            'credit', 'deposit', 'cr amount',
            'balance', 'running balance',
            'opening balance', 'closing balance',
            'total debit', 'total credit',
            'account number', 'customer name',
            'statement period', 'generated on',
            'indian bank', 'page no'
        ]
        
        for pattern in skip_patterns:
            if pattern in row_text and len(row_text) < 100:
                return True
        
        return False
    
    def _extract_indian_bank_transaction(self, row_values: List[str], row_num: int, file_path: Path) -> Optional[RawTransaction]:
        """Extract a single transaction from Indian Bank Excel row"""
        try:
            # Find date (usually in first column)
            transaction_date = None
            for i in range(min(3, len(row_values))):  # Check first 3 columns for date
                date_val = self.parse_date_string(row_values[i])
                if date_val:
                    transaction_date = date_val
                    break
            
            if not transaction_date:
                return None
            
            # Find description (usually in second column, or longest text)
            description = self._find_description(row_values)
            if not description or len(description) < 3:
                return None
            
            # Find amounts - Indian Bank typically has Debit, Credit, Balance columns
            debit_amount, credit_amount = self._find_indian_bank_amounts(row_values)
            
            if not debit_amount and not credit_amount:
                return None
            
            # Determine transaction type and final amount
            if credit_amount:
                transaction_type = "CREDIT"
                final_amount = credit_amount
            else:
                transaction_type = "DEBIT"
                final_amount = debit_amount
            
            # Create transaction
            return RawTransaction(
                date=transaction_date.date() if hasattr(transaction_date, 'date') else transaction_date,
                description=description,
                amount=final_amount,
                transaction_type=transaction_type,
                source_file=str(file_path),
                source_line=row_num + 1,  # Excel rows are 1-based
                bank_name=self.bank_name,
                reference_number=f"IB{row_num + 1}"
            )
            
        except Exception as e:
            self.logger.debug(f"Error extracting Indian Bank transaction from row {row_num}: {e}")
            return None
    
    def _find_description(self, row_values: List[str]) -> Optional[str]:
        """Find description in row values"""
        # Usually the second column, but find the longest meaningful text
        description = ""
        
        for i, value in enumerate(row_values):
            if not value or value.strip() == '':
                continue
            
            value_str = str(value).strip()
            
            # Skip date and amount columns
            if self._looks_like_date(value_str) or self._looks_like_amount(value_str):
                continue
            
            # Skip common non-description values
            if value_str.lower() in ['dr', 'cr', '-', 'debit', 'credit']:
                continue
            
            # Take the longest meaningful text
            if len(value_str) > len(description):
                description = value_str
        
        return description if description else None
    
    def _find_indian_bank_amounts(self, row_values: List[str]) -> tuple:
        """Find debit and credit amounts in Indian Bank format"""
        debit_amount = None
        credit_amount = None
        
        # Look for amounts in columns (skip first 2 for date/description)
        amount_values = []
        for i in range(2, len(row_values)):
            if self._looks_like_amount(row_values[i]):
                try:
                    value = str(row_values[i]).strip()
                    # Remove currency symbols and commas
                    value = re.sub(r'[₹,\s]', '', value)
                    amount = Decimal(value)
                    amount_values.append(amount)
                except:
                    continue
        
        # Indian Bank format typically: Date | Description | Debit | Credit | Balance
        # Or: Date | Description | Amount | Balance (with type indicated elsewhere)
        if len(amount_values) >= 2:
            # Assume first amount is debit, second is credit (if both present)
            # Last amount is usually balance, so use the ones before it
            if len(amount_values) >= 3:
                debit_amount = amount_values[0] if amount_values[0] > 0 else None
                credit_amount = amount_values[1] if amount_values[1] > 0 else None
            else:
                # Only two amounts - one transaction amount, one balance
                debit_amount = amount_values[0]
        elif len(amount_values) == 1:
            # Single amount - determine type from context or assume debit
            debit_amount = amount_values[0]
        
        return debit_amount, credit_amount

    def _find_yearly_header_row(self, df: pd.DataFrame) -> int:
        """Find the header row for yearly format"""
        for i in range(min(150, len(df))):  # Check first 150 rows
            row_values = [str(val) if pd.notna(val) else '' for val in df.iloc[i].values]
            row_text = ' '.join(row_values).lower()

            # Look for the specific yearly header pattern
            if ('date' in row_text and 'transaction details' in row_text and
                'debits' in row_text and 'credits' in row_text and 'balance' in row_text):
                return i

        return -1

    def _is_header_row(self, row_values: List[str]) -> bool:
        """Check if this row is a header row that should be skipped"""
        row_text = ' '.join(row_values).lower()

        # Check for exact header patterns
        if ('date' in row_text and 'transaction details' in row_text and
            'debits' in row_text and 'credits' in row_text and 'balance' in row_text):
            return True

        # Check for individual column headers in their expected positions
        if (len(row_values) > 13 and
            row_values[1].strip().lower() == 'date' and
            'transaction details' in row_values[2].strip().lower() and
            row_values[8].strip().lower() == 'debits' and
            row_values[10].strip().lower() == 'credits' and
            row_values[13].strip().lower() == 'balance'):
            return True

        return False

    def _extract_yearly_transaction(self, row_values: List[str], row_num: int, file_path: Path) -> Optional[RawTransaction]:
        """Extract transaction from yearly format row"""
        try:
            # Yearly format structure based on analysis:
            # Column 1: Date (e.g., "31 Jan 2024")
            # Column 2: Transaction Details (long description)
            # Column 8: Debits (e.g., "INR 100.00" or " - ")
            # Column 10: Credits (e.g., "INR 3,900.00" or " - ")
            # Column 13: Balance (e.g., "INR 5,287.12")

            # Extract date from column 1
            date_str = row_values[1].strip() if len(row_values) > 1 else ""

            # Check if this is a continuation row (no date, no amounts, only description)
            if not date_str or date_str == "":
                # This might be a continuation row - skip it
                return None

            transaction_date = self._parse_yearly_date(date_str)
            if not transaction_date:
                return None

            # Extract description from column 2
            description = row_values[2].strip() if len(row_values) > 2 else ""
            if not description or len(description) < 5:
                return None

            # Check if this is a continuation row with only partial description
            # Common continuation patterns: "BRANCH", "ATM SERVICE BRANCH", etc.
            if self._is_continuation_row(description, row_values):
                return None

            # Extract amounts from columns 8 (debits) and 10 (credits)
            debit_str = row_values[8].strip() if len(row_values) > 8 else ""
            credit_str = row_values[10].strip() if len(row_values) > 10 else ""

            # Parse amounts
            debit_amount = self._parse_yearly_amount(debit_str)
            credit_amount = self._parse_yearly_amount(credit_str)

            # Must have at least one valid amount - this helps filter out continuation rows
            if not debit_amount and not credit_amount:
                return None

            # Additional check: if this row has no amounts but has description,
            # it's likely a continuation row
            if (debit_str in ['', '-', ' - '] and credit_str in ['', '-', ' - ']):
                return None

            # Determine transaction type and final amount
            if credit_amount and credit_amount > 0:
                transaction_type = "CREDIT"
                final_amount = credit_amount
            elif debit_amount and debit_amount > 0:
                transaction_type = "DEBIT"
                final_amount = debit_amount
            else:
                return None

            # Create transaction
            return RawTransaction(
                date=transaction_date.date() if hasattr(transaction_date, 'date') else transaction_date,
                description=description,
                amount=final_amount,
                transaction_type=transaction_type,
                source_file=str(file_path),
                source_line=row_num + 1,
                bank_name=self.bank_name,
                reference_number=f"IB-Y{row_num + 1}"
            )

        except Exception as e:
            self.logger.debug(f"Error extracting yearly transaction from row {row_num}: {e}")
            return None

    def _parse_yearly_date(self, date_str: str) -> Optional[datetime]:
        """Parse date from yearly format (e.g., '31 Jan 2024')"""
        if not date_str:
            return None

        # Yearly format uses "DD MMM YYYY" format
        yearly_date_formats = [
            '%d %b %Y',      # 31 Jan 2024
            '%d %B %Y',      # 31 January 2024
            '%d/%m/%Y',      # 31/01/2024
            '%d-%m-%Y',      # 31-01-2024
        ]

        for fmt in yearly_date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        return None

    def _parse_yearly_amount(self, amount_str: str) -> Optional[Decimal]:
        """Parse amount from yearly format (e.g., 'INR 3,900.00' or ' - ')"""
        if not amount_str or amount_str.strip() in ['', '-', ' - ']:
            return None

        try:
            # Remove INR prefix and clean up
            cleaned = amount_str.replace('INR', '').replace(',', '').strip()

            # Skip if it's just a dash or empty
            if cleaned in ['', '-', ' - ']:
                return None

            return Decimal(cleaned)

        except (ValueError, TypeError):
            return None

    def _is_continuation_row(self, description: str, row_values: List[str]) -> bool:
        """Check if this is a continuation row from a previous transaction"""
        # Check if this row has no date and no amounts (typical continuation pattern)
        date_str = row_values[1].strip() if len(row_values) > 1 else ""
        debit_str = row_values[8].strip() if len(row_values) > 8 else ""
        credit_str = row_values[10].strip() if len(row_values) > 10 else ""
        balance_str = row_values[13].strip() if len(row_values) > 13 else ""

        # If no date, no amounts, and no balance, it's likely a continuation
        has_no_date = not date_str or date_str == ""
        has_no_amounts = (debit_str in ['', '-', ' - '] and credit_str in ['', '-', ' - '])
        has_no_balance = not balance_str or balance_str == ""

        # Common continuation patterns
        continuation_patterns = [
            'branch',
            'atm service branch',
            'mumbai fort',
            '/upi/branch',
            '/branch :',
            'service branch'
        ]

        description_lower = description.lower()

        # If it has no date, no amounts, no balance, and matches continuation patterns
        if has_no_date and has_no_amounts and has_no_balance:
            for pattern in continuation_patterns:
                if pattern in description_lower:
                    return True

        # Additional check: if description is very short and looks like a fragment
        if has_no_date and has_no_amounts and len(description) < 30:
            for pattern in continuation_patterns:
                if pattern in description_lower:
                    return True

        return False

    def _is_yearly_header_row(self, row_values: List[str]) -> bool:
        """Check if this row is a yearly format header row"""
        if len(row_values) < 14:
            return False

        # Check for exact header pattern in expected columns
        date_col = row_values[1].strip().lower() if len(row_values) > 1 else ""
        desc_col = row_values[2].strip().lower() if len(row_values) > 2 else ""
        debit_col = row_values[8].strip().lower() if len(row_values) > 8 else ""
        credit_col = row_values[10].strip().lower() if len(row_values) > 10 else ""
        balance_col = row_values[13].strip().lower() if len(row_values) > 13 else ""

        # Check for exact header match
        if (date_col == 'date' and
            'transaction details' in desc_col and
            debit_col == 'debits' and
            credit_col == 'credits' and
            balance_col == 'balance'):
            return True

        return False

    def _extract_multi_row_transaction(self, df: pd.DataFrame, start_row: int, file_path: Path) -> tuple:
        """
        Extract a multi-row transaction starting from start_row
        Returns (transaction, rows_consumed)
        """
        if start_row >= len(df):
            return None, 1

        # Get the main transaction row
        main_row_values = [str(val) if pd.notna(val) else '' for val in df.iloc[start_row].values]

        # Extract basic transaction data from main row
        date_str = main_row_values[1].strip() if len(main_row_values) > 1 else ""
        transaction_date = self._parse_yearly_date(date_str)

        if not transaction_date:
            return None, 1

        # Build complete description by combining main row and continuation rows
        main_description = main_row_values[2].strip() if len(main_row_values) > 2 else ""
        full_description = main_description

        # Extract amounts from main row
        debit_str = main_row_values[8].strip() if len(main_row_values) > 8 else ""
        credit_str = main_row_values[10].strip() if len(main_row_values) > 10 else ""
        balance_str = main_row_values[13].strip() if len(main_row_values) > 13 else ""

        # Parse amounts
        debit_amount = self._parse_yearly_amount(debit_str)
        credit_amount = self._parse_yearly_amount(credit_str)

        # Must have at least one valid amount
        if not debit_amount and not credit_amount:
            return None, 1

        # Look for continuation rows (rows without date but with description)
        rows_consumed = 1
        current_row = start_row + 1

        while current_row < len(df):
            cont_row_values = [str(val) if pd.notna(val) else '' for val in df.iloc[current_row].values]

            # Check if this is a continuation row
            cont_date = cont_row_values[1].strip() if len(cont_row_values) > 1 else ""
            cont_desc = cont_row_values[2].strip() if len(cont_row_values) > 2 else ""
            cont_debit = cont_row_values[8].strip() if len(cont_row_values) > 8 else ""
            cont_credit = cont_row_values[10].strip() if len(cont_row_values) > 10 else ""
            cont_balance = cont_row_values[13].strip() if len(cont_row_values) > 13 else ""

            # If this row has a date, it's a new transaction
            if cont_date and self._parse_yearly_date(cont_date):
                break

            # If this row is a header, stop
            if self._is_yearly_header_row(cont_row_values):
                break

            # If this row is empty, skip it but don't stop (might be spacing)
            if not any(cell.strip() for cell in cont_row_values if cell):
                current_row += 1
                rows_consumed += 1
                continue

            # If this row has description but no date/amounts/balance, it's a continuation
            if (cont_desc and len(cont_desc) > 3 and
                not cont_date and
                cont_debit in ['', '-', ' - '] and
                cont_credit in ['', '-', ' - '] and
                not cont_balance):

                # Add continuation text to description
                full_description += " " + cont_desc
                current_row += 1
                rows_consumed += 1
            else:
                # Not a continuation row, stop here
                break

        # Validate transaction
        if not full_description or len(full_description) < 5:
            return None, rows_consumed

        # Determine transaction type and amount
        if credit_amount and credit_amount > 0:
            transaction_type = "CREDIT"
            final_amount = credit_amount
        elif debit_amount and debit_amount > 0:
            transaction_type = "DEBIT"
            final_amount = debit_amount
        else:
            return None, rows_consumed

        # Create transaction
        transaction = RawTransaction(
            date=transaction_date.date() if hasattr(transaction_date, 'date') else transaction_date,
            description=full_description.strip(),
            amount=final_amount,
            transaction_type=transaction_type,
            source_file=str(file_path),
            source_line=start_row + 1,
            bank_name=self.bank_name,
            reference_number=f"IB-Y{start_row + 1}"
        )

        return transaction, rows_consumed
