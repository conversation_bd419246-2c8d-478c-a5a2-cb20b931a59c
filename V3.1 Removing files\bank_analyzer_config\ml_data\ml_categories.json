[{"id": "ml_salary_1", "name": "Salary", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_zomato_payout_2", "name": "Zomato Payout", "parent_id": "ml_salary_1", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_swiggy_payout_3", "name": "Swiggy Payout", "parent_id": "ml_salary_1", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_swiggy_tips_4", "name": "Swiggy <PERSON>s", "parent_id": "ml_salary_1", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_zomato_tips_5", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_salary_1", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_shadowfax_payout_6", "name": "Shadowfax Payout", "parent_id": "ml_salary_1", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_shadow_fax_tips_7", "name": "Shadow Fax Tips", "parent_id": "ml_salary_1", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_rapido_payout_8", "name": "Rapido Payout", "parent_id": "ml_salary_1", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_rapido_tips_9", "name": "Rapido Tips", "parent_id": "ml_salary_1", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_investment_returns_10", "name": "Investment Returns", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_auragold_11", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_investment_returns_10", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_aurasilver_12", "name": "Aurasilver", "parent_id": "ml_investment_returns_10", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_stockmarket_13", "name": "Stockmarket", "parent_id": "ml_investment_returns_10", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_freelance_income_14", "name": "Freelance Income", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_website_building_15", "name": "Website Building", "parent_id": "ml_freelance_income_14", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_refunds_16", "name": "Refunds", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_flipkart_refunds_17", "name": "Flipkart Refunds", "parent_id": "ml_refunds_16", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_snapmint_18", "name": "Snapmint", "parent_id": "ml_refunds_16", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_meesho_refunds_19", "name": "Meesho Refunds", "parent_id": "ml_refunds_16", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_zepto_refunds_20", "name": "Zepto Refunds", "parent_id": "ml_refunds_16", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_youtube_income_21", "name": "Youtube Income", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_id_sales_22", "name": "Id Sales", "parent_id": "ml_youtube_income_21", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_gp_links_23", "name": "Gp Links", "parent_id": "ml_youtube_income_21", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_adsense_24", "name": "Adsense", "parent_id": "ml_youtube_income_21", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_website_25", "name": "Website", "parent_id": "ml_youtube_income_21", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_card_account_sales_26", "name": "Card Account Sales", "parent_id": "ml_youtube_income_21", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_investment_withdraw_27", "name": "Investment Withdraw", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_auragold_28", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_investment_withdraw_27", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_aurasilver_29", "name": "Aurasilver", "parent_id": "ml_investment_withdraw_27", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_investments_30", "name": "Investments", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_auragold_31", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_investments_30", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_aurasilver_32", "name": "Aurasilver", "parent_id": "ml_investments_30", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_lic_policy_33", "name": "Lic Policy", "parent_id": "ml_investments_30", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_zerodha_34", "name": "<PERSON><PERSON>", "parent_id": "ml_investments_30", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_intrest_35", "name": "<PERSON><PERSON><PERSON>", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_bank_intrest_indian_bank_36", "name": "Bank Intrest Indian Bank", "parent_id": "ml_intrest_35", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_borrowed_37", "name": "Borrowed", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_from_dad_38", "name": "From Dad", "parent_id": "ml_borrowed_37", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_from_friends_39", "name": "From Friends", "parent_id": "ml_borrowed_37", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_from_gf_40", "name": "From Gf", "parent_id": "ml_borrowed_37", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_rewards_41", "name": "Rewards", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_gpay_tasks_42", "name": "Gpay Tasks", "parent_id": "ml_rewards_41", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_business_payouts_43", "name": "Business Payouts", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_zomato_kitchen_payout_44", "name": "Zomato Kitchen Payout", "parent_id": "ml_business_payouts_43", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_swiggy_payout_45", "name": "Swiggy Payout", "parent_id": "ml_business_payouts_43", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_shared_money_46", "name": "Shared Money", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_from_dad_47", "name": "From Dad", "parent_id": "ml_shared_money_46", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_from_vasanth_48", "name": "From Vasanth", "parent_id": "ml_shared_money_46", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_from_<PERSON><PERSON><PERSON>_49", "name": "From <PERSON><PERSON><PERSON>", "parent_id": "ml_shared_money_46", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_from_abirami_50", "name": "From Abirami", "parent_id": "ml_shared_money_46", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_youtube_51", "name": "Youtube", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_adsence_52", "name": "Adsence", "parent_id": "ml_youtube_51", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_setttings_53", "name": "Setttings", "parent_id": "ml_youtube_51", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_gplinks_54", "name": "Gplinks", "parent_id": "ml_youtube_51", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_id_sales_55", "name": "Id Sales", "parent_id": "ml_youtube_51", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_card_account_sales_56", "name": "Card Account Sales", "parent_id": "ml_youtube_51", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_bank_intrest_57", "name": "Bank Intrest", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_indian_bank_intrest_58", "name": "Indian Bank Intrest", "parent_id": "ml_bank_intrest_57", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_sbi_intrest_59", "name": "Sbi Intrest", "parent_id": "ml_bank_intrest_57", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "income", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_service_expenses_60", "name": "Service Expenses", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_laptop_service_61", "name": "Laptop Service", "parent_id": "ml_service_expenses_60", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_print_income_62", "name": "Print Income", "parent_id": "ml_service_expenses_60", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_savings_63", "name": "Savings", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_1_year_savings_64", "name": "1 Year Savings", "parent_id": "ml_savings_63", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_unexpected_65", "name": "Unexpected", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_uncategorised_66", "name": "Uncategorised", "parent_id": "ml_unexpected_65", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_loans_67", "name": "Loans", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_mpocket_68", "name": "Mpocket", "parent_id": "ml_loans_67", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_idfc_flipkart_paylater_69", "name": "Idfc Flipkart Paylater", "parent_id": "ml_loans_67", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_credit_card_deposit_70", "name": "Credit Card Deposit", "parent_id": "ml_loans_67", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_cash_limit_71", "name": "Cash Limit", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_zomato_cash_limit_72", "name": "Zomato Cash Limit", "parent_id": "ml_cash_limit_71", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_swiggy_cash_limit_73", "name": "Swiggy <PERSON>it", "parent_id": "ml_cash_limit_71", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_shadowfax_cashlimit_74", "name": "Shadowfax Cashlimit", "parent_id": "ml_cash_limit_71", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_college_75", "name": "College", "parent_id": "ml_cash_limit_71", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_gf_76", "name": "Gf", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_fees_77", "name": "Fees", "parent_id": "ml_gf_76", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_emi_78", "name": "<PERSON><PERSON>", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_housing_loan_79", "name": "Housing Loan", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_ola_s1_x_1_80", "name": "Ola S1 X 1", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_ola_s1_x_2_81", "name": "Ola S1 X 2", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_ola_s1_pro_82", "name": "Ola S1 Pro", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_iphone_16_83", "name": "Iphone 16", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_dji_osmo_action_2_84", "name": "Dji Osmo Action 2", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_moneyview_app_85", "name": "Moneyview App", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_kreditbee_86", "name": "<PERSON><PERSON><PERSON><PERSON>", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_slice_87", "name": "Slice", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_mpokket_88", "name": "Mpokket", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_truebalance_89", "name": "Truebalance", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_intial_amount_90", "name": "Intial Amount", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_flipkart_paylater_91", "name": "<PERSON><PERSON><PERSON><PERSON>", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_snapmint_92", "name": "Snapmint", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_initial_payment_93", "name": "Initial Payment", "parent_id": "ml_emi_78", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_house_hold_expenses_94", "name": "House Hold Expenses", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_daily_expenses_95", "name": "Daily Expenses", "parent_id": "ml_house_hold_expenses_94", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_gas_96", "name": "Gas", "parent_id": "ml_house_hold_expenses_94", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_oil_97", "name": "Oil", "parent_id": "ml_house_hold_expenses_94", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_rice_items_98", "name": "Rice Items", "parent_id": "ml_house_hold_expenses_94", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_online_stores_99", "name": "Online Stores", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_amazon_100", "name": "Amazon", "parent_id": "ml_online_stores_99", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_meesho_101", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_online_stores_99", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_flipkart_102", "name": "<PERSON><PERSON><PERSON><PERSON>", "parent_id": "ml_online_stores_99", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_zepto_103", "name": "Zepto", "parent_id": "ml_online_stores_99", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_xpressbees_104", "name": "Xpressbees", "parent_id": "ml_online_stores_99", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_appliances_105", "name": "Appliances", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_house_hold_106", "name": "House Hold", "parent_id": "ml_appliances_105", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_vasanth_needs_107", "name": "<PERSON><PERSON><PERSON><PERSON>s", "parent_id": "ml_appliances_105", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_common_needs_108", "name": "Common Needs", "parent_id": "ml_appliances_105", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_computer_upgrades_109", "name": "Computer Upgrades", "parent_id": "ml_appliances_105", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_computer_maintenance_110", "name": "Computer Maintenance", "parent_id": "ml_appliances_105", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_basic_setup_111", "name": "Basic Setup", "parent_id": "ml_appliances_105", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_laptop_repairs_112", "name": "Laptop Repairs", "parent_id": "ml_appliances_105", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_mobile_repairs_113", "name": "Mobile Repairs", "parent_id": "ml_appliances_105", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_returned_114", "name": "Returned", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_to_dad_115", "name": "To <PERSON>", "parent_id": "ml_returned_114", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_to_friends_116", "name": "To Friends", "parent_id": "ml_returned_114", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_to_gf_117", "name": "To Gf", "parent_id": "ml_returned_114", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_recharge_118", "name": "Recharge", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_landline_119", "name": "Landline", "parent_id": "ml_recharge_118", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_sun_direct_120", "name": "Sun Direct", "parent_id": "ml_recharge_118", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_a<PERSON><PERSON>_mobile_121", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_recharge_118", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_vasanth_mobile_122", "name": "Vasanth Mobile", "parent_id": "ml_recharge_118", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_lend_to_123", "name": "Lend To", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_friends_124", "name": "Friends", "parent_id": "ml_lend_to_123", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_bro_125", "name": "<PERSON><PERSON>", "parent_id": "ml_lend_to_123", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_relations_126", "name": "Relations", "parent_id": "ml_lend_to_123", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_gf_127", "name": "Gf", "parent_id": "ml_lend_to_123", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_business_128", "name": "Business", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_swiggy_restaurent_129", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_zomato_resturent_130", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_equipments_131", "name": "Equipments", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_chicken_132", "name": "Chicken", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_kitchen_cooking_essentials_133", "name": "Kitchen Cooking Essentials", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_applications_134", "name": "Applications", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_registerations_135", "name": "Registerations", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_ghee_136", "name": "<PERSON><PERSON>", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_fish_137", "name": "Fish", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_packing_materials_138", "name": "Packing Materials", "parent_id": "ml_business_128", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_bank_charges_139", "name": "Bank Charges", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_indian_cleaning_corporation_140", "name": "Indian Cleaning Corporation", "parent_id": "ml_bank_charges_139", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_penalty_141", "name": "Penalty", "parent_id": "ml_bank_charges_139", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_cleaning_corporation_142", "name": "Cleaning Corporation", "parent_id": "ml_bank_charges_139", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_service_charges_143", "name": "Service Charges", "parent_id": "ml_bank_charges_139", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_amc_charges_144", "name": "Amc Charges", "parent_id": "ml_bank_charges_139", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_bike_maintence_145", "name": "Bike <PERSON>ce", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_r15_v3_146", "name": "R15 V3", "parent_id": "ml_bike_maintence_145", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_ola_s1_pro_147", "name": "Ola S1 Pro", "parent_id": "ml_bike_maintence_145", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_ola_s1_x_148", "name": "Ola S1 X", "parent_id": "ml_bike_maintence_145", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_stunner_149", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_bike_maintence_145", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_bike_oil_150", "name": "Bike Oil", "parent_id": "ml_bike_maintence_145", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_bike_tyre_151", "name": "Bike <PERSON>", "parent_id": "ml_bike_maintence_145", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_bike_spares_152", "name": "Bike Spares", "parent_id": "ml_bike_maintence_145", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_school_fees_153", "name": "School Fees", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_asmetha_154", "name": "<PERSON><PERSON><PERSON>", "parent_id": "ml_school_fees_153", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_ev_155", "name": "Ev", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_charging_station_156", "name": "Charging Station", "parent_id": "ml_ev_155", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_ola_purchases/subcriptions_157", "name": "<PERSON><PERSON> Purchases/Subcriptions", "parent_id": "ml_ev_155", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_transpotation_158", "name": "Transpotation", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_petrol_159", "name": "Petrol", "parent_id": "ml_transpotation_158", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_diesel_160", "name": "Diesel", "parent_id": "ml_transpotation_158", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_public_transit_161", "name": "Public Transit", "parent_id": "ml_transpotation_158", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_learning_162", "name": "Learning", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_veltech_institute_163", "name": "Veltech Institute", "parent_id": "ml_learning_162", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_materials_164", "name": "Materials", "parent_id": "ml_learning_162", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_entertainment_165", "name": "Entertainment", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_concerts_166", "name": "Concerts", "parent_id": "ml_entertainment_165", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_grooming_167", "name": "Grooming", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_haircut_168", "name": "Haircut", "parent_id": "ml_grooming_167", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_beard_169", "name": "<PERSON>", "parent_id": "ml_grooming_167", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_saved_money_170", "name": "Saved Money", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_celebration_171", "name": "Celebration", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_frnds_birthday_172", "name": "Frnds Birthday", "parent_id": "ml_celebration_171", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_online_storage_173", "name": "Online Storage", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_google_drive_174", "name": "Google Drive", "parent_id": "ml_online_storage_173", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_one_drive_175", "name": "One Drive", "parent_id": "ml_online_storage_173", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_college_176", "name": "College", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_xerox_177", "name": "Xerox", "parent_id": "ml_college_176", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_btech_fees_178", "name": "Btech Fees", "parent_id": "ml_college_176", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_school_179", "name": "School", "parent_id": null, "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_books_180", "name": "Books", "parent_id": "ml_school_179", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}, {"id": "ml_tution_fees_181", "name": "<PERSON><PERSON>", "parent_id": "ml_school_179", "description": "", "is_active": true, "is_system": false, "color": "#007ACC", "icon": "folder", "category_type": "expense", "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000"}]