"""
Background worker threads for UI operations to prevent blocking
"""

from PySide6.QtCore import QThread, Signal, QObject
from typing import Callable, Any, Optional
import logging
import time

from ..core.logger import get_logger


class CategoryRefreshWorker(QThread):
    """Background worker for category refresh operations"""
    
    # Signals
    refresh_started = Signal()
    refresh_completed = Signal()
    refresh_failed = Signal(str)
    progress_update = Signal(str)
    
    def __init__(self, category_manager, parent=None):
        super().__init__(parent)
        self.category_manager = category_manager
        self.logger = get_logger(__name__)
        self._should_stop = False
    
    def run(self):
        """Execute the category refresh in background"""
        try:
            self.refresh_started.emit()
            self.progress_update.emit("Refreshing categories...")
            
            start_time = time.time()
            
            # Force refresh from files
            if hasattr(self.category_manager, 'force_refresh_from_files'):
                self.category_manager.force_refresh_from_files()
            else:
                self.category_manager.refresh_categories()
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"Category refresh completed in {elapsed_time:.3f} seconds")
            
            self.progress_update.emit("Categories refreshed successfully")
            self.refresh_completed.emit()
            
        except Exception as e:
            self.logger.error(f"Category refresh failed: {str(e)}")
            self.refresh_failed.emit(str(e))
    
    def stop(self):
        """Request the worker to stop"""
        self._should_stop = True


class DataLoadWorker(QThread):
    """Background worker for loading transaction data"""
    
    # Signals
    load_started = Signal()
    load_completed = Signal(object)  # Emits the loaded data
    load_failed = Signal(str)
    progress_update = Signal(str, int)  # message, percentage
    
    def __init__(self, data_loader_func: Callable, *args, **kwargs):
        super().__init__()
        self.data_loader_func = data_loader_func
        self.args = args
        self.kwargs = kwargs
        self.logger = get_logger(__name__)
        self._should_stop = False
    
    def run(self):
        """Execute the data loading in background"""
        try:
            self.load_started.emit()
            self.progress_update.emit("Loading data...", 0)
            
            start_time = time.time()
            
            # Execute the data loading function
            result = self.data_loader_func(*self.args, **self.kwargs)
            
            if self._should_stop:
                return
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"Data loading completed in {elapsed_time:.3f} seconds")
            
            self.progress_update.emit("Data loaded successfully", 100)
            self.load_completed.emit(result)
            
        except Exception as e:
            self.logger.error(f"Data loading failed: {str(e)}")
            self.load_failed.emit(str(e))
    
    def stop(self):
        """Request the worker to stop"""
        self._should_stop = True


class FilterWorker(QThread):
    """Background worker for filtering operations"""

    # Signals
    filter_started = Signal()
    filter_completed = Signal(list)  # Emits filtered transactions
    filter_failed = Signal(str)
    progress_update = Signal(str, int)  # message, percentage

    def __init__(self, training_manager, filter_criteria, batch_size=None, parent=None):
        super().__init__(parent)
        self.training_manager = training_manager
        self.filter_criteria = filter_criteria
        self.batch_size = batch_size
        self.logger = get_logger(__name__)
        self._should_stop = False

    def run(self):
        """Execute filtering in background with progress reporting"""
        try:
            self.filter_started.emit()
            self.progress_update.emit("Starting filter operation...", 0)

            start_time = time.time()

            # Define progress callback for detailed updates
            def filter_progress_callback(current, total, message):
                if self._should_stop:
                    return
                # Calculate progress percentage
                progress = int((current / total) * 100) if total > 0 else 0
                self.progress_update.emit(message, progress)

            # Execute filtering with progress reporting
            filtered_transactions = self.training_manager.get_filtered_transactions(
                self.filter_criteria, self.batch_size, progress_callback=filter_progress_callback
            )

            if self._should_stop:
                return

            elapsed_time = time.time() - start_time
            self.logger.info(f"Filtering completed in {elapsed_time:.3f} seconds, found {len(filtered_transactions)} transactions")

            self.progress_update.emit(f"Found {len(filtered_transactions)} transactions", 100)
            self.filter_completed.emit(filtered_transactions)

        except Exception as e:
            self.logger.error(f"Filtering failed: {str(e)}")
            self.filter_failed.emit(str(e))

    def stop(self):
        """Request the worker to stop"""
        self._should_stop = True


class SimilarityWorker(QThread):
    """Background worker for similarity search operations"""

    # Signals
    similarity_started = Signal()
    similarity_completed = Signal(list)  # Emits similar transactions
    similarity_failed = Signal(str)
    progress_update = Signal(str, int)  # message, percentage

    def __init__(self, training_manager, reference_hash_id, similarity_threshold=0.3, limit=None, parent=None):
        super().__init__(parent)
        self.training_manager = training_manager
        self.reference_hash_id = reference_hash_id
        self.similarity_threshold = similarity_threshold
        self.limit = limit
        self.logger = get_logger(__name__)
        self._should_stop = False

    def run(self):
        """Execute similarity search in background"""
        try:
            self.similarity_started.emit()
            self.progress_update.emit("Finding similar transactions...", 0)

            start_time = time.time()

            # Execute similarity search
            similar_transactions = self.training_manager.find_similar_transactions(
                self.reference_hash_id, self.similarity_threshold, self.limit
            )

            if self._should_stop:
                return

            elapsed_time = time.time() - start_time
            self.logger.info(f"Similarity search completed in {elapsed_time:.3f} seconds, found {len(similar_transactions)} similar transactions")

            self.progress_update.emit(f"Found {len(similar_transactions)} similar transactions", 100)
            self.similarity_completed.emit(similar_transactions)

        except Exception as e:
            self.logger.error(f"Similarity search failed: {str(e)}")
            self.similarity_failed.emit(str(e))

    def stop(self):
        """Request the worker to stop"""
        self._should_stop = True


class BatchLabelWorker(QThread):
    """Background worker for batch labeling operations"""

    # Signals
    batch_started = Signal()
    batch_completed = Signal(int)  # Emits number of labeled transactions
    batch_failed = Signal(str)
    progress_update = Signal(str, int)  # message, percentage

    def __init__(self, training_manager, hash_ids, category, sub_category, confidence, parent=None):
        super().__init__(parent)
        self.training_manager = training_manager
        self.hash_ids = hash_ids
        self.category = category
        self.sub_category = sub_category
        self.confidence = confidence
        self.logger = get_logger(__name__)
        self._should_stop = False

    def run(self):
        """Execute batch labeling in background"""
        try:
            self.batch_started.emit()
            self.progress_update.emit("Starting batch labeling...", 0)

            start_time = time.time()
            total_count = len(self.hash_ids)

            # Check for stop signal before starting
            if self._should_stop:
                self.batch_completed.emit(0)
                return

            # Prepare labels for optimized batch operation
            labels = []
            for hash_id in self.hash_ids:
                labels.append({
                    "hash_id": hash_id,
                    "category": self.category,
                    "sub_category": self.sub_category,
                    "confidence": self.confidence
                })

            # Update progress to show preparation
            self.progress_update.emit("Preparing batch operation...", 10)

            # Use optimized batch labeling method
            result = self.training_manager.batch_label_transactions(labels)

            # Update progress to completion
            self.progress_update.emit(f"Completed: {result['success_count']} labeled, {result['error_count']} errors", 100)

            elapsed_time = time.time() - start_time
            self.logger.info(f"Batch labeling completed in {elapsed_time:.3f} seconds, labeled {result['success_count']}/{total_count} transactions")

            # Log any errors
            if result['error_count'] > 0:
                self.logger.warning(f"Batch labeling had {result['error_count']} errors: {result['errors'][:3]}")

            self.batch_completed.emit(result['success_count'])

        except Exception as e:
            self.logger.error(f"Batch labeling failed: {str(e)}")
            self.batch_failed.emit(str(e))

    def stop(self):
        """Request the worker to stop"""
        self._should_stop = True


class BatchOperationWorker(QThread):
    """Background worker for batch operations"""
    
    # Signals
    operation_started = Signal()
    operation_completed = Signal(object)
    operation_failed = Signal(str)
    progress_update = Signal(str, int)
    item_processed = Signal(int, object)  # index, item
    
    def __init__(self, operation_func: Callable, items: list, *args, **kwargs):
        super().__init__()
        self.operation_func = operation_func
        self.items = items
        self.args = args
        self.kwargs = kwargs
        self.logger = get_logger(__name__)
        self._should_stop = False
    
    def run(self):
        """Execute batch operation in background"""
        try:
            self.operation_started.emit()
            total_items = len(self.items)
            results = []
            
            for i, item in enumerate(self.items):
                if self._should_stop:
                    break
                
                # Update progress
                progress = int((i / total_items) * 100)
                self.progress_update.emit(f"Processing item {i+1} of {total_items}", progress)
                
                # Process item
                try:
                    result = self.operation_func(item, *self.args, **self.kwargs)
                    results.append(result)
                    self.item_processed.emit(i, result)
                except Exception as e:
                    self.logger.error(f"Error processing item {i}: {str(e)}")
                    results.append(None)
            
            if not self._should_stop:
                self.progress_update.emit("Batch operation completed", 100)
                self.operation_completed.emit(results)
            
        except Exception as e:
            self.logger.error(f"Batch operation failed: {str(e)}")
            self.operation_failed.emit(str(e))
    
    def stop(self):
        """Request the worker to stop"""
        self._should_stop = True


class PerformanceMonitor(QObject):
    """Monitor and log performance metrics"""
    
    performance_update = Signal(dict)  # Emits performance metrics
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self._start_times = {}
        self._metrics = {}
    
    def start_timer(self, operation_name: str):
        """Start timing an operation"""
        self._start_times[operation_name] = time.time()
    
    def end_timer(self, operation_name: str) -> float:
        """End timing an operation and return elapsed time"""
        if operation_name in self._start_times:
            elapsed = time.time() - self._start_times[operation_name]
            self._metrics[operation_name] = elapsed
            self.logger.debug(f"Operation '{operation_name}' took {elapsed:.3f} seconds")
            self.performance_update.emit({operation_name: elapsed})
            return elapsed
        return 0.0
    
    def get_metrics(self) -> dict:
        """Get all collected metrics"""
        return self._metrics.copy()
    
    def reset_metrics(self):
        """Reset all metrics"""
        self._start_times.clear()
        self._metrics.clear()
