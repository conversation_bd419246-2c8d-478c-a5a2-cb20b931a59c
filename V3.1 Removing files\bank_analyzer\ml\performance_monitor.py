"""
Performance Monitoring System

This module provides comprehensive monitoring and evaluation tools to track
hybrid system performance and identify areas for continuous improvement.
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter
import sqlite3

from .hybrid_prediction_engine import HybridPredictionEngine, HybridPredictionResult, PredictionSource
from .mismatch_analyzer import MLMismatchAnalyzer
from ..core.logger import get_logger


@dataclass
class PerformanceMetrics:
    """Performance metrics for a time period"""
    timestamp: datetime
    total_predictions: int
    accuracy_by_source: Dict[str, float]
    confidence_distribution: Dict[str, int]
    processing_time_stats: Dict[str, float]
    error_rate: float
    manual_label_hit_rate: float
    ml_coverage: float
    fallback_rate: float


@dataclass
class SystemHealthStatus:
    """Overall system health status"""
    status: str  # "healthy", "warning", "critical"
    manual_priority_system: str
    hierarchical_classifier: str
    standard_ml_system: str
    rule_based_system: str
    overall_accuracy: float
    issues: List[str]
    recommendations: List[str]


class PerformanceMonitor:
    """
    Comprehensive performance monitoring for the hybrid prediction system
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        
        # Initialize monitoring database
        self.monitoring_dir = self.data_dir / "performance_monitoring"
        self.monitoring_dir.mkdir(parents=True, exist_ok=True)
        
        self.db_path = self.monitoring_dir / "performance_metrics.db"
        self.reports_dir = self.monitoring_dir / "reports"
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.hybrid_engine = HybridPredictionEngine(str(self.data_dir))
        self.mismatch_analyzer = MLMismatchAnalyzer(str(self.data_dir))
        
        # Configuration
        self.config = {
            "monitoring_interval_hours": 1,
            "alert_thresholds": {
                "accuracy_drop": 0.05,  # 5% drop triggers alert
                "fallback_rate": 0.2,   # 20% fallback rate triggers alert
                "error_rate": 0.1,      # 10% error rate triggers alert
                "manual_hit_rate_min": 0.8  # Minimum 80% manual hit rate
            },
            "retention_days": 90,
            "report_frequency_hours": 24
        }
        
        # Initialize database
        self._init_database()
        
        # Performance tracking
        self.current_session_metrics = {
            "predictions": [],
            "errors": [],
            "processing_times": [],
            "session_start": datetime.now()
        }
    
    def _init_database(self):
        """Initialize SQLite database for performance metrics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Performance metrics table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME,
                        total_predictions INTEGER,
                        manual_hits INTEGER,
                        hierarchical_ml_used INTEGER,
                        standard_ml_used INTEGER,
                        rule_based_used INTEGER,
                        fallback_used INTEGER,
                        error_count INTEGER,
                        avg_processing_time REAL,
                        accuracy_rate REAL,
                        confidence_avg REAL,
                        session_data TEXT
                    )
                ''')
                
                # System health table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_health (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME,
                        status TEXT,
                        manual_system_status TEXT,
                        hierarchical_status TEXT,
                        standard_ml_status TEXT,
                        rule_based_status TEXT,
                        overall_accuracy REAL,
                        issues TEXT,
                        recommendations TEXT
                    )
                ''')
                
                # Prediction log table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS prediction_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME,
                        description TEXT,
                        predicted_category TEXT,
                        predicted_subcategory TEXT,
                        confidence REAL,
                        source TEXT,
                        processing_time REAL,
                        success BOOLEAN
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error initializing monitoring database: {str(e)}")
    
    def log_prediction(self, description: str, result: HybridPredictionResult, 
                      processing_time: float, success: bool = True):
        """Log a prediction for monitoring"""
        try:
            # Add to current session
            self.current_session_metrics["predictions"].append({
                "timestamp": datetime.now(),
                "description": description[:100],  # Truncate for storage
                "result": result,
                "processing_time": processing_time,
                "success": success
            })
            
            # Log to database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO prediction_log 
                    (timestamp, description, predicted_category, predicted_subcategory, 
                     confidence, source, processing_time, success)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    datetime.now(),
                    description[:100],
                    result.category,
                    result.sub_category,
                    result.confidence,
                    result.source.value,
                    processing_time,
                    success
                ))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error logging prediction: {str(e)}")
    
    def log_error(self, error_type: str, error_message: str, context: Dict[str, Any] = None):
        """Log an error for monitoring"""
        try:
            error_entry = {
                "timestamp": datetime.now(),
                "error_type": error_type,
                "error_message": error_message,
                "context": context or {}
            }
            
            self.current_session_metrics["errors"].append(error_entry)
            
        except Exception as e:
            self.logger.error(f"Error logging error: {str(e)}")
    
    def calculate_current_metrics(self) -> PerformanceMetrics:
        """Calculate current performance metrics"""
        try:
            predictions = self.current_session_metrics["predictions"]
            errors = self.current_session_metrics["errors"]
            
            if not predictions:
                return PerformanceMetrics(
                    timestamp=datetime.now(),
                    total_predictions=0,
                    accuracy_by_source={},
                    confidence_distribution={},
                    processing_time_stats={},
                    error_rate=0.0,
                    manual_label_hit_rate=0.0,
                    ml_coverage=0.0,
                    fallback_rate=0.0
                )
            
            total_predictions = len(predictions)
            successful_predictions = [p for p in predictions if p["success"]]
            
            # Accuracy by source
            source_accuracy = defaultdict(lambda: {"correct": 0, "total": 0})
            for pred in successful_predictions:
                source = pred["result"].source.value
                source_accuracy[source]["total"] += 1
                # For now, assume all successful predictions are correct
                # In practice, you'd compare with ground truth
                source_accuracy[source]["correct"] += 1
            
            accuracy_by_source = {
                source: stats["correct"] / max(1, stats["total"])
                for source, stats in source_accuracy.items()
            }
            
            # Confidence distribution
            confidence_ranges = [(0.0, 0.2), (0.2, 0.4), (0.4, 0.6), (0.6, 0.8), (0.8, 1.0)]
            confidence_distribution = {f"{r[0]}-{r[1]}": 0 for r in confidence_ranges}
            
            for pred in successful_predictions:
                confidence = pred["result"].confidence
                for r in confidence_ranges:
                    if r[0] <= confidence < r[1]:
                        confidence_distribution[f"{r[0]}-{r[1]}"] += 1
                        break
            
            # Processing time stats
            processing_times = [p["processing_time"] for p in predictions]
            processing_time_stats = {
                "mean": np.mean(processing_times) if processing_times else 0.0,
                "median": np.median(processing_times) if processing_times else 0.0,
                "p95": np.percentile(processing_times, 95) if processing_times else 0.0,
                "max": np.max(processing_times) if processing_times else 0.0
            }
            
            # Calculate rates
            error_rate = len(errors) / max(1, total_predictions)
            
            manual_hits = sum(1 for p in successful_predictions 
                            if p["result"].source in [PredictionSource.MANUAL_EXACT, 
                                                    PredictionSource.MANUAL_NORMALIZED, 
                                                    PredictionSource.MANUAL_FUZZY])
            manual_label_hit_rate = manual_hits / max(1, len(successful_predictions))
            
            ml_hits = sum(1 for p in successful_predictions 
                         if p["result"].source in [PredictionSource.HIERARCHICAL_ML, 
                                                 PredictionSource.STANDARD_ML])
            ml_coverage = ml_hits / max(1, len(successful_predictions))
            
            fallback_hits = sum(1 for p in successful_predictions 
                              if p["result"].source == PredictionSource.FALLBACK)
            fallback_rate = fallback_hits / max(1, len(successful_predictions))
            
            return PerformanceMetrics(
                timestamp=datetime.now(),
                total_predictions=total_predictions,
                accuracy_by_source=accuracy_by_source,
                confidence_distribution=confidence_distribution,
                processing_time_stats=processing_time_stats,
                error_rate=error_rate,
                manual_label_hit_rate=manual_label_hit_rate,
                ml_coverage=ml_coverage,
                fallback_rate=fallback_rate
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating current metrics: {str(e)}")
            return PerformanceMetrics(
                timestamp=datetime.now(),
                total_predictions=0,
                accuracy_by_source={},
                confidence_distribution={},
                processing_time_stats={},
                error_rate=1.0,
                manual_label_hit_rate=0.0,
                ml_coverage=0.0,
                fallback_rate=1.0
            )
    
    def save_metrics_to_database(self, metrics: PerformanceMetrics):
        """Save metrics to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Calculate aggregated values
                manual_hits = int(metrics.manual_label_hit_rate * metrics.total_predictions)
                hierarchical_ml = int(metrics.ml_coverage * metrics.total_predictions * 0.5)  # Estimate
                standard_ml = int(metrics.ml_coverage * metrics.total_predictions * 0.5)  # Estimate
                rule_based = max(0, metrics.total_predictions - manual_hits - hierarchical_ml - standard_ml - int(metrics.fallback_rate * metrics.total_predictions))
                fallback = int(metrics.fallback_rate * metrics.total_predictions)
                error_count = int(metrics.error_rate * metrics.total_predictions)
                
                cursor.execute('''
                    INSERT INTO performance_metrics 
                    (timestamp, total_predictions, manual_hits, hierarchical_ml_used, 
                     standard_ml_used, rule_based_used, fallback_used, error_count,
                     avg_processing_time, accuracy_rate, confidence_avg, session_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    metrics.timestamp,
                    metrics.total_predictions,
                    manual_hits,
                    hierarchical_ml,
                    standard_ml,
                    rule_based,
                    fallback,
                    error_count,
                    metrics.processing_time_stats.get("mean", 0.0),
                    sum(metrics.accuracy_by_source.values()) / max(1, len(metrics.accuracy_by_source)),
                    0.5,  # Placeholder for average confidence
                    json.dumps(asdict(metrics), default=str)
                ))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Error saving metrics to database: {str(e)}")
    
    def assess_system_health(self) -> SystemHealthStatus:
        """Assess overall system health"""
        try:
            # Get current metrics
            current_metrics = self.calculate_current_metrics()
            
            # Get hybrid engine stats
            engine_stats = self.hybrid_engine.get_prediction_stats()
            
            issues = []
            recommendations = []
            
            # Check manual priority system
            manual_cache_stats = engine_stats["manual_label_cache_stats"]
            if manual_cache_stats["exact_cache_size"] == 0:
                manual_status = "critical"
                issues.append("No manual labels in priority cache")
                recommendations.append("Load manual labels into priority system")
            elif manual_cache_stats["hit_rates"]["overall_hit_rate"] < self.config["alert_thresholds"]["manual_hit_rate_min"]:
                manual_status = "warning"
                issues.append("Low manual label hit rate")
                recommendations.append("Refresh manual label cache or add more manual labels")
            else:
                manual_status = "healthy"
            
            # Check hierarchical classifier
            hierarchical_stats = engine_stats["hierarchical_model_stats"]
            if not hierarchical_stats["is_trained"]:
                hierarchical_status = "warning"
                issues.append("Hierarchical classifier not trained")
                recommendations.append("Train hierarchical classifier with sufficient data")
            else:
                hierarchical_status = "healthy"
            
            # Check standard ML system
            if not self.hybrid_engine.ml_categorizer.is_trained:
                standard_ml_status = "warning"
                issues.append("Standard ML models not trained")
                recommendations.append("Train standard ML models")
            else:
                standard_ml_status = "healthy"
            
            # Rule-based system (always available)
            rule_based_status = "healthy"
            
            # Check performance thresholds
            if current_metrics.error_rate > self.config["alert_thresholds"]["error_rate"]:
                issues.append(f"High error rate: {current_metrics.error_rate:.2%}")
                recommendations.append("Investigate and fix prediction errors")
            
            if current_metrics.fallback_rate > self.config["alert_thresholds"]["fallback_rate"]:
                issues.append(f"High fallback rate: {current_metrics.fallback_rate:.2%}")
                recommendations.append("Improve ML model coverage and accuracy")
            
            # Overall status
            if any(status == "critical" for status in [manual_status, hierarchical_status, standard_ml_status]):
                overall_status = "critical"
            elif any(status == "warning" for status in [manual_status, hierarchical_status, standard_ml_status]) or issues:
                overall_status = "warning"
            else:
                overall_status = "healthy"
            
            # Calculate overall accuracy
            overall_accuracy = sum(current_metrics.accuracy_by_source.values()) / max(1, len(current_metrics.accuracy_by_source))
            
            return SystemHealthStatus(
                status=overall_status,
                manual_priority_system=manual_status,
                hierarchical_classifier=hierarchical_status,
                standard_ml_system=standard_ml_status,
                rule_based_system=rule_based_status,
                overall_accuracy=overall_accuracy,
                issues=issues,
                recommendations=recommendations
            )
            
        except Exception as e:
            self.logger.error(f"Error assessing system health: {str(e)}")
            return SystemHealthStatus(
                status="critical",
                manual_priority_system="unknown",
                hierarchical_classifier="unknown",
                standard_ml_system="unknown",
                rule_based_system="unknown",
                overall_accuracy=0.0,
                issues=[f"Health assessment failed: {str(e)}"],
                recommendations=["Check system logs and restart monitoring"]
            )

    def generate_performance_report(self, days_back: int = 7) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days_back)

            # Get metrics from database
            with sqlite3.connect(self.db_path) as conn:
                # Performance metrics over time
                metrics_df = pd.read_sql_query('''
                    SELECT * FROM performance_metrics
                    WHERE timestamp >= ? AND timestamp <= ?
                    ORDER BY timestamp
                ''', conn, params=(start_time, end_time))

                # System health history
                health_df = pd.read_sql_query('''
                    SELECT * FROM system_health
                    WHERE timestamp >= ? AND timestamp <= ?
                    ORDER BY timestamp
                ''', conn, params=(start_time, end_time))

                # Recent predictions
                predictions_df = pd.read_sql_query('''
                    SELECT * FROM prediction_log
                    WHERE timestamp >= ? AND timestamp <= ?
                    ORDER BY timestamp DESC
                    LIMIT 1000
                ''', conn, params=(start_time, end_time))

            # Calculate summary statistics
            if not metrics_df.empty:
                total_predictions = metrics_df['total_predictions'].sum()
                avg_accuracy = metrics_df['accuracy_rate'].mean()
                avg_processing_time = metrics_df['avg_processing_time'].mean()

                # Source distribution
                source_distribution = {
                    'manual_hits': metrics_df['manual_hits'].sum(),
                    'hierarchical_ml': metrics_df['hierarchical_ml_used'].sum(),
                    'standard_ml': metrics_df['standard_ml_used'].sum(),
                    'rule_based': metrics_df['rule_based_used'].sum(),
                    'fallback': metrics_df['fallback_used'].sum()
                }
            else:
                total_predictions = 0
                avg_accuracy = 0.0
                avg_processing_time = 0.0
                source_distribution = {}

            # Current system health
            current_health = self.assess_system_health()

            # Generate recommendations
            recommendations = self._generate_performance_recommendations(
                metrics_df, health_df, predictions_df, current_health
            )

            report = {
                "report_period": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "days_covered": days_back
                },
                "summary_statistics": {
                    "total_predictions": int(total_predictions),
                    "average_accuracy": float(avg_accuracy),
                    "average_processing_time_ms": float(avg_processing_time * 1000),
                    "source_distribution": source_distribution
                },
                "current_system_health": asdict(current_health),
                "performance_trends": self._analyze_performance_trends(metrics_df),
                "error_analysis": self._analyze_errors(predictions_df),
                "recommendations": recommendations,
                "generated_at": datetime.now().isoformat()
            }

            # Save report
            self._save_performance_report(report)

            return report

        except Exception as e:
            self.logger.error(f"Error generating performance report: {str(e)}")
            return {"error": str(e), "generated_at": datetime.now().isoformat()}

    def _analyze_performance_trends(self, metrics_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance trends over time"""
        try:
            if metrics_df.empty:
                return {"trend": "no_data"}

            # Calculate trends
            trends = {}

            # Accuracy trend
            if len(metrics_df) >= 2:
                accuracy_trend = np.polyfit(range(len(metrics_df)), metrics_df['accuracy_rate'], 1)[0]
                trends['accuracy_trend'] = "improving" if accuracy_trend > 0.01 else "declining" if accuracy_trend < -0.01 else "stable"
            else:
                trends['accuracy_trend'] = "insufficient_data"

            # Processing time trend
            if len(metrics_df) >= 2:
                time_trend = np.polyfit(range(len(metrics_df)), metrics_df['avg_processing_time'], 1)[0]
                trends['processing_time_trend'] = "improving" if time_trend < -0.001 else "declining" if time_trend > 0.001 else "stable"
            else:
                trends['processing_time_trend'] = "insufficient_data"

            # Manual hit rate trend
            if len(metrics_df) >= 2:
                manual_rate = metrics_df['manual_hits'] / metrics_df['total_predictions']
                manual_trend = np.polyfit(range(len(metrics_df)), manual_rate, 1)[0]
                trends['manual_hit_rate_trend'] = "improving" if manual_trend > 0.01 else "declining" if manual_trend < -0.01 else "stable"
            else:
                trends['manual_hit_rate_trend'] = "insufficient_data"

            return trends

        except Exception as e:
            self.logger.debug(f"Error analyzing performance trends: {str(e)}")
            return {"trend": "analysis_error"}

    def _analyze_errors(self, predictions_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze error patterns"""
        try:
            if predictions_df.empty:
                return {"error_rate": 0.0, "patterns": []}

            # Calculate error rate
            total_predictions = len(predictions_df)
            failed_predictions = len(predictions_df[predictions_df['success'] == False])
            error_rate = failed_predictions / max(1, total_predictions)

            # Analyze error patterns by source
            error_by_source = predictions_df[predictions_df['success'] == False]['source'].value_counts().to_dict()

            # Analyze low confidence predictions
            low_confidence = predictions_df[predictions_df['confidence'] < 0.5]
            low_confidence_rate = len(low_confidence) / max(1, total_predictions)

            return {
                "error_rate": error_rate,
                "errors_by_source": error_by_source,
                "low_confidence_rate": low_confidence_rate,
                "total_failed": failed_predictions,
                "patterns": [
                    f"Most errors from: {max(error_by_source.items(), key=lambda x: x[1])[0]}" if error_by_source else "No error patterns",
                    f"Low confidence rate: {low_confidence_rate:.2%}"
                ]
            }

        except Exception as e:
            self.logger.debug(f"Error analyzing errors: {str(e)}")
            return {"error_rate": 1.0, "patterns": ["Error analysis failed"]}

    def _generate_performance_recommendations(self, metrics_df: pd.DataFrame,
                                            health_df: pd.DataFrame,
                                            predictions_df: pd.DataFrame,
                                            current_health: SystemHealthStatus) -> List[str]:
        """Generate performance improvement recommendations"""
        try:
            recommendations = []

            # Add health-based recommendations
            recommendations.extend(current_health.recommendations)

            # Analyze trends for additional recommendations
            if not metrics_df.empty and len(metrics_df) >= 2:
                # Check accuracy trend
                recent_accuracy = metrics_df['accuracy_rate'].tail(3).mean()
                if recent_accuracy < 0.8:
                    recommendations.append("Overall accuracy is below 80%. Consider retraining models.")

                # Check processing time
                recent_processing_time = metrics_df['avg_processing_time'].tail(3).mean()
                if recent_processing_time > 0.1:  # 100ms
                    recommendations.append("Processing time is high. Consider optimizing prediction pipeline.")

                # Check fallback usage
                recent_fallback_rate = (metrics_df['fallback_used'].tail(3).sum() /
                                      metrics_df['total_predictions'].tail(3).sum())
                if recent_fallback_rate > 0.2:
                    recommendations.append("High fallback usage. Improve ML model coverage.")

            # Remove duplicates
            recommendations = list(set(recommendations))

            return recommendations

        except Exception as e:
            self.logger.debug(f"Error generating recommendations: {str(e)}")
            return ["Error generating recommendations"]

    def _save_performance_report(self, report: Dict[str, Any]):
        """Save performance report to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = self.reports_dir / f"performance_report_{timestamp}.json"

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Performance report saved to {report_file}")

        except Exception as e:
            self.logger.error(f"Error saving performance report: {str(e)}")

    def cleanup_old_data(self):
        """Clean up old monitoring data"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.config["retention_days"])

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Clean up old metrics
                cursor.execute('DELETE FROM performance_metrics WHERE timestamp < ?', (cutoff_date,))
                cursor.execute('DELETE FROM system_health WHERE timestamp < ?', (cutoff_date,))
                cursor.execute('DELETE FROM prediction_log WHERE timestamp < ?', (cutoff_date,))

                conn.commit()

            self.logger.info(f"Cleaned up monitoring data older than {self.config['retention_days']} days")

        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {str(e)}")

    def reset_session_metrics(self):
        """Reset current session metrics"""
        self.current_session_metrics = {
            "predictions": [],
            "errors": [],
            "processing_times": [],
            "session_start": datetime.now()
        }

    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get summary of monitoring system status"""
        try:
            current_metrics = self.calculate_current_metrics()
            current_health = self.assess_system_health()

            return {
                "monitoring_active": True,
                "session_start": self.current_session_metrics["session_start"].isoformat(),
                "current_metrics": asdict(current_metrics),
                "system_health": asdict(current_health),
                "database_path": str(self.db_path),
                "reports_directory": str(self.reports_dir)
            }

        except Exception as e:
            self.logger.error(f"Error getting monitoring summary: {str(e)}")
            return {"monitoring_active": False, "error": str(e)}
