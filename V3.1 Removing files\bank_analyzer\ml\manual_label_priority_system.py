"""
Manual Label Priority System

This module implements an exact match lookup system that prioritizes manual labels
over ML predictions, ensuring 100% accuracy on previously labeled transactions.
"""

import json
import hashlib
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import pandas as pd
from collections import defaultdict

from .data_preparation import TransactionDataPreparator
from ..core.logger import get_logger


@dataclass
class ManualLabelEntry:
    """Represents a manual label entry in the priority cache"""
    hash_id: str
    description: str
    normalized_description: str
    category: str
    sub_category: str
    confidence: float
    labeled_by: str
    labeled_at: datetime
    frequency: int
    amount_range: Tuple[float, float]
    transaction_types: List[str]
    last_accessed: datetime
    access_count: int = 0


@dataclass
class LookupResult:
    """Result of manual label lookup"""
    found: bool
    entry: Optional[ManualLabelEntry] = None
    match_type: str = "none"  # "exact", "normalized", "fuzzy"
    confidence: float = 0.0


class ManualLabelPrioritySystem:
    """
    Priority system that ensures manual labels always take precedence over ML predictions
    Uses multiple lookup strategies for maximum coverage
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        
        # Initialize data preparator for hash generation
        self.data_preparator = TransactionDataPreparator()
        
        # Cache files
        self.cache_dir = self.data_dir / "manual_label_cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.exact_cache_file = self.cache_dir / "exact_matches.json"
        self.normalized_cache_file = self.cache_dir / "normalized_matches.json"
        self.fuzzy_cache_file = self.cache_dir / "fuzzy_matches.json"
        self.stats_file = self.cache_dir / "cache_stats.json"
        
        # In-memory caches for fast lookup
        self.exact_cache: Dict[str, ManualLabelEntry] = {}
        self.normalized_cache: Dict[str, ManualLabelEntry] = {}
        self.fuzzy_cache: Dict[str, List[ManualLabelEntry]] = {}
        
        # Configuration
        self.config = {
            "fuzzy_similarity_threshold": 0.9,
            "max_fuzzy_candidates": 10,
            "cache_refresh_interval": 3600,  # 1 hour
            "enable_fuzzy_matching": True,
            "enable_merchant_matching": True
        }
        
        # Statistics
        self.stats = {
            "exact_hits": 0,
            "normalized_hits": 0,
            "fuzzy_hits": 0,
            "total_lookups": 0,
            "cache_misses": 0,
            "last_refresh": None
        }
        
        # Load existing caches
        self._load_caches()
    
    def lookup_manual_label(self, description: str, amount: float = None, 
                          transaction_type: str = None) -> LookupResult:
        """
        Look up manual label for a transaction description
        
        Args:
            description: Transaction description
            amount: Transaction amount (optional, for better matching)
            transaction_type: Transaction type (optional)
            
        Returns:
            LookupResult with manual label if found
        """
        self.stats["total_lookups"] += 1
        
        try:
            # Strategy 1: Exact hash match
            exact_result = self._lookup_exact_match(description, transaction_type)
            if exact_result.found:
                self.stats["exact_hits"] += 1
                self._update_access_stats(exact_result.entry)
                return exact_result
            
            # Strategy 2: Normalized description match
            normalized_result = self._lookup_normalized_match(description)
            if normalized_result.found:
                self.stats["normalized_hits"] += 1
                self._update_access_stats(normalized_result.entry)
                return normalized_result
            
            # Strategy 3: Fuzzy matching (if enabled)
            if self.config["enable_fuzzy_matching"]:
                fuzzy_result = self._lookup_fuzzy_match(description, amount)
                if fuzzy_result.found:
                    self.stats["fuzzy_hits"] += 1
                    self._update_access_stats(fuzzy_result.entry)
                    return fuzzy_result
            
            # No match found
            self.stats["cache_misses"] += 1
            return LookupResult(found=False)
            
        except Exception as e:
            self.logger.error(f"Error in manual label lookup: {str(e)}")
            return LookupResult(found=False)
    
    def add_manual_label(self, description: str, category: str, sub_category: str,
                        confidence: float = 1.0, labeled_by: str = "user",
                        amount: float = None, transaction_type: str = None) -> bool:
        """
        Add a new manual label to the priority system
        
        Args:
            description: Transaction description
            category: Manual category label
            sub_category: Manual subcategory label
            confidence: Confidence in the label (default 1.0 for manual)
            labeled_by: Who labeled this transaction
            amount: Transaction amount (optional)
            transaction_type: Transaction type (optional)
            
        Returns:
            True if added successfully
        """
        try:
            # Normalize description
            normalized = self.data_preparator.normalize_description(description)
            
            # Generate hash IDs
            exact_hash = self._generate_exact_hash(description)
            normalized_hash = self._generate_normalized_hash(normalized)
            
            # Create manual label entry
            entry = ManualLabelEntry(
                hash_id=exact_hash,
                description=description,
                normalized_description=normalized,
                category=category,
                sub_category=sub_category,
                confidence=confidence,
                labeled_by=labeled_by,
                labeled_at=datetime.now(),
                frequency=1,
                amount_range=(amount or 0.0, amount or 0.0),
                transaction_types=[transaction_type] if transaction_type else [],
                last_accessed=datetime.now(),
                access_count=0
            )
            
            # Add to caches
            self.exact_cache[exact_hash] = entry
            self.normalized_cache[normalized_hash] = entry
            
            # Add to fuzzy cache if enabled
            if self.config["enable_fuzzy_matching"]:
                self._add_to_fuzzy_cache(entry)
            
            # Save caches
            self._save_caches()
            
            self.logger.info(f"Added manual label: {description[:50]}... -> {category}/{sub_category}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding manual label: {str(e)}")
            return False
    
    def bulk_load_manual_labels(self, force_refresh: bool = False) -> int:
        """
        Bulk load all manual labels from the training data
        
        Args:
            force_refresh: Force reload even if cache is recent
            
        Returns:
            Number of labels loaded
        """
        try:
            # Check if refresh is needed
            if not force_refresh and self._is_cache_fresh():
                self.logger.info("Manual label cache is fresh, skipping bulk load")
                return len(self.exact_cache)
            
            self.logger.info("Bulk loading manual labels from training data...")
            
            # Load unique transactions with manual labels
            unique_transactions = self.data_preparator.load_unique_transactions()
            
            # Filter for manually labeled transactions
            manual_labels = {
                hash_id: txn for hash_id, txn in unique_transactions.items()
                if txn.is_manually_labeled and txn.category and txn.sub_category
            }
            
            # Clear existing caches
            self.exact_cache.clear()
            self.normalized_cache.clear()
            self.fuzzy_cache.clear()
            
            # Load into caches
            loaded_count = 0
            for hash_id, txn in manual_labels.items():
                try:
                    # Create manual label entry
                    entry = ManualLabelEntry(
                        hash_id=hash_id,
                        description=txn.description,
                        normalized_description=txn.normalized_description,
                        category=txn.category,
                        sub_category=txn.sub_category,
                        confidence=txn.confidence,
                        labeled_by=txn.labeled_by,
                        labeled_at=txn.labeled_at or datetime.now(),
                        frequency=txn.frequency,
                        amount_range=txn.amount_range,
                        transaction_types=list(txn.transaction_types),
                        last_accessed=datetime.now(),
                        access_count=0
                    )
                    
                    # Add to exact cache
                    exact_hash = self._generate_exact_hash(txn.description)
                    self.exact_cache[exact_hash] = entry
                    
                    # Add to normalized cache
                    normalized_hash = self._generate_normalized_hash(txn.normalized_description)
                    self.normalized_cache[normalized_hash] = entry
                    
                    # Add to fuzzy cache
                    if self.config["enable_fuzzy_matching"]:
                        self._add_to_fuzzy_cache(entry)
                    
                    loaded_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"Error loading manual label for {hash_id}: {str(e)}")
                    continue
            
            # Save caches
            self._save_caches()
            
            # Update stats
            self.stats["last_refresh"] = datetime.now().isoformat()
            self._save_stats()
            
            self.logger.info(f"Bulk loaded {loaded_count} manual labels into priority cache")
            return loaded_count
            
        except Exception as e:
            self.logger.error(f"Error in bulk load: {str(e)}")
            return 0

    def _lookup_exact_match(self, description: str, transaction_type: str = None) -> LookupResult:
        """Look up exact hash match"""
        try:
            # Try context-aware hash first (if transaction type provided)
            if transaction_type:
                context_hash = self._generate_context_hash(description, transaction_type)
                if context_hash in self.exact_cache:
                    return LookupResult(
                        found=True,
                        entry=self.exact_cache[context_hash],
                        match_type="exact",
                        confidence=1.0
                    )

            # Try exact description hash
            exact_hash = self._generate_exact_hash(description)
            if exact_hash in self.exact_cache:
                return LookupResult(
                    found=True,
                    entry=self.exact_cache[exact_hash],
                    match_type="exact",
                    confidence=1.0
                )

            return LookupResult(found=False)

        except Exception as e:
            self.logger.debug(f"Error in exact match lookup: {str(e)}")
            return LookupResult(found=False)

    def _lookup_normalized_match(self, description: str) -> LookupResult:
        """Look up normalized description match"""
        try:
            normalized = self.data_preparator.normalize_description(description)
            normalized_hash = self._generate_normalized_hash(normalized)

            if normalized_hash in self.normalized_cache:
                return LookupResult(
                    found=True,
                    entry=self.normalized_cache[normalized_hash],
                    match_type="normalized",
                    confidence=0.95
                )

            return LookupResult(found=False)

        except Exception as e:
            self.logger.debug(f"Error in normalized match lookup: {str(e)}")
            return LookupResult(found=False)

    def _lookup_fuzzy_match(self, description: str, amount: float = None) -> LookupResult:
        """Look up fuzzy match using similarity"""
        try:
            if not self.config["enable_fuzzy_matching"]:
                return LookupResult(found=False)

            best_match = None
            best_similarity = 0.0

            # Check fuzzy cache
            normalized = self.data_preparator.normalize_description(description)

            for fuzzy_key, candidates in self.fuzzy_cache.items():
                for candidate in candidates:
                    similarity = self._calculate_similarity(normalized, candidate.normalized_description)

                    # Consider amount similarity if provided
                    if amount and candidate.amount_range[0] > 0:
                        amount_similarity = self._calculate_amount_similarity(amount, candidate.amount_range)
                        combined_similarity = 0.8 * similarity + 0.2 * amount_similarity
                    else:
                        combined_similarity = similarity

                    if (combined_similarity > best_similarity and
                        combined_similarity >= self.config["fuzzy_similarity_threshold"]):
                        best_similarity = combined_similarity
                        best_match = candidate

            if best_match:
                return LookupResult(
                    found=True,
                    entry=best_match,
                    match_type="fuzzy",
                    confidence=best_similarity
                )

            return LookupResult(found=False)

        except Exception as e:
            self.logger.debug(f"Error in fuzzy match lookup: {str(e)}")
            return LookupResult(found=False)

    def _generate_exact_hash(self, description: str) -> str:
        """Generate exact hash for description"""
        return hashlib.md5(description.encode('utf-8')).hexdigest()[:12]

    def _generate_normalized_hash(self, normalized_description: str) -> str:
        """Generate hash for normalized description"""
        return hashlib.md5(normalized_description.encode('utf-8')).hexdigest()[:12]

    def _generate_context_hash(self, description: str, transaction_type: str) -> str:
        """Generate context-aware hash"""
        context_string = f"{description}|{transaction_type}"
        return hashlib.md5(context_string.encode('utf-8')).hexdigest()[:12]

    def _add_to_fuzzy_cache(self, entry: ManualLabelEntry):
        """Add entry to fuzzy cache for similarity matching"""
        try:
            # Use first few words as fuzzy key
            words = entry.normalized_description.split()[:3]
            fuzzy_key = ' '.join(words) if words else entry.normalized_description[:20]

            if fuzzy_key not in self.fuzzy_cache:
                self.fuzzy_cache[fuzzy_key] = []

            # Avoid duplicates
            existing_hashes = {e.hash_id for e in self.fuzzy_cache[fuzzy_key]}
            if entry.hash_id not in existing_hashes:
                self.fuzzy_cache[fuzzy_key].append(entry)

                # Limit cache size
                if len(self.fuzzy_cache[fuzzy_key]) > self.config["max_fuzzy_candidates"]:
                    self.fuzzy_cache[fuzzy_key] = self.fuzzy_cache[fuzzy_key][-self.config["max_fuzzy_candidates"]:]

        except Exception as e:
            self.logger.debug(f"Error adding to fuzzy cache: {str(e)}")

    def _calculate_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity between two descriptions"""
        try:
            # Simple Jaccard similarity for now
            words1 = set(desc1.lower().split())
            words2 = set(desc2.lower().split())

            if not words1 and not words2:
                return 1.0
            if not words1 or not words2:
                return 0.0

            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))

            return intersection / union if union > 0 else 0.0

        except Exception as e:
            self.logger.debug(f"Error calculating similarity: {str(e)}")
            return 0.0

    def _calculate_amount_similarity(self, amount: float, amount_range: Tuple[float, float]) -> float:
        """Calculate amount similarity"""
        try:
            min_amt, max_amt = amount_range
            if min_amt == max_amt == 0:
                return 0.5  # Neutral if no amount info

            if min_amt <= amount <= max_amt:
                return 1.0  # Exact range match

            # Calculate distance-based similarity
            range_center = (min_amt + max_amt) / 2
            range_width = max_amt - min_amt if max_amt > min_amt else abs(range_center * 0.1)

            distance = abs(amount - range_center)
            similarity = max(0.0, 1.0 - (distance / (range_width * 2)))

            return similarity

        except Exception as e:
            self.logger.debug(f"Error calculating amount similarity: {str(e)}")
            return 0.0

    def _update_access_stats(self, entry: ManualLabelEntry):
        """Update access statistics for cache entry"""
        try:
            entry.last_accessed = datetime.now()
            entry.access_count += 1
        except Exception as e:
            self.logger.debug(f"Error updating access stats: {str(e)}")

    def _is_cache_fresh(self) -> bool:
        """Check if cache is fresh enough to skip refresh"""
        try:
            if not self.stats.get("last_refresh"):
                return False

            last_refresh = datetime.fromisoformat(self.stats["last_refresh"])
            age_seconds = (datetime.now() - last_refresh).total_seconds()

            return age_seconds < self.config["cache_refresh_interval"]

        except Exception as e:
            self.logger.debug(f"Error checking cache freshness: {str(e)}")
            return False

    def _load_caches(self):
        """Load caches from disk"""
        try:
            # Load exact cache
            if self.exact_cache_file.exists():
                with open(self.exact_cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.exact_cache = {
                        k: ManualLabelEntry(**v) for k, v in data.items()
                    }

            # Load normalized cache
            if self.normalized_cache_file.exists():
                with open(self.normalized_cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.normalized_cache = {
                        k: ManualLabelEntry(**v) for k, v in data.items()
                    }

            # Load fuzzy cache
            if self.fuzzy_cache_file.exists():
                with open(self.fuzzy_cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.fuzzy_cache = {
                        k: [ManualLabelEntry(**entry) for entry in entries]
                        for k, entries in data.items()
                    }

            # Load stats
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    saved_stats = json.load(f)
                    self.stats.update(saved_stats)

            self.logger.info(f"Loaded manual label caches: {len(self.exact_cache)} exact, "
                           f"{len(self.normalized_cache)} normalized, {len(self.fuzzy_cache)} fuzzy")

        except Exception as e:
            self.logger.warning(f"Error loading caches: {str(e)}")
            # Initialize empty caches on error
            self.exact_cache = {}
            self.normalized_cache = {}
            self.fuzzy_cache = {}

    def _save_caches(self):
        """Save caches to disk"""
        try:
            # Convert datetime objects to ISO strings for JSON serialization
            def serialize_entry(entry: ManualLabelEntry) -> dict:
                data = asdict(entry)
                data['labeled_at'] = entry.labeled_at.isoformat() if entry.labeled_at else None
                data['last_accessed'] = entry.last_accessed.isoformat() if entry.last_accessed else None
                return data

            # Save exact cache
            exact_data = {k: serialize_entry(v) for k, v in self.exact_cache.items()}
            with open(self.exact_cache_file, 'w', encoding='utf-8') as f:
                json.dump(exact_data, f, indent=2, ensure_ascii=False)

            # Save normalized cache
            normalized_data = {k: serialize_entry(v) for k, v in self.normalized_cache.items()}
            with open(self.normalized_cache_file, 'w', encoding='utf-8') as f:
                json.dump(normalized_data, f, indent=2, ensure_ascii=False)

            # Save fuzzy cache
            fuzzy_data = {
                k: [serialize_entry(entry) for entry in entries]
                for k, entries in self.fuzzy_cache.items()
            }
            with open(self.fuzzy_cache_file, 'w', encoding='utf-8') as f:
                json.dump(fuzzy_data, f, indent=2, ensure_ascii=False)

            self.logger.debug("Manual label caches saved to disk")

        except Exception as e:
            self.logger.error(f"Error saving caches: {str(e)}")

    def _save_stats(self):
        """Save statistics to disk"""
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving stats: {str(e)}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "exact_cache_size": len(self.exact_cache),
            "normalized_cache_size": len(self.normalized_cache),
            "fuzzy_cache_size": len(self.fuzzy_cache),
            "total_fuzzy_entries": sum(len(entries) for entries in self.fuzzy_cache.values()),
            "hit_rates": {
                "exact_hit_rate": self.stats["exact_hits"] / max(1, self.stats["total_lookups"]),
                "normalized_hit_rate": self.stats["normalized_hits"] / max(1, self.stats["total_lookups"]),
                "fuzzy_hit_rate": self.stats["fuzzy_hits"] / max(1, self.stats["total_lookups"]),
                "overall_hit_rate": (self.stats["exact_hits"] + self.stats["normalized_hits"] +
                                   self.stats["fuzzy_hits"]) / max(1, self.stats["total_lookups"])
            },
            "performance_stats": self.stats.copy(),
            "last_refresh": self.stats.get("last_refresh"),
            "cache_freshness": "fresh" if self._is_cache_fresh() else "stale"
        }

    def clear_caches(self):
        """Clear all caches"""
        try:
            self.exact_cache.clear()
            self.normalized_cache.clear()
            self.fuzzy_cache.clear()

            # Reset stats
            self.stats = {
                "exact_hits": 0,
                "normalized_hits": 0,
                "fuzzy_hits": 0,
                "total_lookups": 0,
                "cache_misses": 0,
                "last_refresh": None
            }

            # Remove cache files
            for cache_file in [self.exact_cache_file, self.normalized_cache_file,
                             self.fuzzy_cache_file, self.stats_file]:
                if cache_file.exists():
                    cache_file.unlink()

            self.logger.info("All manual label caches cleared")

        except Exception as e:
            self.logger.error(f"Error clearing caches: {str(e)}")

    def optimize_caches(self):
        """Optimize caches by removing unused entries"""
        try:
            # Remove entries that haven't been accessed recently
            cutoff_time = datetime.now().timestamp() - (30 * 24 * 3600)  # 30 days

            # Optimize exact cache
            to_remove = []
            for hash_id, entry in self.exact_cache.items():
                if entry.last_accessed.timestamp() < cutoff_time and entry.access_count == 0:
                    to_remove.append(hash_id)

            for hash_id in to_remove:
                del self.exact_cache[hash_id]

            # Similar optimization for other caches
            # ... (implement as needed)

            self._save_caches()
            self.logger.info(f"Cache optimization completed. Removed {len(to_remove)} unused entries")

        except Exception as e:
            self.logger.error(f"Error optimizing caches: {str(e)}")
