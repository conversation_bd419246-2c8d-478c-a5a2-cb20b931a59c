#!/usr/bin/env python3
"""
GPU System Setup Script

Automated setup and installation script for the GPU-accelerated
deep learning categorization system.
"""

import sys
import subprocess
import os
import time
from pathlib import Path
import json
from datetime import datetime


def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_gpu_availability():
    """Check if CUDA GPU is available"""
    print("\n🔧 Checking GPU availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            cuda_version = torch.version.cuda
            
            print(f"✅ CUDA GPU available: {gpu_name}")
            print(f"   GPU Count: {gpu_count}")
            print(f"   CUDA Version: {cuda_version}")
            
            # Check GPU memory
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"   GPU Memory: {gpu_memory:.1f} GB")
            
            if gpu_memory < 4.0:
                print("⚠️ Warning: GPU has less than 4GB memory. Training may be slow.")
            
            return True
        else:
            print("❌ CUDA GPU not available")
            print("   The system will use CPU (slower training)")
            return False
            
    except ImportError:
        print("❌ PyTorch not installed")
        return False


def install_gpu_dependencies():
    """Install GPU-specific dependencies"""
    print("\n📦 Installing GPU dependencies...")
    
    # GPU-specific packages
    gpu_packages = [
        "torch>=2.0.0",
        "torchtext>=0.15.0", 
        "transformers>=4.30.0",
        "accelerate>=0.20.0"
    ]
    
    for package in gpu_packages:
        print(f"   Installing {package}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
            print(f"   ✅ {package} installed")
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Failed to install {package}: {e}")
            return False
    
    return True


def setup_directories():
    """Setup required directories"""
    print("\n📁 Setting up directories...")
    
    directories = [
        "bank_analyzer_config/ml_models",
        "bank_analyzer_config/performance_monitoring",
        "data/processed_statements",
        "logs"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"   ✅ {directory}")
    
    return True


def create_gpu_config():
    """Create GPU system configuration"""
    print("\n⚙️ Creating GPU system configuration...")
    
    config = {
        "gpu_system": {
            "enabled": True,
            "auto_train": True,
            "min_confidence_threshold": 0.85,
            "target_accuracy": 0.95,
            "batch_size": 16,
            "max_epochs": 20,
            "learning_rate": 2e-5
        },
        "fallback_system": {
            "enabled": True,
            "confidence_threshold": 0.70
        },
        "performance_monitoring": {
            "enabled": True,
            "save_interval": 100,
            "optimization_interval": 1000
        },
        "setup_info": {
            "setup_date": datetime.now().isoformat(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "gpu_available": check_gpu_availability()
        }
    }
    
    config_path = Path("bank_analyzer_config/gpu_system_config.json")
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"   ✅ Configuration saved to {config_path}")
    return True


def test_gpu_system():
    """Test the GPU system installation"""
    print("\n🧪 Testing GPU system...")
    
    try:
        # Test imports
        print("   Testing imports...")
        from bank_analyzer.ml.gpu_deep_learning_categorizer import GPUDeepLearningCategorizer
        from bank_analyzer.ml.intelligent_category_router import IntelligentCategoryRouter
        from bank_analyzer.ml.integrated_gpu_categorizer import IntegratedGPUCategorizer
        print("   ✅ All imports successful")
        
        # Test initialization
        print("   Testing initialization...")
        categorizer = IntegratedGPUCategorizer()
        print("   ✅ GPU categorizer initialized")
        
        # Test sample categorization
        print("   Testing sample categorization...")
        sample_transaction = {
            'description': 'UPI-SALARY CREDIT FROM COMPANY ABC-GPAY',
            'amount': 50000.0,
            'date': '2025-01-01'
        }
        
        result = categorizer.categorize_transaction(sample_transaction)
        
        if result['success']:
            print(f"   ✅ Sample categorization successful: {result['category']}")
        else:
            print(f"   ⚠️ Sample categorization failed: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ GPU system test failed: {e}")
        return False


def setup_training_data():
    """Setup training data if available"""
    print("\n📊 Checking training data...")
    
    training_files = [
        "colab_training_data.csv",
        "unique_transactions.csv"
    ]
    
    found_files = []
    for file_name in training_files:
        if Path(file_name).exists():
            found_files.append(file_name)
            print(f"   ✅ Found: {file_name}")
    
    if found_files:
        print(f"   📈 {len(found_files)} training file(s) available")
        print("   💡 You can train the GPU model using:")
        print("      python -c \"from bank_analyzer.ml.integrated_gpu_categorizer import IntegratedGPUCategorizer; c = IntegratedGPUCategorizer(); c.train_gpu_model('colab_training_data.csv')\"")
    else:
        print("   ⚠️ No training data found")
        print("   💡 Add training data (colab_training_data.csv) to enable GPU model training")
    
    return True


def main():
    """Main setup function"""
    print("🚀 GPU-ACCELERATED CATEGORIZATION SYSTEM SETUP")
    print("=" * 60)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    gpu_available = check_gpu_availability()
    
    # Install dependencies
    if not install_gpu_dependencies():
        print("\n❌ Failed to install dependencies")
        return False
    
    # Setup directories
    if not setup_directories():
        print("\n❌ Failed to setup directories")
        return False
    
    # Create configuration
    if not create_gpu_config():
        print("\n❌ Failed to create configuration")
        return False
    
    # Test system
    if not test_gpu_system():
        print("\n❌ GPU system test failed")
        return False
    
    # Setup training data
    setup_training_data()
    
    # Final summary
    print("\n🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("📊 System Summary:")
    print(f"   GPU Available: {'✅ Yes' if gpu_available else '❌ No (CPU mode)'}")
    print("   Dependencies: ✅ Installed")
    print("   Configuration: ✅ Created")
    print("   System Test: ✅ Passed")
    
    print("\n📋 Next Steps:")
    print("1. Add training data (colab_training_data.csv) if not already present")
    print("2. Train the GPU model:")
    print("   python test_gpu_system_performance.py")
    print("3. Start using the enhanced categorization system!")
    
    print("\n💡 Performance Tips:")
    if gpu_available:
        print("   • GPU acceleration is enabled for optimal performance")
        print("   • Training will be significantly faster")
        print("   • Target accuracy: 95%+")
    else:
        print("   • Running in CPU mode (slower but functional)")
        print("   • Consider installing CUDA for GPU acceleration")
    
    print("\n🔧 Configuration file: bank_analyzer_config/gpu_system_config.json")
    print("📄 Test the system: python test_gpu_system_performance.py")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
