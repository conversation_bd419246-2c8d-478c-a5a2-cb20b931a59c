#!/usr/bin/env python3
"""
Quick GPU Model Training Script
Train the GPU model with CUDA support
"""

import sys
import time
from pathlib import Path

# Add bank_analyzer to path
sys.path.append('.')

def main():
    print("🚀 TRAINING GPU MODEL WITH RTX 4050")
    print("=" * 50)
    
    try:
        # Check CUDA first
        import torch
        print(f"✅ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"🎮 GPU device: {torch.cuda.get_device_name(0)}")
        
        # Import GPU categorizer
        from bank_analyzer.ml.integrated_gpu_categorizer import IntegratedGPUCategorizer
        
        # Check training data
        training_data = Path("colab_training_data.csv")
        if not training_data.exists():
            print(f"❌ Training data not found: {training_data}")
            return False
        
        print(f"📁 Training data: {training_data}")
        
        # Initialize categorizer
        print("📦 Initializing GPU categorizer...")
        categorizer = IntegratedGPUCategorizer()
        
        # Start training
        print("🤖 Starting GPU model training...")
        print("   This will take a few minutes with GPU acceleration...")
        
        start_time = time.time()
        result = categorizer.train_gpu_model('colab_training_data.csv', use_cross_validation=False)
        training_time = time.time() - start_time
        
        # Show results
        print(f"\n🎉 Training completed in {training_time:.1f} seconds!")
        
        if result.get('success', False):
            print(f"✅ Training successful!")
            print(f"   Accuracy: {result.get('accuracy', 0)*100:.1f}%")
            print(f"   Categories learned: {result.get('categories_learned', 0)}")
            print(f"   Total samples: {result.get('total_samples', 0)}")
            
            # Test the trained model
            print("\n🧪 Testing trained model...")
            test_transaction = {
                'description': 'UPI-SALARY CREDIT FROM TECH COMPANY-GPAY',
                'amount': 50000.0,
                'date': '2025-01-01'
            }
            
            test_result = categorizer.categorize_transaction(test_transaction)
            print(f"   Test result: {test_result['category']} ({test_result['method_used']})")
            print(f"   Confidence: {test_result['confidence']:.2f}")
            
            if test_result['method_used'] == 'GPU_ML':
                print("🚀 GPU deep learning is working!")
            else:
                print(f"🔧 Using fallback: {test_result['method_used']}")
            
            return True
        else:
            print(f"❌ Training failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error during training: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 GPU model training completed successfully!")
        print("   You can now run the demo to see GPU categorization in action")
    else:
        print("\n⚠️ GPU model training failed")
        print("   The system will continue to use fallback methods")
