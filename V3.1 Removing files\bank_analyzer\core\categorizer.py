"""
Transaction categorization engine
Automatically categorizes transactions based on description patterns and rules
Enhanced with GPU-accelerated deep learning capabilities
"""

import re
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging

from ..models.transaction import RawTransaction, ProcessedTransaction
from ..core.logger import get_logger

# Import GPU categorizer (with fallback for backward compatibility)
try:
    from ..ml.integrated_gpu_categorizer import IntegratedGPUCategorizer
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False


@dataclass
class CategoryRule:
    """Rule for categorizing transactions"""
    category: str
    sub_category: str
    keywords: List[str]
    patterns: List[str]  # Regex patterns
    confidence_weight: float = 1.0
    transaction_type: str = "Expense"  # "Expense", "Income", "Transfer"


class TransactionCategorizer:
    """
    Enhanced automatic transaction categorization engine
    Uses GPU-accelerated deep learning, keyword matching, pattern recognition, and rule-based classification
    """

    def __init__(self, main_app_data_dir: str = "data", enable_gpu: bool = True):
        self.logger = get_logger(__name__)
        self.main_app_data_dir = Path(main_app_data_dir)

        # Initialize GPU categorizer if available and enabled
        self.gpu_categorizer = None
        if GPU_AVAILABLE and enable_gpu:
            try:
                self.gpu_categorizer = IntegratedGPUCategorizer()
                self.logger.info("GPU categorization system initialized")
            except Exception as e:
                self.logger.warning(f"Failed to initialize GPU categorizer: {e}")
                self.gpu_categorizer = None
        elif not GPU_AVAILABLE:
            self.logger.info("GPU categorization not available, using traditional methods")

        # Load existing categories from main application
        self.categories = self._load_existing_categories()

        # Initialize categorization rules
        self.rules = self._initialize_categorization_rules()

        # Transaction mode mapping
        self.transaction_modes = [
            "Cash", "Credit Card", "Debit Card", "UPI", "Net Banking",
            "Wallet", "Cheque", "Bank Transfer", "Other"
        ]

        # Performance tracking
        self.categorization_stats = {
            'total_categorizations': 0,
            'gpu_categorizations': 0,
            'rule_categorizations': 0,
            'manual_categorizations': 0
        }
    
    def _load_existing_categories(self) -> Dict[str, List[str]]:
        """Load existing categories from main application"""
        categories = {}
        
        try:
            categories_file = self.main_app_data_dir / "expenses" / "categories.csv"
            
            if categories_file.exists():
                df = pd.read_csv(categories_file)
                
                for _, row in df.iterrows():
                    category = row['category']
                    sub_category = row['sub_category']
                    
                    if category not in categories:
                        categories[category] = []
                    
                    if sub_category not in categories[category]:
                        categories[category].append(sub_category)
                
                self.logger.info(f"Loaded {len(categories)} categories from main application")
            else:
                self.logger.warning("Categories file not found, using default categories")
                categories = self._get_default_categories()
                
        except Exception as e:
            self.logger.error(f"Error loading categories: {str(e)}")
            categories = self._get_default_categories()
        
        return categories
    
    def _get_default_categories(self) -> Dict[str, List[str]]:
        """Get default categories if main application categories are not available"""
        return {
            "Food & Dining": ["Restaurants", "Groceries", "Fast Food", "Coffee", "Delivery"],
            "Transportation": ["Fuel", "Public Transport", "Taxi/Uber", "Parking", "Maintenance"],
            "Shopping": ["Clothing", "Electronics", "Books", "Home & Garden", "Groceries"],
            "Entertainment": ["Movies", "Games", "Sports", "Hobbies", "Subscriptions"],
            "Bills & Utilities": ["Electricity", "Water", "Internet", "Phone", "Insurance"],
            "Healthcare": ["Doctor", "Pharmacy", "Dental", "Vision", "Fitness"],
            "Education": ["Courses", "Books", "Supplies", "Tuition", "Certification"],
            "Travel": ["Flights", "Hotels", "Car Rental", "Activities", "Food"],
            "Personal Care": ["Haircut", "Cosmetics", "Spa", "Clothing", "Accessories"],
            "Gifts & Donations": ["Gifts", "Charity", "Tips", "Religious", "Social"],
            "Business": ["Office Supplies", "Software", "Equipment", "Travel", "Meals"],
            "Other": ["Miscellaneous", "Unexpected", "Emergency", "Investment", "Savings"]
        }
    
    def _initialize_categorization_rules(self) -> List[CategoryRule]:
        """Initialize categorization rules based on common transaction patterns"""
        rules = []
        
        # Food & Dining rules
        rules.extend([
            CategoryRule("Food & Dining", "Restaurants", 
                        ["restaurant", "cafe", "dine", "dining", "eatery", "bistro", "grill"],
                        [r"\b(restaurant|cafe|dine|dining)\b", r"\b(food|meal)\b.*\b(out|restaurant)\b"],
                        1.0),
            CategoryRule("Food & Dining", "Groceries",
                        ["grocery", "supermarket", "market", "store", "mart", "bazaar", "provision"],
                        [r"\b(grocery|supermarket|market)\b", r"\b(big\s*bazaar|reliance|dmart)\b"],
                        1.0),
            CategoryRule("Food & Dining", "Fast Food",
                        ["mcdonald", "kfc", "pizza", "burger", "domino", "subway", "fast food"],
                        [r"\b(mcd|kfc|pizza|burger|domino|subway)\b"],
                        1.0),
            CategoryRule("Food & Dining", "Delivery",
                        ["zomato", "swiggy", "uber eats", "food delivery", "delivery"],
                        [r"\b(zomato|swiggy|uber\s*eats)\b"],
                        1.0)
        ])
        
        # Transportation rules
        rules.extend([
            CategoryRule("Transportation", "Fuel",
                        ["petrol", "diesel", "fuel", "gas", "hp", "ioc", "bpcl", "shell"],
                        [r"\b(petrol|diesel|fuel|gas)\b", r"\b(hp|ioc|bpcl|shell)\b"],
                        1.0),
            CategoryRule("Transportation", "Public Transport",
                        ["metro", "bus", "train", "railway", "transport", "ticket"],
                        [r"\b(metro|bus|train|railway)\b", r"\b(transport|ticket)\b"],
                        0.8),
            CategoryRule("Transportation", "Taxi/Uber",
                        ["uber", "ola", "taxi", "cab", "ride"],
                        [r"\b(uber|ola|taxi|cab)\b"],
                        1.0),
            CategoryRule("Transportation", "Parking",
                        ["parking", "toll", "toll plaza"],
                        [r"\b(parking|toll)\b"],
                        0.9)
        ])
        
        # Shopping rules
        rules.extend([
            CategoryRule("Shopping", "Electronics",
                        ["amazon", "flipkart", "electronics", "mobile", "laptop", "computer"],
                        [r"\b(amazon|flipkart)\b", r"\b(electronics|mobile|laptop|computer)\b"],
                        0.8),
            CategoryRule("Shopping", "Clothing",
                        ["clothing", "fashion", "apparel", "shirt", "dress", "shoes"],
                        [r"\b(clothing|fashion|apparel|shirt|dress|shoes)\b"],
                        0.7),
            CategoryRule("Shopping", "Books",
                        ["book", "library", "bookstore", "kindle"],
                        [r"\b(book|library|bookstore|kindle)\b"],
                        0.8)
        ])
        
        # Bills & Utilities rules
        rules.extend([
            CategoryRule("Bills & Utilities", "Electricity",
                        ["electricity", "power", "electric", "bescom", "mseb"],
                        [r"\b(electricity|power|electric|bescom|mseb)\b"],
                        1.0),
            CategoryRule("Bills & Utilities", "Internet",
                        ["internet", "broadband", "wifi", "airtel", "jio", "bsnl"],
                        [r"\b(internet|broadband|wifi|airtel|jio|bsnl)\b"],
                        0.9),
            CategoryRule("Bills & Utilities", "Phone",
                        ["mobile", "phone", "recharge", "prepaid", "postpaid"],
                        [r"\b(mobile|phone|recharge|prepaid|postpaid)\b"],
                        0.8)
        ])
        
        # Healthcare rules
        rules.extend([
            CategoryRule("Healthcare", "Doctor",
                        ["doctor", "clinic", "hospital", "medical", "consultation"],
                        [r"\b(doctor|clinic|hospital|medical|consultation)\b"],
                        0.9),
            CategoryRule("Healthcare", "Pharmacy",
                        ["pharmacy", "medicine", "drug", "chemist", "apollo"],
                        [r"\b(pharmacy|medicine|drug|chemist|apollo)\b"],
                        0.9)
        ])
        
        # Income rules
        rules.extend([
            CategoryRule("Income", "Salary",
                        ["salary", "wages", "payroll", "income"],
                        [r"\b(salary|wages|payroll|income)\b"],
                        1.0, "Income"),
            CategoryRule("Income", "Interest",
                        ["interest", "dividend", "returns"],
                        [r"\b(interest|dividend|returns)\b"],
                        0.9, "Income")
        ])
        
        return rules

    def categorize_transaction(self, raw_transaction: RawTransaction) -> ProcessedTransaction:
        """
        Enhanced categorize a raw transaction using GPU deep learning and fallback methods

        Args:
            raw_transaction: Raw transaction to categorize

        Returns:
            ProcessedTransaction with category assignments
        """
        processed = ProcessedTransaction()
        processed.update_from_raw(raw_transaction)

        self.categorization_stats['total_categorizations'] += 1

        # Try GPU categorization first if available
        if self.gpu_categorizer:
            try:
                # Convert to format expected by GPU categorizer
                transaction_dict = {
                    'description': raw_transaction.description,
                    'amount': raw_transaction.amount,
                    'date': raw_transaction.date.isoformat() if raw_transaction.date else None
                }

                gpu_result = self.gpu_categorizer.categorize_transaction(transaction_dict)

                if gpu_result['success'] and gpu_result['confidence'] > 0.7:
                    processed.category = gpu_result['category']
                    processed.sub_category = gpu_result['sub_category']
                    processed.confidence = gpu_result['confidence']
                    processed.categorization_method = gpu_result['method_used']

                    self.categorization_stats['gpu_categorizations'] += 1

                    self.logger.debug(f"GPU categorized '{raw_transaction.description[:50]}...' as '{processed.category}' using {gpu_result['method_used']}")
                    return processed

            except Exception as e:
                self.logger.warning(f"GPU categorization failed, falling back to rules: {e}")

        # Fallback to traditional rule-based categorization
        suggestions = self._get_category_suggestions(raw_transaction.description)

        if suggestions:
            # Use the best suggestion
            best_suggestion = suggestions[0]
            processed.category = best_suggestion['category']
            processed.sub_category = best_suggestion['sub_category']
            processed.confidence = best_suggestion.get('confidence', 0.8)
            processed.categorization_method = "Rule_Based"

            self.categorization_stats['rule_categorizations'] += 1
            # Don't override transaction type - it was correctly set in update_from_raw()
            # processed.type = best_suggestion['transaction_type']
            processed.confidence_score = best_suggestion['confidence']
            processed.suggested_categories = suggestions[:3]  # Keep top 3 suggestions
        else:
            # Default categorization
            processed.category = "Other"
            processed.sub_category = "Miscellaneous"
            processed.confidence_score = 0.1

        # Determine transaction mode based on description
        processed.transaction_mode = self._determine_transaction_mode(raw_transaction.description)

        return processed

    def _get_category_suggestions(self, description: str) -> List[Dict[str, Any]]:
        """
        Get category suggestions for a transaction description

        Args:
            description: Transaction description

        Returns:
            List of category suggestions with confidence scores
        """
        if not description:
            return []

        description_lower = description.lower()
        suggestions = []

        for rule in self.rules:
            confidence = 0.0

            # Check keyword matches
            keyword_matches = 0
            for keyword in rule.keywords:
                if keyword.lower() in description_lower:
                    keyword_matches += 1

            if keyword_matches > 0:
                confidence += (keyword_matches / len(rule.keywords)) * 0.6

            # Check pattern matches
            pattern_matches = 0
            for pattern in rule.patterns:
                try:
                    if re.search(pattern, description_lower, re.IGNORECASE):
                        pattern_matches += 1
                except re.error:
                    continue

            if pattern_matches > 0:
                confidence += (pattern_matches / len(rule.patterns)) * 0.4

            # Apply rule weight
            confidence *= rule.confidence_weight

            if confidence > 0:
                suggestions.append({
                    'category': rule.category,
                    'sub_category': rule.sub_category,
                    'transaction_type': rule.transaction_type,
                    'confidence': confidence,
                    'matched_keywords': keyword_matches,
                    'matched_patterns': pattern_matches
                })

        # Sort by confidence score
        suggestions.sort(key=lambda x: x['confidence'], reverse=True)

        return suggestions

    def _determine_transaction_mode(self, description: str) -> str:
        """
        Determine transaction mode based on description

        Args:
            description: Transaction description

        Returns:
            Transaction mode string
        """
        if not description:
            return "Other"

        description_lower = description.lower()

        # UPI patterns
        if any(term in description_lower for term in ['upi', 'paytm', 'phonepe', 'gpay', 'google pay']):
            return "UPI"

        # Card patterns
        if any(term in description_lower for term in ['card', 'pos', 'atm']):
            if 'credit' in description_lower:
                return "Credit Card"
            else:
                return "Debit Card"

        # Net banking patterns
        if any(term in description_lower for term in ['neft', 'rtgs', 'imps', 'net banking', 'online']):
            return "Net Banking"

        # Cash patterns
        if any(term in description_lower for term in ['cash', 'withdrawal', 'deposit']):
            return "Cash"

        # Cheque patterns
        if any(term in description_lower for term in ['cheque', 'check', 'chq']):
            return "Cheque"

        return "Other"

    def add_custom_rule(self, category: str, sub_category: str, keywords: List[str],
                       patterns: List[str] = None, confidence_weight: float = 1.0,
                       transaction_type: str = "Expense") -> bool:
        """
        Add a custom categorization rule

        Args:
            category: Category name
            sub_category: Sub-category name
            keywords: List of keywords to match
            patterns: List of regex patterns to match
            confidence_weight: Weight for this rule
            transaction_type: Type of transaction

        Returns:
            True if rule was added successfully
        """
        try:
            if patterns is None:
                patterns = []

            rule = CategoryRule(
                category=category,
                sub_category=sub_category,
                keywords=keywords,
                patterns=patterns,
                confidence_weight=confidence_weight,
                transaction_type=transaction_type
            )

            self.rules.append(rule)
            self.logger.info(f"Added custom rule for {category}/{sub_category}")
            return True

        except Exception as e:
            self.logger.error(f"Error adding custom rule: {str(e)}")
            return False

    def get_categories(self) -> Dict[str, List[str]]:
        """Get available categories and subcategories"""
        return self.categories.copy()

    def add_category(self, category: str, sub_category: str) -> bool:
        """
        Add a new category/subcategory combination

        Args:
            category: Category name
            sub_category: Sub-category name

        Returns:
            True if added successfully
        """
        try:
            if category not in self.categories:
                self.categories[category] = []

            if sub_category not in self.categories[category]:
                self.categories[category].append(sub_category)
                self.logger.info(f"Added new category: {category}/{sub_category}")
                return True
            else:
                self.logger.warning(f"Category already exists: {category}/{sub_category}")
                return False

        except Exception as e:
            self.logger.error(f"Error adding category: {str(e)}")
            return False

    def batch_categorize(self, raw_transactions: List[RawTransaction]) -> List[ProcessedTransaction]:
        """
        Categorize multiple transactions in batch

        Args:
            raw_transactions: List of raw transactions

        Returns:
            List of processed transactions
        """
        processed_transactions = []

        for raw_transaction in raw_transactions:
            try:
                processed = self.categorize_transaction(raw_transaction)
                processed_transactions.append(processed)
            except Exception as e:
                self.logger.error(f"Error categorizing transaction: {str(e)}")
                # Create a basic processed transaction with minimal data
                processed = ProcessedTransaction()
                processed.update_from_raw(raw_transaction)
                processed.category = "Other"
                processed.sub_category = "Miscellaneous"
                processed.transaction_mode = "Other"
                processed_transactions.append(processed)

        return processed

    def train_gpu_model(self, csv_path: str) -> Dict[str, Any]:
        """
        Train the GPU model with new data

        Args:
            csv_path: Path to training data CSV

        Returns:
            Training results dictionary
        """
        if not self.gpu_categorizer:
            return {'success': False, 'error': 'GPU categorizer not available'}

        try:
            results = self.gpu_categorizer.train_gpu_model(csv_path)
            self.logger.info(f"GPU model training completed with {results.get('accuracy', 0)*100:.1f}% accuracy")
            return results
        except Exception as e:
            self.logger.error(f"GPU model training failed: {e}")
            return {'success': False, 'error': str(e)}

    def get_categorization_stats(self) -> Dict[str, Any]:
        """
        Get categorization performance statistics

        Returns:
            Dictionary with performance statistics
        """
        stats = self.categorization_stats.copy()

        if stats['total_categorizations'] > 0:
            stats['gpu_percentage'] = (stats['gpu_categorizations'] / stats['total_categorizations']) * 100
            stats['rule_percentage'] = (stats['rule_categorizations'] / stats['total_categorizations']) * 100
        else:
            stats['gpu_percentage'] = 0
            stats['rule_percentage'] = 0

        # Add GPU system stats if available
        if self.gpu_categorizer:
            gpu_stats = self.gpu_categorizer.get_system_statistics()
            stats['gpu_system_stats'] = gpu_stats

        return stats

    def optimize_gpu_system(self):
        """
        Optimize the GPU categorization system
        """
        if self.gpu_categorizer:
            self.gpu_categorizer.optimize_system()
            self.logger.info("GPU system optimization completed")
        else:
            self.logger.warning("GPU categorizer not available for optimization")

    def backup_gpu_models(self) -> Dict[str, Any]:
        """
        Create backup of GPU models

        Returns:
            Backup results dictionary
        """
        if not self.gpu_categorizer:
            return {'success': False, 'error': 'GPU categorizer not available'}

        return self.gpu_categorizer.backup_models()

    def is_gpu_available(self) -> bool:
        """
        Check if GPU categorization is available

        Returns:
            True if GPU categorizer is available and functional
        """
        return self.gpu_categorizer is not None and self.gpu_categorizer.system_status['gpu_available']

    def get_gpu_performance_summary(self) -> Dict[str, Any]:
        """
        Get GPU system performance summary

        Returns:
            Performance summary dictionary
        """
        if not self.gpu_categorizer:
            return {'available': False, 'message': 'GPU categorizer not available'}

        try:
            return self.gpu_categorizer.router.get_performance_summary()
        except Exception as e:
            return {'available': False, 'error': str(e)}
