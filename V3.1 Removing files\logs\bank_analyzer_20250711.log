2025-07-11 01:10:26 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-07-11 01:10:26 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250711.log
2025-07-11 01:10:26 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-07-11 01:10:26 - __main__ - INFO - main:89 - Starting Bank Statement Analyzer application
2025-07-11 01:10:26 - __main__ - INFO - main:98 - ✅ NLTK data available for text processing
2025-07-11 01:10:26 - bank_analyzer.core.directory_manager - INFO - __init__:56 - Directory Manager initialized with organized structure
2025-07-11 01:10:26 - bank_analyzer.ui.main_window - INFO - check_existing_processed_data:431 - Found 6 processed files across all directories, latest: data\processed_statements\transactions_Indian_Bank_20250710_020937.csv with 1938 transactions
2025-07-11 01:10:26 - bank_analyzer.ui.main_window - INFO - __init__:95 - Bank Analyzer main window initialized
2025-07-11 01:10:26 - __main__ - INFO - main:131 - Bank Statement Analyzer application started successfully
2025-07-11 01:13:11 - bank_analyzer.ui.main_window - INFO - _load_processed_transactions:644 - Loaded 1938 processed transactions for categorization
2025-07-11 01:13:12 - bank_analyzer.ui.main_window - INFO - _load_processed_file:535 - Loaded processed data from: data\processed_statements\transactions_Indian_Bank_20250710_020937.csv
2025-07-11 01:13:14 - bank_analyzer.ui.main_window - INFO - _load_processed_transactions:644 - Loaded 1938 processed transactions for categorization
2025-07-11 01:13:14 - bank_analyzer.core.transaction_data_manager - INFO - __init__:105 - Transaction Data Manager initialized
2025-07-11 01:13:15 - bank_analyzer.ui.hybrid_categorization_qt - INFO - _initialize_components_async:369 - Starting asynchronous component initialization...
2025-07-11 01:13:19 - bank_analyzer.ml.manual_label_priority_system - INFO - _load_caches:517 - Loaded manual label caches: 2004 exact, 2004 normalized, 879 fuzzy
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:119 - Using NLTK PorterStemmer
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:131 - Enhanced stopwords with 132 additional NLTK stopwords
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - __init__:107 - TextPreprocessor initialized with 198 stopwords (NLTK enhanced mode)
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:627 - Loaded improved ML models successfully
2025-07-11 01:13:19 - bank_analyzer.core.categorizer - INFO - __init__:56 - GPU categorization not available, using traditional methods
2025-07-11 01:13:19 - bank_analyzer.core.categorizer - WARNING - _load_existing_categories:100 - Categories file not found, using default categories
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:119 - Using NLTK PorterStemmer
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:131 - Enhanced stopwords with 132 additional NLTK stopwords
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - __init__:107 - TextPreprocessor initialized with 198 stopwords (NLTK enhanced mode)
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:627 - Loaded improved ML models successfully
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:119 - Using NLTK PorterStemmer
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:131 - Enhanced stopwords with 132 additional NLTK stopwords
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - __init__:107 - TextPreprocessor initialized with 198 stopwords (NLTK enhanced mode)
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:627 - Loaded improved ML models successfully
2025-07-11 01:13:19 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - _create_automatic_backup:689 - Created automatic backup: bank_analyzer_config\merchant_cache\backups\patterns_backup_20250711_011319.json
2025-07-11 01:13:19 - bank_analyzer.ml.enhanced_merchant_mapper - INFO - __init__:158 - Enhanced merchant mapper initialized with 0 patterns
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:119 - Using NLTK PorterStemmer
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _setup_nltk:131 - Enhanced stopwords with 132 additional NLTK stopwords
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - __init__:107 - TextPreprocessor initialized with 198 stopwords (NLTK enhanced mode)
2025-07-11 01:13:19 - bank_analyzer.ml.ml_categorizer - INFO - _load_models:627 - Loaded improved ML models successfully
2025-07-11 01:13:19 - bank_analyzer.ml.hybrid_prediction_engine - INFO - _initialize_system:111 - Initializing hybrid prediction engine...
2025-07-11 01:13:19 - bank_analyzer.ml.manual_label_priority_system - INFO - bulk_load_manual_labels:220 - Bulk loading manual labels from training data...
2025-07-11 01:13:20 - bank_analyzer.ml.data_preparation - INFO - load_unique_transactions:666 - Loaded 2030 unique transactions from storage in 0.961 seconds
2025-07-11 01:13:20 - bank_analyzer.ml.manual_label_priority_system - INFO - bulk_load_manual_labels:282 - Bulk loaded 2030 manual labels into priority cache
2025-07-11 01:13:20 - bank_analyzer.ml.hybrid_prediction_engine - INFO - _initialize_system:115 - Loaded 2030 manual labels into priority cache
2025-07-11 01:13:20 - bank_analyzer.ml.hybrid_prediction_engine - INFO - _initialize_system:119 - Initializing hierarchical classifier...
2025-07-11 01:13:20 - bank_analyzer.ml.hybrid_prediction_engine - INFO - _initialize_system:122 - Hybrid prediction engine initialized successfully
2025-07-11 01:13:20 - bank_analyzer.ml.category_manager - INFO - _load_main_app_categories:275 - Loaded 50 categories from main application
2025-07-11 01:13:20 - bank_analyzer.ml.category_manager - INFO - _load_ml_categories:308 - Loaded 181 ML-specific categories
2025-07-11 01:13:20 - bank_analyzer.ml.enhanced_training_data_manager - INFO - __init__:97 - Enhanced Training Data Manager initialized
2025-07-11 01:13:20 - bank_analyzer.ui.hybrid_categorization_qt - INFO - _on_initialization_complete:433 - ✅ All components initialized successfully
2025-07-11 01:13:20 - bank_analyzer.ui.hybrid_categorization_qt - INFO - _handle_session_management:1074 - Processing 1938 transactions from live data
2025-07-11 01:13:21 - bank_analyzer.ui.hybrid_categorization_qt - INFO - _run_background_validation:1129 - Background validation found 10 potential issues in first 10 transactions
2025-07-11 01:13:25 - bank_analyzer.ui.hybrid_categorization_qt - INFO - process_ml_categorization:1823 - Using Hybrid Prediction Engine for categorization
2025-07-11 01:13:25 - bank_analyzer.ui.hybrid_categorization_qt - INFO - process_ml_categorization:1853 - Started ML processing of 1938 transactions
2025-07-11 01:21:42 - __main__ - INFO - main:135 - Bank Statement Analyzer application exited with code: 0
