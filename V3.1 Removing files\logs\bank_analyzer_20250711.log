2025-07-11 01:10:26 - bank_analyzer.core.logger - INFO - setup_logging:72 - Logging system initialized
2025-07-11 01:10:26 - bank_analyzer.core.logger - INFO - setup_logging:73 - Log file: logs\bank_analyzer_20250711.log
2025-07-11 01:10:26 - bank_analyzer.core.logger - INFO - setup_logging:74 - Log level: INFO
2025-07-11 01:10:26 - __main__ - INFO - main:89 - Starting Bank Statement Analyzer application
2025-07-11 01:10:26 - __main__ - INFO - main:98 - ✅ NLTK data available for text processing
2025-07-11 01:10:26 - bank_analyzer.core.directory_manager - INFO - __init__:56 - Directory Manager initialized with organized structure
2025-07-11 01:10:26 - bank_analyzer.ui.main_window - INFO - check_existing_processed_data:431 - Found 6 processed files across all directories, latest: data\processed_statements\transactions_Indian_Bank_20250710_020937.csv with 1938 transactions
2025-07-11 01:10:26 - bank_analyzer.ui.main_window - INFO - __init__:95 - Bank Analyzer main window initialized
2025-07-11 01:10:26 - __main__ - INFO - main:131 - Bank Statement Analyzer application started successfully
