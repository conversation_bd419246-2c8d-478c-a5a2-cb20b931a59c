# Standalone Bank Statement Analyzer Requirements
# Complete dependency list for standalone operation with GPU acceleration

# Core GUI Framework
PySide6>=6.5.0

# Data Processing
pandas>=1.5.0
numpy>=1.24.0

# PDF Processing
PyPDF2>=3.0.0
pdfplumber>=0.9.0

# Excel File Processing
openpyxl>=3.1.0
xlrd>=2.0.0

# Machine Learning (Traditional)
scikit-learn>=1.3.0
joblib>=1.3.0

# GPU-Accelerated Deep Learning (CUDA-enabled)
# Note: Install with CUDA support using:
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
torch>=2.5.0
torchvision>=0.16.0
torchaudio>=2.1.0
transformers>=4.30.0
accelerate>=0.20.0
datasets>=2.14.0

# Natural Language Processing
nltk>=3.8.0
tokenizers>=0.13.0

# HTTP Requests (for AI APIs)
requests>=2.31.0

# JSON Schema Validation
jsonschema>=4.17.0

# Date/Time Processing
python-dateutil>=2.8.0

# Logging and Configuration
colorama>=0.4.6

# Additional ML Dependencies
tqdm>=4.65.0
matplotlib>=3.7.0
seaborn>=0.12.0

# GPU Memory Management
psutil>=5.9.0

# Optional: Better PDF parsing (uncomment if needed)
# pymupdf>=1.23.0

# Development and Testing (optional)
# pytest>=7.4.0
# pytest-qt>=4.2.0

# CUDA Installation Notes:
# ========================
# For GPU acceleration with NVIDIA RTX 4050/4060 or similar:
# 1. Install NVIDIA drivers from: https://www.nvidia.com/drivers/
# 2. Install CUDA-enabled PyTorch:
#    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
# 3. Verify CUDA: python -c "import torch; print(torch.cuda.is_available())"
#
# Alternative CUDA versions:
# - CUDA 11.8: --index-url https://download.pytorch.org/whl/cu118
# - CUDA 12.1: --index-url https://download.pytorch.org/whl/cu121
# - CPU only: --index-url https://download.pytorch.org/whl/cpu
