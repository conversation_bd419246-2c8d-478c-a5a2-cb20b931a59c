# Standalone Bank Statement Analyzer Requirements
# Complete dependency list for standalone operation

# Core GUI Framework
PySide6>=6.5.0

# Data Processing
pandas>=1.5.0
numpy>=1.24.0

# PDF Processing
PyPDF2>=3.0.0
pdfplumber>=0.9.0

# Excel File Processing
openpyxl>=3.1.0
xlrd>=2.0.0

# Machine Learning
scikit-learn>=1.3.0
joblib>=1.3.0

# GPU-Accelerated Deep Learning
torch>=2.0.0
torchtext>=0.15.0
transformers>=4.30.0
accelerate>=0.20.0

# Natural Language Processing
nltk>=3.8.0

# HTTP Requests (for AI APIs)
requests>=2.31.0

# JSON Schema Validation
jsonschema>=4.17.0

# Date/Time Processing
python-dateutil>=2.8.0

# Logging and Configuration
colorama>=0.4.6

# Optional: Better PDF parsing (uncomment if needed)
# pymupdf>=1.23.0

# Development and Testing (optional)
# pytest>=7.4.0
# pytest-qt>=4.2.0
