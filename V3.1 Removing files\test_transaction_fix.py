#!/usr/bin/env python3
"""
Test script to verify the transaction fix
"""

import sys
from datetime import date
from decimal import Decimal

# Add bank_analyzer to path
sys.path.append('.')

def test_transaction_creation():
    """Test creating RawTransaction objects"""
    try:
        from bank_analyzer.models.transaction import RawTransaction
        print("✅ Successfully imported RawTransaction")
        
        # Create a test transaction
        transaction = RawTransaction(
            date=date(2025, 1, 1),
            description='UPI-SALARY CREDIT FROM COMPANY ABC PVT LTD-GPAY',
            amount=Decimal('50000.0'),
            balance=Decimal('100000.0'),
            transaction_type='CREDIT'
        )
        
        print(f"✅ Created transaction: {transaction.description}")
        print(f"   Date: {transaction.date}")
        print(f"   Amount: {transaction.amount}")
        print(f"   Type: {transaction.transaction_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating transaction: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intelligent_router():
    """Test the intelligent router with proper transaction objects"""
    try:
        from bank_analyzer.ml.intelligent_category_router import IntelligentCategoryRouter
        from bank_analyzer.models.transaction import RawTransaction
        
        print("✅ Successfully imported IntelligentCategoryRouter")
        
        # Create router
        router = IntelligentCategoryRouter()
        print("✅ Created router instance")
        
        # Create test transaction
        transaction = RawTransaction(
            date=date(2025, 1, 1),
            description='UPI-SALARY CREDIT FROM COMPANY ABC PVT LTD-GPAY',
            amount=Decimal('50000.0'),
            balance=Decimal('100000.0'),
            transaction_type='CREDIT'
        )
        
        print("✅ Created test transaction")
        
        # Try categorization
        result = router.categorize_transaction(transaction)
        print(f"✅ Categorization successful!")
        print(f"   Category: {result.category}")
        print(f"   Sub-Category: {result.sub_category}")
        print(f"   Method: {result.method_used}")
        print(f"   Confidence: {result.confidence:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with intelligent router: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Transaction Fix")
    print("=" * 40)
    
    # Test 1: Transaction creation
    print("\n1. Testing RawTransaction creation...")
    transaction_ok = test_transaction_creation()
    
    # Test 2: Intelligent router
    print("\n2. Testing IntelligentCategoryRouter...")
    router_ok = test_intelligent_router()
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 Test Summary:")
    print(f"   Transaction Creation: {'✅ PASS' if transaction_ok else '❌ FAIL'}")
    print(f"   Intelligent Router: {'✅ PASS' if router_ok else '❌ FAIL'}")
    
    if transaction_ok and router_ok:
        print("\n🎉 All tests passed! The fix should work.")
    else:
        print("\n⚠️ Some tests failed. Need to investigate further.")
