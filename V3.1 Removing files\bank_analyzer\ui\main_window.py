"""
Main window for Bank Statement Analyzer - RESTRUCTURED
Clean implementation with only core functionality as specified in requirements
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QFileDialog, QProgressBar, QTextEdit,
    QGroupBox, QFormLayout, QMessageBox, QStatusBar,
    QMenuBar, QComboBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QSplitter, QFrame
)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QAction, QFont

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import csv

from ..core.logger import get_logger
from ..core.directory_manager import get_directory_manager
from ..parsers.parser_factory import parser_factory
from ..models.transaction import RawTransaction, ProcessedTransaction
from ..models.transaction_wrapper import wrap_transactions


class StatementProcessingThread(QThread):
    """Thread for processing bank statements in background"""
    
    progress_updated = Signal(int, str)  # progress percentage, status message
    processing_completed = Signal(list)  # raw_transactions
    error_occurred = Signal(str)  # error message
    
    def __init__(self, file_paths: List[Path], bank_name: str, period_type: str = "monthly"):
        super().__init__()
        self.file_paths = file_paths
        self.bank_name = bank_name
        self.period_type = period_type
        self.logger = get_logger(__name__)
    
    def run(self):
        """Run the statement processing"""
        try:
            all_raw_transactions = []
            total_files = len(self.file_paths)
            
            # Parse all files
            for i, file_path in enumerate(self.file_paths):
                self.progress_updated.emit(
                    int((i / total_files) * 100),  # Full progress for parsing only
                    f"Parsing {file_path.name}..."
                )
                
                transactions = parser_factory.parse_file(file_path, self.bank_name, self.period_type)
                all_raw_transactions.extend(transactions)
                
                self.logger.info(f"Parsed {len(transactions)} transactions from {file_path}")
            
            self.progress_updated.emit(100, "Processing completed")
            self.processing_completed.emit(all_raw_transactions)
            
        except Exception as e:
            self.logger.error(f"Error processing statements: {str(e)}")
            self.error_occurred.emit(str(e))


class BankAnalyzerMainWindow(QMainWindow):
    """
    Main window for the Bank Statement Analyzer application - RESTRUCTURED
    Clean implementation with only core functionality
    """
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        
        # Core data
        self.raw_transactions = []
        self.selected_file_paths = []
        self.processing_thread = None
        self.csv_file_path = None
        
        # ML labeling window
        self.ml_labeling_window = None
        self.ml_training_window = None
        self.ai_processing_window = None
        
        self.setup_ui()

        # Check for existing processed data after UI setup
        self.check_existing_processed_data()

        self.logger.info("Bank Analyzer main window initialized")
    
    def setup_ui(self):
        """Setup the main window UI"""
        self.setWindowTitle("Bank Statement Analyzer - Clean Version")
        self.setGeometry(100, 100, 1000, 700)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create statement analyzers section
        self.create_statement_analyzers_section(main_layout)
        
        # Create status bar
        self.create_status_bar()
    
    def create_menu_bar(self):
        """Create clean menu bar with only core functionality"""
        menubar = self.menuBar()
        
        # Machine Learning menu
        ml_menu = menubar.addMenu("Machine Learning")
        
        # Transaction Labelling submenu
        labeling_menu = ml_menu.addMenu("Transaction Labelling")
        
        manual_labeling_action = QAction("Manual Labelling with AI Assistant", self)
        manual_labeling_action.triggered.connect(self.open_manual_labeling)
        labeling_menu.addAction(manual_labeling_action)
        
        # AI Processing submenu
        ai_processing_menu = ml_menu.addMenu("AI Processing")
        
        hybrid_categorization_action = QAction("Hybrid Categorisation System", self)
        hybrid_categorization_action.triggered.connect(self.open_hybrid_categorization)
        ai_processing_menu.addAction(hybrid_categorization_action)
        
        # Model Management submenu
        model_menu = ml_menu.addMenu("Model Management")
        
        train_action = QAction("Train Model", self)
        train_action.triggered.connect(self.open_model_training)
        model_menu.addAction(train_action)
    
    def create_statement_analyzers_section(self, layout):
        """Create the main Statement Analyzers section"""
        # Main title
        title_label = QLabel("Statement Analyzers")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Create parser selection group
        parser_group = QGroupBox("Bank Statement Parsers")
        parser_layout = QVBoxLayout(parser_group)
        
        # Indian Bank section
        indian_bank_frame = QFrame()
        indian_bank_frame.setFrameStyle(QFrame.Box)
        indian_bank_layout = QVBoxLayout(indian_bank_frame)
        
        indian_bank_label = QLabel("Indian Bank")
        indian_bank_label.setFont(QFont("Arial", 12, QFont.Bold))
        indian_bank_layout.addWidget(indian_bank_label)
        
        indian_bank_buttons = QHBoxLayout()
        self.indian_bank_yearly_btn = QPushButton("Yearly Statements")
        self.indian_bank_monthly_btn = QPushButton("Monthly Statements")
        self.indian_bank_yearly_btn.clicked.connect(lambda: self.select_parser("Indian Bank", "yearly"))
        self.indian_bank_monthly_btn.clicked.connect(lambda: self.select_parser("Indian Bank", "monthly"))
        indian_bank_buttons.addWidget(self.indian_bank_yearly_btn)
        indian_bank_buttons.addWidget(self.indian_bank_monthly_btn)
        indian_bank_layout.addLayout(indian_bank_buttons)
        
        parser_layout.addWidget(indian_bank_frame)
        
        # State Bank section
        state_bank_frame = QFrame()
        state_bank_frame.setFrameStyle(QFrame.Box)
        state_bank_layout = QVBoxLayout(state_bank_frame)
        
        state_bank_label = QLabel("State Bank of India")
        state_bank_label.setFont(QFont("Arial", 12, QFont.Bold))
        state_bank_layout.addWidget(state_bank_label)
        
        state_bank_buttons = QHBoxLayout()
        self.state_bank_yearly_btn = QPushButton("Yearly Statements")
        self.state_bank_monthly_btn = QPushButton("Monthly Statements")
        self.state_bank_yearly_btn.clicked.connect(lambda: self.select_parser("State Bank of India", "yearly"))
        self.state_bank_monthly_btn.clicked.connect(lambda: self.select_parser("State Bank of India", "monthly"))
        state_bank_buttons.addWidget(self.state_bank_yearly_btn)
        state_bank_buttons.addWidget(self.state_bank_monthly_btn)
        state_bank_layout.addLayout(state_bank_buttons)
        
        parser_layout.addWidget(state_bank_frame)
        layout.addWidget(parser_group)
        
        # File processing section
        processing_group = QGroupBox("File Processing")
        processing_layout = QVBoxLayout(processing_group)
        
        # Selected parser display
        self.selected_parser_label = QLabel("No parser selected")
        self.selected_parser_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        processing_layout.addWidget(self.selected_parser_label)
        
        # File selection and processing options
        file_buttons = QHBoxLayout()
        self.open_statement_btn = QPushButton("Open Statement")
        self.open_statement_btn.clicked.connect(self.open_statement_files)
        self.open_statement_btn.setEnabled(False)
        file_buttons.addWidget(self.open_statement_btn)

        self.process_files_btn = QPushButton("Process Files")
        self.process_files_btn.clicked.connect(self.process_files)
        self.process_files_btn.setEnabled(False)
        file_buttons.addWidget(self.process_files_btn)

        processing_layout.addLayout(file_buttons)

        # Existing data options
        existing_data_group = QGroupBox("Existing Processed Data")
        existing_data_layout = QVBoxLayout(existing_data_group)

        # Status label for existing data
        self.existing_data_label = QLabel("Checking for existing processed data...")
        self.existing_data_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        existing_data_layout.addWidget(self.existing_data_label)

        # Load existing data buttons
        existing_buttons_layout = QHBoxLayout()

        self.load_existing_btn = QPushButton("Load Latest Processed Data")
        self.load_existing_btn.clicked.connect(self.load_existing_processed_data)
        self.load_existing_btn.setEnabled(False)
        existing_buttons_layout.addWidget(self.load_existing_btn)

        self.select_existing_btn = QPushButton("Select Processed File...")
        self.select_existing_btn.clicked.connect(self.select_existing_processed_data)
        self.select_existing_btn.setEnabled(True)
        existing_buttons_layout.addWidget(self.select_existing_btn)

        existing_data_layout.addLayout(existing_buttons_layout)

        processing_layout.addWidget(existing_data_group)
        
        # Selected files display
        self.selected_files_text = QTextEdit()
        self.selected_files_text.setMaximumHeight(100)
        self.selected_files_text.setPlaceholderText("No files selected")
        processing_layout.addWidget(self.selected_files_text)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        processing_layout.addWidget(self.progress_bar)
        
        # Progress label
        self.progress_label = QLabel()
        self.progress_label.setVisible(False)
        processing_layout.addWidget(self.progress_label)
        
        layout.addWidget(processing_group)
        layout.addStretch()
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def select_parser(self, bank_name: str, period_type: str):
        """Select a parser for processing"""
        self.selected_bank = bank_name
        self.selected_period = period_type
        self.selected_parser_label.setText(f"Selected: {bank_name} - {period_type.title()}")
        self.open_statement_btn.setEnabled(True)
        self.logger.info(f"Selected parser: {bank_name} - {period_type}")
    
    def open_statement_files(self):
        """Open statement files for processing"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("Bank Statements (*.pdf *.csv *.xlsx *.xls);;All Files (*)")
        
        if file_dialog.exec():
            self.selected_file_paths = [Path(path) for path in file_dialog.selectedFiles()]
            self.update_selected_files_display()
            self.process_files_btn.setEnabled(len(self.selected_file_paths) > 0)
    
    def update_selected_files_display(self):
        """Update the selected files display"""
        if not self.selected_file_paths:
            self.selected_files_text.setPlainText("No files selected")
        else:
            file_info = []
            for file_path in self.selected_file_paths:
                try:
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    file_info.append(f"{file_path.name} ({size_mb:.1f} MB)")
                except Exception:
                    file_info.append(f"{file_path.name} (size unknown)")
            self.selected_files_text.setPlainText("\n".join(file_info))

    def process_files(self):
        """Process selected files"""
        if not self.selected_file_paths:
            QMessageBox.warning(self, "No Files", "Please select files to process.")
            return

        if not hasattr(self, 'selected_bank'):
            QMessageBox.warning(self, "No Parser", "Please select a parser first.")
            return

        # Start processing in background thread
        self.processing_thread = StatementProcessingThread(self.selected_file_paths, self.selected_bank, self.selected_period)
        self.processing_thread.progress_updated.connect(self.on_processing_progress)
        self.processing_thread.processing_completed.connect(self.on_processing_completed)
        self.processing_thread.error_occurred.connect(self.on_processing_error)

        # Update UI
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        self.process_files_btn.setEnabled(False)
        self.status_bar.showMessage("Processing files...")

        # Start thread
        self.processing_thread.start()

    def on_processing_progress(self, percentage: int, message: str):
        """Handle processing progress updates"""
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(message)
        self.status_bar.showMessage(message)

    def on_processing_completed(self, raw_transactions: List[RawTransaction]):
        """Handle processing completion"""
        self.raw_transactions = raw_transactions

        # Update UI
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        self.process_files_btn.setEnabled(True)

        # Save to CSV automatically
        self.save_to_csv(raw_transactions)

        # Refresh existing data check after processing
        self.check_existing_processed_data()

        # Show completion message
        QMessageBox.information(
            self, "Processing Complete",
            f"Successfully processed {len(raw_transactions)} transactions.\n"
            f"Data saved to CSV file: {self.csv_file_path}"
        )

        self.status_bar.showMessage(f"Processed {len(raw_transactions)} transactions")

    def on_processing_error(self, error_message: str):
        """Handle processing errors"""
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        self.process_files_btn.setEnabled(True)

        self.status_bar.showMessage("Processing failed")
        QMessageBox.critical(self, "Processing Error", f"Failed to process files:\n\n{error_message}")

    def check_existing_processed_data(self):
        """Check for existing processed data and update UI accordingly"""
        try:
            dir_manager = get_directory_manager()

            # Check multiple directories for processed data
            search_dirs = [
                'processed_statements',
                'categorized_data/manually_categorized',
                'categorized_data/ml_categorized',
                'categorized_data/ai_categorized',
                'categorized_data/hybrid_categorized'
            ]

            all_processed_files = []

            # Collect all CSV files from different directories
            for dir_name in search_dirs:
                try:
                    search_dir = dir_manager.get_directory(dir_name)
                    if search_dir.exists():
                        csv_files = list(search_dir.glob("*.csv"))
                        all_processed_files.extend(csv_files)
                except Exception:
                    continue

            if all_processed_files:
                # Sort by modification time to get the most recent
                latest_file = max(all_processed_files, key=lambda f: f.stat().st_mtime)
                file_age = datetime.now() - datetime.fromtimestamp(latest_file.stat().st_mtime)

                # Count transactions in the latest file
                try:
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        import csv
                        reader = csv.DictReader(f)
                        transaction_count = sum(1 for _ in reader)
                except Exception:
                    transaction_count = 0

                # Update UI
                if file_age.days == 0:
                    age_text = f"{int(file_age.total_seconds() // 3600)} hours ago"
                else:
                    age_text = f"{file_age.days} days ago"

                total_files = len(all_processed_files)
                self.existing_data_label.setText(
                    f"Latest: {latest_file.name}\n"
                    f"Contains {transaction_count} transactions\n"
                    f"Last modified: {age_text}\n"
                    f"Total files available: {total_files}"
                )
                self.existing_data_label.setStyleSheet("color: #27ae60; font-weight: bold;")
                self.load_existing_btn.setEnabled(True)

                # Store the latest file path for loading
                self.latest_processed_file = latest_file

                self.logger.info(f"Found {total_files} processed files across all directories, latest: {latest_file} with {transaction_count} transactions")
            else:
                self.existing_data_label.setText("No existing processed data found.\nProcess new statement files to get started.")
                self.existing_data_label.setStyleSheet("color: #e74c3c; font-style: italic;")
                self.load_existing_btn.setEnabled(False)
                self.latest_processed_file = None

        except Exception as e:
            self.logger.error(f"Error checking existing processed data: {str(e)}")
            self.existing_data_label.setText("Error checking for existing data.")
            self.existing_data_label.setStyleSheet("color: #e74c3c;")
            self.load_existing_btn.setEnabled(False)

    def load_existing_processed_data(self):
        """Load the latest existing processed data"""
        try:
            if not hasattr(self, 'latest_processed_file') or not self.latest_processed_file:
                QMessageBox.warning(self, "No Data", "No processed data file found.")
                return

            self._load_processed_file(self.latest_processed_file)

        except Exception as e:
            self.logger.error(f"Error loading existing processed data: {str(e)}")
            QMessageBox.critical(self, "Load Error", f"Failed to load existing processed data:\n{str(e)}")

    def select_existing_processed_data(self):
        """Allow user to select from all available processed data files"""
        try:
            dir_manager = get_directory_manager()

            # Check multiple directories for processed data
            search_dirs = [
                ('processed_statements', 'Processed Statements'),
                ('categorized_data', 'Categorized Data'),
                ('categorized_data/manually_categorized', 'Manually Categorized'),
                ('categorized_data/ml_categorized', 'ML Categorized'),
                ('categorized_data/ai_categorized', 'AI Categorized'),
                ('categorized_data/hybrid_categorized', 'Hybrid Categorized')
            ]

            all_csv_files = []

            # Collect all CSV files from different directories
            for dir_name, display_name in search_dirs:
                try:
                    search_dir = dir_manager.get_directory(dir_name)
                    if search_dir.exists():
                        csv_files = list(search_dir.glob("*.csv"))
                        for csv_file in csv_files:
                            all_csv_files.append((csv_file, display_name))
                except Exception:
                    continue

            if not all_csv_files:
                QMessageBox.information(
                    self, "No Files Found",
                    "No processed data files found.\n\n"
                    "Process some bank statements first to create processed data files."
                )
                return

            # Open file dialog to select processed file
            file_dialog = QFileDialog(self)
            file_dialog.setWindowTitle("Select Processed Data File")

            # Set initial directory to processed_statements
            processed_dir = dir_manager.get_directory('processed_statements')
            if processed_dir.exists():
                file_dialog.setDirectory(str(processed_dir))

            file_dialog.setNameFilter("CSV files (*.csv);;All files (*.*)")
            file_dialog.setFileMode(QFileDialog.ExistingFile)

            if file_dialog.exec():
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    selected_file = Path(selected_files[0])
                    self._load_processed_file(selected_file)

        except Exception as e:
            self.logger.error(f"Error selecting existing processed data: {str(e)}")
            QMessageBox.critical(self, "Selection Error", f"Failed to select processed data file:\n{str(e)}")

    def _load_processed_file(self, file_path: Path):
        """Load a specific processed data file"""
        try:
            # Set the CSV file path to the selected file
            self.csv_file_path = file_path

            # Load the transactions for internal use
            loaded_transactions = self._load_processed_transactions()
            self.raw_transactions = wrap_transactions(loaded_transactions)

            # Show success message
            QMessageBox.information(
                self, "Data Loaded",
                f"Successfully loaded {len(self.raw_transactions)} transactions from processed data.\n\n"
                f"File: {file_path.name}\n"
                f"Modified: {datetime.fromtimestamp(file_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"You can now use the ML menu options for categorization and analysis."
            )

            self.status_bar.showMessage(f"Loaded {len(self.raw_transactions)} transactions from {file_path.name}")
            self.logger.info(f"Loaded processed data from: {file_path}")

        except Exception as e:
            self.logger.error(f"Error loading processed file {file_path}: {str(e)}")
            QMessageBox.critical(self, "Load Error", f"Failed to load processed data file:\n{str(e)}")

    def save_to_csv(self, transactions: List[RawTransaction]):
        """Save transactions to CSV file using organized directory structure"""
        try:
            # Get directory manager and processed statements file path
            dir_manager = get_directory_manager()
            bank_name = self.selected_bank if self.selected_bank else "Unknown"
            self.csv_file_path = dir_manager.get_processed_statements_file(bank_name)

            # Write to CSV
            with open(self.csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['date', 'description', 'amount', 'transaction_type', 'bank_name', 'source_file', 'category', 'subcategory']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for txn in transactions:
                    # For debit transactions, use negative amounts; for credit transactions, use positive amounts
                    amount_value = str(txn.amount) if txn.amount else ''

                    writer.writerow({
                        'date': txn.date.strftime('%Y-%m-%d') if txn.date else '',
                        'description': txn.description or '',
                        'amount': amount_value,
                        'transaction_type': txn.transaction_type or '',
                        'bank_name': txn.bank_name or '',
                        'source_file': txn.source_file or '',
                        'category': '',  # Empty for manual labeling
                        'subcategory': ''  # Empty for manual labeling
                    })

            self.logger.info(f"Saved {len(transactions)} transactions to {self.csv_file_path}")

        except Exception as e:
            self.logger.error(f"Error saving to CSV: {str(e)}")
            QMessageBox.critical(self, "Save Error", f"Failed to save CSV file:\n{str(e)}")

    def open_manual_labeling(self):
        """Open manual labeling interface"""
        try:
            from .manual_labeling_window import ManualLabelingWindow
            if self.ml_labeling_window is None:
                self.ml_labeling_window = ManualLabelingWindow(self)
            self.ml_labeling_window.show()
            self.ml_labeling_window.raise_()
        except Exception as e:
            self.logger.error(f"Error opening manual labeling window: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open manual labeling window:\n{str(e)}")

    def open_hybrid_categorization(self):
        """Open hybrid categorization system with processed transaction data"""
        try:
            # Check if we have processed transaction data
            if not hasattr(self, 'csv_file_path') or not self.csv_file_path or not self.csv_file_path.exists():
                QMessageBox.warning(
                    self, "No Data",
                    "Please process bank statements first before opening the hybrid categorization system."
                )
                return

            # Load processed transactions from CSV
            processed_transactions = self._load_processed_transactions()
            if not processed_transactions:
                QMessageBox.warning(
                    self, "No Data",
                    "No processed transaction data found. Please process bank statements first."
                )
                return

            # Wrap transactions to ensure compatibility with hybrid categorization system
            wrapped_transactions = wrap_transactions(processed_transactions)

            # Use the Qt-based hybrid categorization system that accepts direct data
            from .hybrid_categorization_qt import HybridCategorizationDialog
            if not hasattr(self, 'hybrid_categorization_dialog') or self.hybrid_categorization_dialog is None:
                self.hybrid_categorization_dialog = HybridCategorizationDialog(self)

            # Pass wrapped transaction data to the hybrid categorization system
            self.hybrid_categorization_dialog.set_transactions(wrapped_transactions)
            self.hybrid_categorization_dialog.show()
            self.hybrid_categorization_dialog.raise_()

        except Exception as e:
            self.logger.error(f"Error opening hybrid categorization system: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open hybrid categorization system:\n{str(e)}")

    def _load_processed_transactions(self) -> List[Dict[str, Any]]:
        """Load processed transactions from CSV file"""
        try:
            transactions = []
            with open(self.csv_file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    transaction = {
                        'date': row.get('date', ''),
                        'description': row.get('description', ''),
                        'amount': float(row.get('amount', 0)) if row.get('amount') else 0.0,
                        'transaction_type': row.get('transaction_type', ''),
                        'bank_name': row.get('bank_name', ''),
                        'source_file': row.get('source_file', ''),
                        'category': row.get('category', ''),
                        'subcategory': row.get('subcategory', '')
                    }
                    transactions.append(transaction)

            self.logger.info(f"Loaded {len(transactions)} processed transactions for categorization")
            return transactions

        except Exception as e:
            self.logger.error(f"Error loading processed transactions: {str(e)}")
            return []

    def on_hybrid_categorization_completed(self, processed_transactions):
        """Handle completion of hybrid categorization process"""
        try:
            self.logger.info(f"Received {len(processed_transactions)} categorized transactions from hybrid system")

            # Save the categorized transactions to the exports directory for final use
            dir_manager = get_directory_manager()
            export_file_path = dir_manager.get_export_file('final_categorized_transactions')

            # Save to CSV for external use
            import csv
            with open(export_file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'date', 'description', 'amount', 'transaction_type', 'bank_name',
                    'category', 'sub_category', 'confidence_score', 'is_manually_reviewed',
                    'transaction_mode', 'notes'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for txn in processed_transactions:
                    writer.writerow({
                        'date': txn.date.strftime('%Y-%m-%d') if hasattr(txn, 'date') and txn.date else '',
                        'description': getattr(txn, 'original_description', '') or getattr(txn, 'notes', ''),
                        'amount': str(getattr(txn, 'amount', 0)),
                        'transaction_type': getattr(txn, 'type', ''),
                        'bank_name': getattr(txn, 'bank_name', self.selected_bank or ''),
                        'category': getattr(txn, 'category', ''),
                        'sub_category': getattr(txn, 'sub_category', ''),
                        'confidence_score': getattr(txn, 'confidence_score', 0.0),
                        'is_manually_reviewed': getattr(txn, 'is_manually_reviewed', False),
                        'transaction_mode': getattr(txn, 'transaction_mode', ''),
                        'notes': getattr(txn, 'notes', '')
                    })

            # Show success message
            QMessageBox.information(
                self, "Categorization Complete",
                f"Successfully processed {len(processed_transactions)} transactions through hybrid categorization.\n\n"
                f"Final categorized data saved to:\n{export_file_path}"
            )

            self.logger.info(f"Final categorized transactions saved to {export_file_path}")

        except Exception as e:
            self.logger.error(f"Error handling hybrid categorization completion: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to save final categorized data:\n{str(e)}")

    def open_model_training(self):
        """Open model training interface"""
        try:
            from .model_training_window import ModelTrainingWindow
            if self.ml_training_window is None:
                self.ml_training_window = ModelTrainingWindow(self)
            self.ml_training_window.show()
            self.ml_training_window.raise_()
        except Exception as e:
            self.logger.error(f"Error opening model training window: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open model training window:\n{str(e)}")
