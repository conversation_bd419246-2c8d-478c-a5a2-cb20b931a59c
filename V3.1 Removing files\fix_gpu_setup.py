#!/usr/bin/env python3
"""
GPU Setup Fixer
Fix CUDA/GPU setup for RTX 4050 and install proper PyTorch with CUDA support
"""

import sys
import subprocess
import platform
from pathlib import Path

def check_current_pytorch():
    """Check current PyTorch installation"""
    print("🔍 CHECKING CURRENT PYTORCH INSTALLATION")
    print("=" * 50)
    
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        print(f"🔧 CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"🚀 CUDA version: {torch.version.cuda}")
            print(f"💻 GPU device: {torch.cuda.get_device_name(0)}")
            print(f"📊 GPU count: {torch.cuda.device_count()}")
            return True
        else:
            print("❌ CUDA not available - CPU-only PyTorch detected")
            return False
            
    except ImportError:
        print("❌ PyTorch not installed")
        return False

def check_system_cuda():
    """Check if CUDA is installed on system"""
    print("\n🔍 CHECKING SYSTEM CUDA")
    print("=" * 30)
    
    # Check nvidia-smi
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA driver detected")
            # Extract CUDA version from nvidia-smi output
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version:' in line:
                    cuda_version = line.split('CUDA Version:')[1].strip().split()[0]
                    print(f"🚀 System CUDA version: {cuda_version}")
                    return cuda_version
        else:
            print("❌ nvidia-smi failed")
            return None
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ nvidia-smi not found")
        return None

def get_pytorch_cuda_command():
    """Get the correct PyTorch installation command for CUDA"""
    print("\n💡 PYTORCH CUDA INSTALLATION")
    print("=" * 35)
    
    # For RTX 4050, we need CUDA 11.8 or 12.1
    # Most compatible is CUDA 11.8
    cuda_version = "cu118"  # CUDA 11.8
    
    install_command = f"pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/{cuda_version}"
    
    print("🔧 Recommended installation command:")
    print(f"   {install_command}")
    
    return install_command

def install_cuda_pytorch():
    """Install PyTorch with CUDA support"""
    print("\n🚀 INSTALLING PYTORCH WITH CUDA")
    print("=" * 40)
    
    # Uninstall existing PyTorch first
    print("🗑️ Uninstalling existing PyTorch...")
    uninstall_commands = [
        "pip uninstall torch torchvision torchaudio -y",
    ]
    
    for cmd in uninstall_commands:
        print(f"   Running: {cmd}")
        try:
            result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print("   ✅ Success")
            else:
                print(f"   ⚠️ Warning: {result.stderr}")
        except subprocess.TimeoutExpired:
            print("   ⚠️ Command timed out")
    
    # Install CUDA PyTorch
    install_cmd = get_pytorch_cuda_command()
    print(f"\n📦 Installing CUDA PyTorch...")
    print(f"   Running: {install_cmd}")
    
    try:
        result = subprocess.run(install_cmd.split(), capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("   ✅ CUDA PyTorch installation completed!")
            return True
        else:
            print(f"   ❌ Installation failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("   ❌ Installation timed out")
        return False

def verify_gpu_setup():
    """Verify GPU setup after installation"""
    print("\n🧪 VERIFYING GPU SETUP")
    print("=" * 30)
    
    try:
        # Re-import torch after installation
        import importlib
        if 'torch' in sys.modules:
            importlib.reload(sys.modules['torch'])
        
        import torch
        
        print(f"✅ PyTorch version: {torch.__version__}")
        print(f"🔧 CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"🚀 CUDA version: {torch.version.cuda}")
            print(f"💻 GPU device: {torch.cuda.get_device_name(0)}")
            
            # Test GPU tensor operations
            print("🧪 Testing GPU operations...")
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y)
            print("✅ GPU tensor operations working!")
            
            return True
        else:
            print("❌ CUDA still not available")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying setup: {e}")
        return False

def show_manual_instructions():
    """Show manual installation instructions"""
    print("\n📋 MANUAL INSTALLATION INSTRUCTIONS")
    print("=" * 45)
    
    print("If automatic installation fails, follow these steps:")
    print()
    print("1. 🗑️ Uninstall existing PyTorch:")
    print("   pip uninstall torch torchvision torchaudio -y")
    print()
    print("2. 🚀 Install CUDA PyTorch (for RTX 4050):")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    print()
    print("3. 🧪 Verify installation:")
    print("   python -c \"import torch; print(f'CUDA available: {torch.cuda.is_available()}')\"")
    print()
    print("4. 🔄 Restart your Python environment/IDE")
    print()
    print("💡 Alternative CUDA versions:")
    print("   - CUDA 12.1: --index-url https://download.pytorch.org/whl/cu121")
    print("   - CUDA 11.8: --index-url https://download.pytorch.org/whl/cu118")

def main():
    """Main GPU setup fixer"""
    print("🛠️ GPU SETUP FIXER FOR RTX 4050")
    print("=" * 50)
    
    # Check current setup
    pytorch_has_cuda = check_current_pytorch()
    system_cuda = check_system_cuda()
    
    if pytorch_has_cuda:
        print("\n🎉 GPU setup is already working!")
        print("✅ PyTorch has CUDA support")
        print("✅ GPU is accessible")
        return
    
    if system_cuda is None:
        print("\n❌ NVIDIA drivers not detected")
        print("💡 Please install NVIDIA drivers for RTX 4050 first:")
        print("   https://www.nvidia.com/drivers/")
        return
    
    print(f"\n🔧 System has CUDA but PyTorch doesn't support it")
    print("💡 Need to install PyTorch with CUDA support")
    
    # Ask user for permission
    response = input("\n❓ Install PyTorch with CUDA support? (y/n): ").lower().strip()
    
    if response == 'y' or response == 'yes':
        success = install_cuda_pytorch()
        
        if success:
            print("\n🔄 Please restart Python and run this script again to verify")
        else:
            show_manual_instructions()
    else:
        show_manual_instructions()

if __name__ == "__main__":
    main()
