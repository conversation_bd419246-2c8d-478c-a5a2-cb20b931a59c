"""
AI Progress Dialog for Bank Statement Analyzer
Shows detailed progress during AI categorization with real-time statistics
"""

import sys
from typing import Optional, Dict, Any
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar, 
    QPushButton, QTextEdit, QGridLayout, QFrame, QWidget
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor
import time


class AIProgressDialog(QDialog):
    """
    Detailed progress dialog for AI categorization process
    Shows real-time statistics, progress, and activity log
    """
    
    cancel_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🤖 AI Categorization Progress")
        self.setModal(True)
        self.resize(800, 600)
        
        # Progress tracking
        self.start_time = time.time()
        self.current_step = ""
        self.overall_progress = 0
        self.transaction_progress = 0
        self.statistics = {
            'ai_categorized': 0,
            'cache_used': 0,
            'manual_required': 0,
            'estimated_cost': 0.0,
            'patterns_learned': 0,
            'processing_speed': 0.0
        }
        
        # Setup UI
        self.setup_ui()
        
        # Timer for elapsed time updates
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_elapsed_time)
        self.timer.start(1000)  # Update every second
        
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Header with AI icon and title
        header_layout = QHBoxLayout()
        
        # AI Icon (simple text for now)
        ai_icon = QLabel("🤖")
        ai_icon.setStyleSheet("font-size: 24px;")
        header_layout.addWidget(ai_icon)
        
        # Title and elapsed time
        title_layout = QVBoxLayout()
        self.title_label = QLabel("AI Categorization in Progress")
        self.title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        title_layout.addWidget(self.title_label)
        
        self.elapsed_label = QLabel("Elapsed: 00:00")
        self.elapsed_label.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        title_layout.addWidget(self.elapsed_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Progress section
        progress_frame = QFrame()
        progress_frame.setFrameStyle(QFrame.Box)
        progress_frame.setStyleSheet("QFrame { border: 1px solid #bdc3c7; border-radius: 5px; padding: 10px; }")
        progress_layout = QVBoxLayout(progress_frame)
        
        # Overall progress
        self.overall_label = QLabel("Overall Progress: 0%")
        self.overall_label.setStyleSheet("font-weight: bold; color: #2980b9;")
        progress_layout.addWidget(self.overall_label)
        
        self.overall_progress_bar = QProgressBar()
        self.overall_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        progress_layout.addWidget(self.overall_progress_bar)
        
        # Transaction progress
        self.transaction_label = QLabel("Transaction Progress: 0%")
        self.transaction_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        progress_layout.addWidget(self.transaction_label)
        
        self.transaction_progress_bar = QProgressBar()
        self.transaction_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #2ecc71;
                border-radius: 3px;
            }
        """)
        progress_layout.addWidget(self.transaction_progress_bar)
        
        # Current step
        self.step_label = QLabel("Initializing...")
        self.step_label.setStyleSheet("font-style: italic; color: #34495e; margin-top: 5px;")
        progress_layout.addWidget(self.step_label)
        
        layout.addWidget(progress_frame)
        
        # Statistics section
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Box)
        stats_frame.setStyleSheet("QFrame { border: 1px solid #bdc3c7; border-radius: 5px; padding: 10px; }")
        stats_layout = QGridLayout(stats_frame)
        
        stats_title = QLabel("📊 Real-time Statistics")
        stats_title.setStyleSheet("font-weight: bold; color: #2c3e50; margin-bottom: 5px;")
        stats_layout.addWidget(stats_title, 0, 0, 1, 2)
        
        # Statistics labels
        self.ai_categorized_label = QLabel("🤖 AI Categorized: 0")
        self.cache_used_label = QLabel("💾 Cache Used: 0")
        self.manual_required_label = QLabel("✋ Manual Required: 0")
        self.estimated_cost_label = QLabel("💰 Estimated Cost: $0.00")
        self.patterns_learned_label = QLabel("📚 Patterns Learned: 0")
        self.processing_speed_label = QLabel("⚡ Processing Speed: 0.0 txn/sec")
        
        # Add to grid
        stats_layout.addWidget(self.ai_categorized_label, 1, 0)
        stats_layout.addWidget(self.cache_used_label, 1, 1)
        stats_layout.addWidget(self.manual_required_label, 2, 0)
        stats_layout.addWidget(self.estimated_cost_label, 2, 1)
        stats_layout.addWidget(self.patterns_learned_label, 3, 0)
        stats_layout.addWidget(self.processing_speed_label, 3, 1)
        
        layout.addWidget(stats_frame)
        
        # Activity log
        log_label = QLabel("📝 Activity Log")
        log_label.setStyleSheet("font-weight: bold; color: #2c3e50; margin-top: 10px;")
        layout.addWidget(log_label)
        
        self.activity_log = QTextEdit()
        self.activity_log.setMaximumHeight(150)
        self.activity_log.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #f8f9fa;
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }
        """)
        layout.addWidget(self.activity_log)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.cancel_requested.emit)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        button_layout.addWidget(self.cancel_button)
        
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.accept)
        self.close_button.setEnabled(False)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:enabled {
                background-color: #27ae60;
            }
            QPushButton:enabled:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def update_progress(self, overall_progress: int, step: str, 
                       transaction_progress: int = 0, statistics: Optional[Dict[str, Any]] = None):
        """Update the progress dialog with new information"""
        self.overall_progress = overall_progress
        self.current_step = step
        self.transaction_progress = transaction_progress
        
        if statistics:
            self.statistics.update(statistics)
        
        # Update UI
        self.overall_progress_bar.setValue(overall_progress)
        self.overall_label.setText(f"Overall Progress: {overall_progress}%")
        
        self.transaction_progress_bar.setValue(transaction_progress)
        self.transaction_label.setText(f"Transaction Progress: {transaction_progress}%")
        
        self.step_label.setText(step)
        
        # Update statistics
        self.ai_categorized_label.setText(f"🤖 AI Categorized: {self.statistics['ai_categorized']}")
        self.cache_used_label.setText(f"💾 Cache Used: {self.statistics['cache_used']}")
        self.manual_required_label.setText(f"✋ Manual Required: {self.statistics['manual_required']}")
        self.estimated_cost_label.setText(f"💰 Estimated Cost: ${self.statistics['estimated_cost']:.2f}")
        self.patterns_learned_label.setText(f"📚 Patterns Learned: {self.statistics['patterns_learned']}")
        self.processing_speed_label.setText(f"⚡ Processing Speed: {self.statistics['processing_speed']:.1f} txn/sec")
        
        # Add to activity log
        timestamp = time.strftime("%H:%M:%S")
        self.activity_log.append(f"[{timestamp}] {step}")
        
        # Auto-scroll to bottom
        cursor = self.activity_log.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.activity_log.setTextCursor(cursor)
    
    def update_elapsed_time(self):
        """Update the elapsed time display"""
        elapsed = int(time.time() - self.start_time)
        minutes = elapsed // 60
        seconds = elapsed % 60
        self.elapsed_label.setText(f"Elapsed: {minutes:02d}:{seconds:02d}")
    
    def processing_completed(self, final_statistics: Optional[Dict[str, Any]] = None):
        """Mark processing as completed"""
        if final_statistics:
            self.statistics.update(final_statistics)
            self.update_progress(100, "✅ Processing completed successfully!", 100, final_statistics)
        else:
            self.update_progress(100, "✅ Processing completed successfully!", 100)
        
        # Enable close button, disable cancel
        self.close_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        
        # Update title
        self.title_label.setText("AI Categorization Completed")
        
        # Stop timer
        self.timer.stop()

    def start_processing(self, total_files: int):
        """Initialize processing with file count"""
        self.total_files = total_files
        self.update_progress(0, f"Starting AI processing for {total_files} file(s)...")

        # Reset statistics
        self.statistics = {
            'ai_categorized': 0,
            'cache_used': 0,
            'manual_required': 0,
            'estimated_cost': 0.0,
            'patterns_learned': 0,
            'processing_speed': 0.0
        }

        # Reset start time
        self.start_time = time.time()

    def update_step(self, step_message: str, percentage: int = None):
        """Update the current step with optional percentage"""
        if percentage is not None:
            self.update_progress(percentage, step_message)
        else:
            self.update_progress(self.overall_progress, step_message)

    def update_statistics(self, statistics: Dict[str, Any]):
        """Update the statistics display"""
        if statistics:
            self.statistics.update(statistics)

            # Update statistics labels
            self.ai_categorized_label.setText(f"🤖 AI Categorized: {self.statistics.get('ai_categorized', 0)}")
            self.cache_used_label.setText(f"💾 Cache Used: {self.statistics.get('cache_used', 0)}")
            self.manual_required_label.setText(f"✋ Manual Required: {self.statistics.get('manual_required', 0)}")
            self.estimated_cost_label.setText(f"💰 Estimated Cost: ${self.statistics.get('estimated_cost', 0.0):.2f}")
            self.patterns_learned_label.setText(f"📚 Patterns Learned: {self.statistics.get('patterns_learned', 0)}")
            self.processing_speed_label.setText(f"⚡ Processing Speed: {self.statistics.get('processing_speed', 0.0):.1f} txn/sec")

    def processing_cancelled(self):
        """Mark processing as cancelled"""
        self.update_progress(self.overall_progress, "❌ Processing cancelled by user")
        
        # Enable close button, disable cancel
        self.close_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        
        # Update title
        self.title_label.setText("AI Categorization Cancelled")
        
        # Stop timer
        self.timer.stop()
    
    def processing_error(self, error_message: str):
        """Mark processing as failed"""
        self.update_progress(self.overall_progress, f"❌ Error: {error_message}")
        
        # Enable close button, disable cancel
        self.close_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        
        # Update title
        self.title_label.setText("AI Categorization Failed")
        
        # Stop timer
        self.timer.stop()
