"""
Manual Labeling Window - Clean Implementation
Implements manual transaction labeling with explicit data loading only
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFileDialog, QTableWidget, QTableWidgetItem,
    QComboBox, QGroupBox, QSplitter, QTextEdit, QMessageBox,
    QHeaderView, QMenuBar, QStatusBar, QLineEdit, QDateEdit,
    QDoubleSpinBox, QCheckBox, QFrame, QGridLayout, QSpacerItem,
    QSizePolicy, QInputDialog, QDialog, QRadioButton, QDialogButtonBox,
    QTreeWidget, QTreeWidgetItem
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QAction, QFont, QIcon

import csv
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.logger import get_logger


class ManualLabelingWindow(QMainWindow):
    """
    Manual Labeling Window - Clean Implementation
    No automatic data loading - only explicit user-triggered loading
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # Data
        self.transactions = []
        self.filtered_transactions = []
        self.csv_file_path = None
        # Load categories from JSON file
        self.categories = self.load_categories_from_json()

        # Filtering and search state
        self.current_filter = "All Transactions"
        self.search_text = ""
        self.date_from = None
        self.date_to = None
        self.amount_min = None
        self.amount_max = None
        self.current_sort_column = 0
        self.current_sort_order = Qt.AscendingOrder

        # Search timer for real-time filtering
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.apply_all_filters)
        self.search_timer.setInterval(300)  # 300ms delay
        
        self.setup_ui()

        # Initialize category dropdowns with all available categories
        self.initialize_category_dropdowns()

        self.logger.info("Manual Labeling Window initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("Manual Labelling with AI Assistant")
        self.setGeometry(100, 100, 1400, 800)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout with minimal spacing
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        main_layout.setSpacing(5)  # Reduce spacing between widgets

        # Compact status label
        self.status_label = QLabel("No data loaded - Use Data → Load Data to select a CSV file")
        self.status_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 4px 8px; background-color: #ecf0f1; border-radius: 3px; font-size: 11px;")
        self.status_label.setMaximumHeight(25)  # Limit height
        main_layout.addWidget(self.status_label)
        
        # Create main content
        self.create_main_content(main_layout)
        
        # Create status bar
        self.create_status_bar()

    def load_categories_from_json(self):
        """Load categories from the ML data JSON file"""
        import json
        from pathlib import Path
        from collections import defaultdict

        json_file = Path("bank_analyzer_config/ml_data/ml_categories.json")

        if not json_file.exists():
            self.logger.warning(f"Categories JSON file not found: {json_file}")
            return {'credit': {}, 'debit': {}}

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Organize categories by parent-child relationship
            all_categories = {}

            # First pass: collect all categories
            for item in data:
                if not item.get('is_active', True):
                    continue

                category_id = item.get('id', '')
                name = item.get('name', '')
                parent_id = item.get('parent_id')
                category_type = item.get('category_type', 'expense')

                # Clean the name
                clean_name = self.clean_category_name(name)
                if not clean_name:
                    continue

                all_categories[category_id] = {
                    'name': clean_name,
                    'parent_id': parent_id,
                    'type': category_type,
                    'original_name': name
                }

            # Second pass: organize by parent-child
            root_categories = {}

            for cat_id, cat_info in all_categories.items():
                parent_id = cat_info['parent_id']

                if parent_id is None or parent_id not in all_categories:
                    # This is a root category
                    if cat_info['name'] not in root_categories:
                        root_categories[cat_info['name']] = []
                else:
                    # This is a subcategory
                    parent_name = all_categories[parent_id]['name']
                    if parent_name not in root_categories:
                        root_categories[parent_name] = []

                    if cat_info['name'] not in root_categories[parent_name]:
                        root_categories[parent_name].append(cat_info['name'])

            # Determine transaction types (credit/debit) based on category type
            credit_categories = {}
            debit_categories = {}

            for category, subcategories in root_categories.items():
                # Most categories are expenses (debit), but some might be income (credit)
                income_keywords = ['income', 'salary', 'investment', 'dividend', 'interest', 'refund']

                if any(keyword in category.lower() for keyword in income_keywords):
                    credit_categories[category] = subcategories
                else:
                    debit_categories[category] = subcategories

            self.logger.info(f"Loaded {len(credit_categories)} credit and {len(debit_categories)} debit categories from JSON")

            return {
                'credit': credit_categories,
                'debit': debit_categories
            }

        except Exception as e:
            self.logger.error(f"Error loading categories from JSON: {str(e)}")
            return {'credit': {}, 'debit': {}}

    def clean_category_name(self, name):
        """Clean category name by removing ml_ prefix and numbers"""
        if not name:
            return ""

        # Remove ml_ prefix if present
        if name.startswith('ml_'):
            name = name[3:]

        # Split by underscore and remove numeric parts
        parts = name.split('_')
        cleaned_parts = []

        for part in parts:
            # Skip if part is purely numeric
            if part.isdigit():
                continue
            # Skip if part contains only numbers and is at the end
            if len(part) > 6 and part.isdigit():
                continue
            cleaned_parts.append(part)

        # Join parts and capitalize properly
        result = ' '.join(cleaned_parts)
        return result.title() if result else name.title()

    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        
        # Data menu
        data_menu = menubar.addMenu("Data")
        
        load_data_action = QAction("Load Data", self)
        load_data_action.triggered.connect(self.load_data)
        data_menu.addAction(load_data_action)
        
        save_data_action = QAction("Save Data", self)
        save_data_action.triggered.connect(self.save_data)
        save_data_action.setEnabled(False)
        data_menu.addAction(save_data_action)
        self.save_data_action = save_data_action
    
    def create_main_content(self, layout):
        """Create main content area with enhanced filtering"""
        # Main splitter
        main_splitter = QSplitter(Qt.Horizontal)

        # Left panel - Filters and table
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Enhanced filter section - compact
        self.filter_group = QGroupBox("Filters")
        self.filter_group.setCheckable(True)
        self.filter_group.setChecked(True)
        self.filter_group.setStyleSheet("QGroupBox { font-size: 11px; font-weight: bold; }")
        filter_layout = QVBoxLayout(self.filter_group)
        filter_layout.setContentsMargins(8, 15, 8, 8)  # Reduce margins
        filter_layout.setSpacing(6)  # Reduce spacing

        # Enhanced text-based description filters
        desc_filter_frame = QFrame()
        desc_filter_layout = QGridLayout(desc_filter_frame)
        desc_filter_layout.setContentsMargins(0, 0, 0, 0)
        desc_filter_layout.setVerticalSpacing(4)
        desc_filter_layout.setHorizontalSpacing(6)

        # Description filter operator
        desc_op_label = QLabel("Description:")
        desc_op_label.setStyleSheet("font-size: 11px;")
        desc_filter_layout.addWidget(desc_op_label, 0, 0)

        self.desc_operator_combo = QComboBox()
        self.desc_operator_combo.addItems([
            "Contains", "Starts with", "Ends with", "Equals", "Does not contain"
        ])
        self.desc_operator_combo.setMaximumHeight(24)
        self.desc_operator_combo.setStyleSheet("font-size: 11px;")
        self.desc_operator_combo.currentTextChanged.connect(self.apply_all_filters)
        desc_filter_layout.addWidget(self.desc_operator_combo, 0, 1)

        # Description filter text input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter words to filter (e.g., 'upi food' or 'gpay insurance')...")
        self.search_input.setMaximumHeight(24)
        self.search_input.setStyleSheet("font-size: 11px;")
        self.search_input.textChanged.connect(self.on_search_changed)
        desc_filter_layout.addWidget(self.search_input, 0, 2)

        # Clear description filter button
        self.clear_desc_btn = QPushButton("✕")
        self.clear_desc_btn.clicked.connect(self.clear_description_filter)
        self.clear_desc_btn.setMaximumWidth(24)
        self.clear_desc_btn.setMaximumHeight(24)
        self.clear_desc_btn.setStyleSheet("font-size: 10px; font-weight: bold;")
        self.clear_desc_btn.setToolTip("Clear description filter")
        desc_filter_layout.addWidget(self.clear_desc_btn, 0, 3)

        filter_layout.addWidget(desc_filter_frame)

        # Compact filter controls in a grid layout
        filter_controls_frame = QFrame()
        filter_controls_layout = QGridLayout(filter_controls_frame)
        filter_controls_layout.setContentsMargins(0, 0, 0, 0)
        filter_controls_layout.setVerticalSpacing(4)  # Reduce vertical spacing
        filter_controls_layout.setHorizontalSpacing(8)  # Reduce horizontal spacing

        # Row 1: Split transaction type filters
        # Credit/Debit filter
        type_label = QLabel("Type:")
        type_label.setStyleSheet("font-size: 11px;")
        filter_controls_layout.addWidget(type_label, 0, 0)

        self.transaction_type_combo = QComboBox()
        self.transaction_type_combo.addItems([
            "All Types",
            "Credit Only",
            "Debit Only"
        ])
        self.transaction_type_combo.setMaximumHeight(24)
        self.transaction_type_combo.setStyleSheet("font-size: 11px;")
        self.transaction_type_combo.currentTextChanged.connect(self.on_transaction_type_changed)
        filter_controls_layout.addWidget(self.transaction_type_combo, 0, 1)

        # Category status filter
        status_label = QLabel("Status:")
        status_label.setStyleSheet("font-size: 11px;")
        filter_controls_layout.addWidget(status_label, 0, 2)

        self.category_status_combo = QComboBox()
        self.category_status_combo.addItems([
            "All Status",
            "Labeled",
            "Unlabeled",
            "Has Category",
            "Missing Category",
            "Has Subcategory",
            "Missing Subcategory"
        ])
        self.category_status_combo.setMaximumHeight(24)
        self.category_status_combo.setStyleSheet("font-size: 11px;")
        self.category_status_combo.currentTextChanged.connect(self.apply_all_filters)
        filter_controls_layout.addWidget(self.category_status_combo, 0, 3)

        # Row 2: Compact date range filters
        from_label = QLabel("From:")
        from_label.setStyleSheet("font-size: 11px;")
        filter_controls_layout.addWidget(from_label, 1, 0)

        self.date_from_input = QDateEdit()
        self.date_from_input.setCalendarPopup(True)
        self.date_from_input.setSpecialValueText("No start date")
        self.date_from_input.setDate(QDate(2000, 1, 1))
        self.date_from_input.clearMinimumDate()
        self.date_from_input.clear()
        self.date_from_input.setMaximumHeight(24)
        self.date_from_input.setStyleSheet("font-size: 11px;")
        self.date_from_input.dateChanged.connect(self.on_date_filter_changed)
        filter_controls_layout.addWidget(self.date_from_input, 1, 1)

        to_label = QLabel("To:")
        to_label.setStyleSheet("font-size: 11px;")
        filter_controls_layout.addWidget(to_label, 1, 2)

        self.date_to_input = QDateEdit()
        self.date_to_input.setCalendarPopup(True)
        self.date_to_input.setSpecialValueText("No end date")
        self.date_to_input.setDate(QDate.currentDate())
        self.date_to_input.clearMaximumDate()
        self.date_to_input.clear()
        self.date_to_input.setMaximumHeight(24)
        self.date_to_input.setStyleSheet("font-size: 11px;")
        self.date_to_input.dateChanged.connect(self.on_date_filter_changed)
        filter_controls_layout.addWidget(self.date_to_input, 1, 3)

        # Row 3: Category name filters
        cat_filter_label = QLabel("Category:")
        cat_filter_label.setStyleSheet("font-size: 11px;")
        filter_controls_layout.addWidget(cat_filter_label, 2, 0)

        self.category_filter_combo = QComboBox()
        self.category_filter_combo.addItem("All Categories")
        self.category_filter_combo.setMaximumHeight(24)
        self.category_filter_combo.setStyleSheet("font-size: 11px;")
        self.category_filter_combo.currentTextChanged.connect(self.on_category_filter_changed)
        filter_controls_layout.addWidget(self.category_filter_combo, 2, 1)

        subcat_filter_label = QLabel("Subcategory:")
        subcat_filter_label.setStyleSheet("font-size: 11px;")
        filter_controls_layout.addWidget(subcat_filter_label, 2, 2)

        self.subcategory_filter_combo = QComboBox()
        self.subcategory_filter_combo.addItem("All Subcategories")
        self.subcategory_filter_combo.setMaximumHeight(24)
        self.subcategory_filter_combo.setStyleSheet("font-size: 11px;")
        self.subcategory_filter_combo.currentTextChanged.connect(self.apply_all_filters)
        filter_controls_layout.addWidget(self.subcategory_filter_combo, 2, 3)

        filter_layout.addWidget(filter_controls_frame)

        # Compact date presets
        date_presets_frame = QFrame()
        date_presets_layout = QHBoxLayout(date_presets_frame)
        date_presets_layout.setContentsMargins(0, 0, 0, 0)
        date_presets_layout.setSpacing(4)

        self.preset_30_days_btn = QPushButton("30d")
        self.preset_30_days_btn.clicked.connect(lambda: self.set_date_preset(30))
        self.preset_30_days_btn.setMaximumHeight(22)
        self.preset_30_days_btn.setMaximumWidth(35)
        self.preset_30_days_btn.setStyleSheet("font-size: 10px;")
        self.preset_30_days_btn.setToolTip("Last 30 days")
        date_presets_layout.addWidget(self.preset_30_days_btn)

        self.preset_3_months_btn = QPushButton("3m")
        self.preset_3_months_btn.clicked.connect(lambda: self.set_date_preset(90))
        self.preset_3_months_btn.setMaximumHeight(22)
        self.preset_3_months_btn.setMaximumWidth(35)
        self.preset_3_months_btn.setStyleSheet("font-size: 10px;")
        self.preset_3_months_btn.setToolTip("Last 3 months")
        date_presets_layout.addWidget(self.preset_3_months_btn)

        self.preset_year_btn = QPushButton("1y")
        self.preset_year_btn.clicked.connect(self.set_date_preset_year)
        self.preset_year_btn.setMaximumHeight(22)
        self.preset_year_btn.setMaximumWidth(35)
        self.preset_year_btn.setStyleSheet("font-size: 10px;")
        self.preset_year_btn.setToolTip("This year")
        date_presets_layout.addWidget(self.preset_year_btn)

        date_presets_layout.addStretch()
        filter_layout.addWidget(date_presets_frame)

        # Enhanced amount filters with operators
        amount_frame = QFrame()
        amount_layout = QGridLayout(amount_frame)
        amount_layout.setContentsMargins(0, 0, 0, 0)
        amount_layout.setVerticalSpacing(4)
        amount_layout.setHorizontalSpacing(6)

        # Amount operator selection
        amount_op_label = QLabel("Amount:")
        amount_op_label.setStyleSheet("font-size: 11px;")
        amount_layout.addWidget(amount_op_label, 0, 0)

        self.amount_operator_combo = QComboBox()
        self.amount_operator_combo.addItems([
            "Between", "Equals", "Greater than", "Less than", "Not equal"
        ])
        self.amount_operator_combo.setMaximumHeight(24)
        self.amount_operator_combo.setStyleSheet("font-size: 11px;")
        self.amount_operator_combo.currentTextChanged.connect(self.on_amount_operator_changed)
        amount_layout.addWidget(self.amount_operator_combo, 0, 1)

        # Amount value inputs
        self.amount_min_input = QDoubleSpinBox()
        self.amount_min_input.setRange(-999999999, 999999999)
        self.amount_min_input.setDecimals(2)
        self.amount_min_input.setSpecialValueText("No min")
        self.amount_min_input.setValue(self.amount_min_input.minimum())
        self.amount_min_input.setMaximumHeight(24)
        self.amount_min_input.setStyleSheet("font-size: 11px;")
        self.amount_min_input.valueChanged.connect(self.on_amount_filter_changed)
        amount_layout.addWidget(self.amount_min_input, 0, 2)

        self.amount_to_label = QLabel("to")
        self.amount_to_label.setStyleSheet("font-size: 11px;")
        amount_layout.addWidget(self.amount_to_label, 0, 3)

        self.amount_max_input = QDoubleSpinBox()
        self.amount_max_input.setRange(-999999999, 999999999)
        self.amount_max_input.setDecimals(2)
        self.amount_max_input.setSpecialValueText("No max")
        self.amount_max_input.setValue(self.amount_max_input.maximum())
        self.amount_max_input.setMaximumHeight(24)
        self.amount_max_input.setStyleSheet("font-size: 11px;")
        self.amount_max_input.valueChanged.connect(self.on_amount_filter_changed)
        amount_layout.addWidget(self.amount_max_input, 0, 4)

        # Clear amount filter button
        self.clear_amount_btn = QPushButton("✕")
        self.clear_amount_btn.clicked.connect(self.clear_amount_filter)
        self.clear_amount_btn.setMaximumWidth(24)
        self.clear_amount_btn.setMaximumHeight(24)
        self.clear_amount_btn.setStyleSheet("font-size: 10px; font-weight: bold;")
        self.clear_amount_btn.setToolTip("Clear amount filter")
        amount_layout.addWidget(self.clear_amount_btn, 0, 5)

        filter_layout.addWidget(amount_frame)

        # Filter logic and presets section
        logic_presets_frame = QFrame()
        logic_presets_layout = QGridLayout(logic_presets_frame)
        logic_presets_layout.setContentsMargins(0, 0, 0, 0)
        logic_presets_layout.setVerticalSpacing(4)
        logic_presets_layout.setHorizontalSpacing(6)

        # Filter logic toggle
        logic_label = QLabel("Logic:")
        logic_label.setStyleSheet("font-size: 11px;")
        logic_presets_layout.addWidget(logic_label, 0, 0)

        self.filter_logic_combo = QComboBox()
        self.filter_logic_combo.addItems(["AND (All must match)", "OR (Any can match)"])
        self.filter_logic_combo.setMaximumHeight(24)
        self.filter_logic_combo.setStyleSheet("font-size: 11px;")
        self.filter_logic_combo.currentTextChanged.connect(self.apply_all_filters)
        logic_presets_layout.addWidget(self.filter_logic_combo, 0, 1)

        # Filter presets
        presets_label = QLabel("Presets:")
        presets_label.setStyleSheet("font-size: 11px;")
        logic_presets_layout.addWidget(presets_label, 0, 2)

        self.preset_upi_btn = QPushButton("UPI")
        self.preset_upi_btn.clicked.connect(self.apply_upi_preset)
        self.preset_upi_btn.setMaximumHeight(22)
        self.preset_upi_btn.setMaximumWidth(35)
        self.preset_upi_btn.setStyleSheet("font-size: 10px;")
        self.preset_upi_btn.setToolTip("UPI Transactions")
        logic_presets_layout.addWidget(self.preset_upi_btn, 0, 3)

        self.preset_large_btn = QPushButton("Large")
        self.preset_large_btn.clicked.connect(self.apply_large_amounts_preset)
        self.preset_large_btn.setMaximumHeight(22)
        self.preset_large_btn.setMaximumWidth(35)
        self.preset_large_btn.setStyleSheet("font-size: 10px;")
        self.preset_large_btn.setToolTip("Large Amounts (>1000)")
        logic_presets_layout.addWidget(self.preset_large_btn, 0, 4)

        self.preset_unlabeled_upi_btn = QPushButton("UPI-U")
        self.preset_unlabeled_upi_btn.clicked.connect(self.apply_unlabeled_upi_preset)
        self.preset_unlabeled_upi_btn.setMaximumHeight(22)
        self.preset_unlabeled_upi_btn.setMaximumWidth(40)
        self.preset_unlabeled_upi_btn.setStyleSheet("font-size: 10px;")
        self.preset_unlabeled_upi_btn.setToolTip("Unlabeled UPI")
        logic_presets_layout.addWidget(self.preset_unlabeled_upi_btn, 0, 5)

        filter_layout.addWidget(logic_presets_frame)

        # Enhanced filter action buttons
        filter_actions_frame = QFrame()
        filter_actions_layout = QHBoxLayout(filter_actions_frame)
        filter_actions_layout.setContentsMargins(0, 0, 0, 0)
        filter_actions_layout.setSpacing(8)

        self.clear_all_filters_btn = QPushButton("Clear All")
        self.clear_all_filters_btn.clicked.connect(self.clear_all_filters)
        self.clear_all_filters_btn.setMaximumHeight(24)
        self.clear_all_filters_btn.setMaximumWidth(70)
        self.clear_all_filters_btn.setStyleSheet("font-size: 10px;")
        filter_actions_layout.addWidget(self.clear_all_filters_btn)

        # Active filters indicator
        self.active_filters_label = QLabel("0 filters active")
        self.active_filters_label.setStyleSheet("color: #2196F3; font-weight: bold; font-size: 10px;")
        filter_actions_layout.addWidget(self.active_filters_label)

        # Enhanced results count label
        self.results_count_label = QLabel("No data loaded")
        self.results_count_label.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
        filter_actions_layout.addWidget(self.results_count_label)

        filter_actions_layout.addStretch()
        filter_layout.addWidget(filter_actions_frame)

        # Compact transaction table with enhanced sorting and manual selection
        self.transaction_table = QTableWidget()
        self.transaction_table.setColumnCount(7)
        self.transaction_table.setHorizontalHeaderLabels([
            "☐", "Date", "Description", "Amount", "Category", "Subcategory", "Type"
        ])

        # Set up compact table headers with drag-to-resize and sorting
        header = self.transaction_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setStretchLastSection(True)
        header.setSortIndicatorShown(True)
        header.sectionClicked.connect(self.on_header_clicked)
        header.setDefaultSectionSize(100)  # Compact default column width
        header.setMinimumSectionSize(60)   # Minimum column width
        header.setMaximumHeight(28)        # Compact header height

        # Enable sorting
        self.transaction_table.setSortingEnabled(True)

        # Fix row selection behavior - select entire row when clicking any cell
        self.transaction_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.transaction_table.setSelectionMode(QTableWidget.ExtendedSelection)  # Allow multiple selection

        # Connect selection change to update batch labeling info
        self.transaction_table.itemSelectionChanged.connect(self.on_selection_changed)

        # Make table rows more compact
        self.transaction_table.verticalHeader().setDefaultSectionSize(22)  # Compact row height
        self.transaction_table.verticalHeader().setVisible(False)  # Hide row numbers

        # Set compact font for table
        table_font = QFont()
        table_font.setPointSize(9)
        self.transaction_table.setFont(table_font)

        self.transaction_table.itemSelectionChanged.connect(self.on_transaction_selected)

        # Add filter group and table to left layout
        left_layout.addWidget(self.filter_group)
        left_layout.addWidget(self.transaction_table)

        main_splitter.addWidget(left_widget)

        # Compact right panel - Transaction details and labeling
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(8)

        # Compact transaction details
        details_group = QGroupBox("Transaction Details")
        details_group.setStyleSheet("QGroupBox { font-size: 11px; font-weight: bold; }")
        details_layout = QVBoxLayout(details_group)
        details_layout.setContentsMargins(8, 15, 8, 8)

        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(120)  # More compact
        self.details_text.setPlaceholderText("Select a transaction to view details")
        details_font = QFont()
        details_font.setPointSize(9)
        self.details_text.setFont(details_font)
        details_layout.addWidget(self.details_text)

        right_layout.addWidget(details_group)

        # Compact labeling section
        labeling_group = QGroupBox("Category & Subcategory")
        labeling_group.setStyleSheet("QGroupBox { font-size: 11px; font-weight: bold; }")
        labeling_layout = QVBoxLayout(labeling_group)
        labeling_layout.setContentsMargins(8, 15, 8, 8)
        labeling_layout.setSpacing(6)
        
        # Compact category dropdown
        cat_label = QLabel("Category:")
        cat_label.setStyleSheet("font-size: 11px;")
        labeling_layout.addWidget(cat_label)

        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)  # Allow typing in category
        self.category_combo.currentTextChanged.connect(self.on_category_changed)
        self.category_combo.setMaximumHeight(24)
        self.category_combo.setStyleSheet("font-size: 11px;")
        labeling_layout.addWidget(self.category_combo)

        # Compact subcategory dropdown
        subcat_label = QLabel("Subcategory:")
        subcat_label.setStyleSheet("font-size: 11px;")
        labeling_layout.addWidget(subcat_label)

        self.subcategory_combo = QComboBox()
        self.subcategory_combo.setEditable(True)  # Allow typing in subcategory
        self.subcategory_combo.setMaximumHeight(24)
        self.subcategory_combo.setStyleSheet("font-size: 11px;")
        labeling_layout.addWidget(self.subcategory_combo)

        # Compact apply button
        self.apply_label_btn = QPushButton("Apply Label")
        self.apply_label_btn.clicked.connect(self.apply_label)
        self.apply_label_btn.setEnabled(False)
        self.apply_label_btn.setMaximumHeight(28)
        self.apply_label_btn.setStyleSheet("font-size: 11px; font-weight: bold;")
        labeling_layout.addWidget(self.apply_label_btn)

        right_layout.addWidget(labeling_group)

        # Category Management section
        category_mgmt_group = QGroupBox("Category Management")
        category_mgmt_group.setStyleSheet("QGroupBox { font-size: 11px; font-weight: bold; }")
        category_mgmt_layout = QVBoxLayout(category_mgmt_group)
        category_mgmt_layout.setContentsMargins(8, 15, 8, 8)
        category_mgmt_layout.setSpacing(4)

        # Category management buttons in a grid
        mgmt_grid = QGridLayout()
        mgmt_grid.setSpacing(4)

        # Create category button
        self.create_category_btn = QPushButton("New Category")
        self.create_category_btn.clicked.connect(self.create_category)
        self.create_category_btn.setMaximumHeight(24)
        self.create_category_btn.setStyleSheet("font-size: 10px;")
        mgmt_grid.addWidget(self.create_category_btn, 0, 0)

        # Edit category button
        self.edit_category_btn = QPushButton("Edit Category")
        self.edit_category_btn.clicked.connect(self.edit_category)
        self.edit_category_btn.setMaximumHeight(24)
        self.edit_category_btn.setStyleSheet("font-size: 10px;")
        mgmt_grid.addWidget(self.edit_category_btn, 0, 1)

        # Create subcategory button
        self.create_subcategory_btn = QPushButton("New Subcategory")
        self.create_subcategory_btn.clicked.connect(self.create_subcategory)
        self.create_subcategory_btn.setMaximumHeight(24)
        self.create_subcategory_btn.setStyleSheet("font-size: 10px;")
        mgmt_grid.addWidget(self.create_subcategory_btn, 1, 0)

        # Edit subcategory button
        self.edit_subcategory_btn = QPushButton("Edit Subcategory")
        self.edit_subcategory_btn.clicked.connect(self.edit_subcategory)
        self.edit_subcategory_btn.setMaximumHeight(24)
        self.edit_subcategory_btn.setStyleSheet("font-size: 10px;")
        mgmt_grid.addWidget(self.edit_subcategory_btn, 1, 1)

        category_mgmt_layout.addLayout(mgmt_grid)

        # Organize categories button (full width)
        self.organize_categories_btn = QPushButton("Organize Categories")
        self.organize_categories_btn.clicked.connect(self.organize_categories)
        self.organize_categories_btn.setMaximumHeight(28)
        self.organize_categories_btn.setStyleSheet("font-size: 11px; font-weight: bold; background-color: #2196F3; color: white;")
        category_mgmt_layout.addWidget(self.organize_categories_btn)

        right_layout.addWidget(category_mgmt_group)

        # Batch labeling section
        batch_group = QGroupBox("Batch Labeling")
        batch_group.setStyleSheet("QGroupBox { font-size: 11px; font-weight: bold; }")
        batch_layout = QVBoxLayout(batch_group)
        batch_layout.setContentsMargins(8, 15, 8, 8)
        batch_layout.setSpacing(6)

        # Selection info
        self.selection_info_label = QLabel("No transactions selected")
        self.selection_info_label.setStyleSheet("font-size: 10px; color: #666;")
        batch_layout.addWidget(self.selection_info_label)

        # Batch apply button
        self.batch_apply_btn = QPushButton("Apply to Selected")
        self.batch_apply_btn.clicked.connect(self.apply_batch_label)
        self.batch_apply_btn.setEnabled(False)
        self.batch_apply_btn.setMaximumHeight(28)
        self.batch_apply_btn.setStyleSheet("font-size: 11px; font-weight: bold; background-color: #4CAF50; color: white;")
        batch_layout.addWidget(self.batch_apply_btn)

        # Select all filtered button
        self.select_all_btn = QPushButton("Select All Filtered")
        self.select_all_btn.clicked.connect(self.select_all_filtered)
        self.select_all_btn.setMaximumHeight(24)
        self.select_all_btn.setStyleSheet("font-size: 10px;")
        batch_layout.addWidget(self.select_all_btn)

        # Clear selection button
        self.clear_selection_btn = QPushButton("Clear Selection")
        self.clear_selection_btn.clicked.connect(self.clear_selection)
        self.clear_selection_btn.setMaximumHeight(24)
        self.clear_selection_btn.setStyleSheet("font-size: 10px;")
        batch_layout.addWidget(self.clear_selection_btn)

        right_layout.addWidget(batch_group)
        right_layout.addStretch()
        
        main_splitter.addWidget(right_widget)
        
        # Set splitter proportions
        main_splitter.setSizes([1000, 400])
        
        layout.addWidget(main_splitter)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Load data to begin labeling")
    
    def load_data(self):
        """Load data from CSV file"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setNameFilter("CSV Files (*.csv);;All Files (*)")
        
        if file_dialog.exec():
            file_path = Path(file_dialog.selectedFiles()[0])
            self.load_csv_file(file_path)
    
    def load_csv_file(self, file_path: Path):
        """Load transactions from CSV file"""
        try:
            self.transactions = []
            
            with open(file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    # Get amount and description
                    amount = float(row.get('amount', 0))
                    description = row.get('description', '').lower()

                    # Determine transaction type using Indian Bank logic
                    # Check if there's already a transaction_type or type field in the CSV
                    existing_type = row.get('transaction_type', row.get('type', '')).lower()
                    if existing_type in ['credit', 'debit']:
                        txn_type = existing_type
                        # Apply correct sign based on type
                        if txn_type == 'credit':
                            amount = abs(amount)  # Credit should be positive
                        else:  # debit
                            amount = -abs(amount)  # Debit should be negative
                    else:
                        # Enhanced description-based logic for Indian Bank transactions

                        # Credit transaction patterns (money coming in)
                        credit_patterns = [
                            'credit', 'by upi credit', 'deposit', 'salary', 'transfer from',
                            'received from', 'refund', 'interest', 'dividend', 'bonus',
                            'cashback', 'reversal', 'reimbursement', 'income', 'payment received',
                            'fund transfer cr', 'neft cr', 'rtgs cr', 'imps cr', 'upi cr',
                            'cash deposit', 'cheque deposit', 'online transfer cr'
                        ]

                        # Debit transaction patterns (money going out)
                        debit_patterns = [
                            'debit', 'thru upi debit', 'withdrawal', 'payment to', 'transfer to',
                            'sent to', 'purchase', 'bill payment', 'emi', 'loan payment',
                            'fund transfer dr', 'neft dr', 'rtgs dr', 'imps dr', 'upi dr',
                            'atm withdrawal', 'pos purchase', 'online purchase', 'charges',
                            'fees', 'penalty', 'tax'
                        ]

                        # Check for credit patterns first
                        if any(pattern in description for pattern in credit_patterns):
                            txn_type = 'credit'
                            amount = abs(amount)  # Credit should be positive
                        # Check for debit patterns
                        elif any(pattern in description for pattern in debit_patterns):
                            txn_type = 'debit'
                            amount = -abs(amount)  # Debit should be negative
                        # Special handling for UPI transactions
                        elif any(pattern in description for pattern in ['upi/', '/upi', 'pytm', 'paytm']):
                            # For UPI, check if it's explicitly a credit transaction
                            if any(credit_word in description for credit_word in ['from', 'received', 'refund', 'cashback']):
                                txn_type = 'credit'
                                amount = abs(amount)
                            else:
                                # Default UPI transactions are typically debits (payments)
                                txn_type = 'debit'
                                amount = -abs(amount)
                        else:
                            # Final fallback: use amount sign as indicator
                            # If amount is already negative in source data, it's likely a debit
                            # If positive, it's likely a credit
                            if amount < 0:
                                txn_type = 'debit'
                                amount = -abs(amount)  # Ensure negative for debit
                            else:
                                txn_type = 'credit'
                                amount = abs(amount)  # Ensure positive for credit
                    
                    transaction = {
                        'date': row.get('date', ''),
                        'description': row.get('description', ''),
                        'amount': amount,
                        'bank_name': row.get('bank_name', ''),
                        'source_file': row.get('source_file', ''),
                        'category': row.get('category', ''),
                        'subcategory': row.get('subcategory', ''),
                        'type': txn_type
                    }
                    self.transactions.append(transaction)
            
            self.csv_file_path = file_path

            # Initialize filtered transactions and apply filters
            self.filtered_transactions = self.transactions.copy()
            self.apply_all_filters()

            self.save_data_action.setEnabled(True)

            self.status_label.setText(f"Loaded {len(self.transactions)} transactions from {file_path.name}")
            self.status_bar.showMessage(f"Loaded {len(self.transactions)} transactions")
            
            self.logger.info(f"Loaded {len(self.transactions)} transactions from {file_path}")
            
        except Exception as e:
            self.logger.error(f"Error loading CSV file: {str(e)}")
            QMessageBox.critical(self, "Load Error", f"Failed to load CSV file:\n{str(e)}")
    
    def populate_table(self):
        """Populate the transaction table with filtered results"""
        filtered_transactions = self.get_filtered_transactions()

        # Temporarily disable sorting to avoid conflicts during population
        self.transaction_table.setSortingEnabled(False)

        self.transaction_table.setRowCount(len(filtered_transactions))

        for row, txn in enumerate(filtered_transactions):
            # Add checkbox for manual selection
            checkbox = QCheckBox()
            checkbox.stateChanged.connect(self.on_manual_selection_changed)
            self.transaction_table.setCellWidget(row, 0, checkbox)

            # Add transaction data (shifted by 1 column due to checkbox)
            self.transaction_table.setItem(row, 1, QTableWidgetItem(str(txn['date'])))
            self.transaction_table.setItem(row, 2, QTableWidgetItem(str(txn['description'])))
            self.transaction_table.setItem(row, 3, QTableWidgetItem(f"{txn['amount']:.2f}"))
            self.transaction_table.setItem(row, 4, QTableWidgetItem(str(txn['category'])))
            self.transaction_table.setItem(row, 5, QTableWidgetItem(str(txn['subcategory'])))
            self.transaction_table.setItem(row, 6, QTableWidgetItem(str(txn['type'])))

        # Re-enable sorting
        self.transaction_table.setSortingEnabled(True)

        # Update sort indicator
        header = self.transaction_table.horizontalHeader()
        header.setSortIndicator(self.current_sort_column, self.current_sort_order)
    

    
    def on_transaction_selected(self):
        """Handle transaction selection"""
        current_row = self.transaction_table.currentRow()
        if current_row >= 0:
            filtered_transactions = self.get_filtered_transactions()
            if current_row < len(filtered_transactions):
                txn = filtered_transactions[current_row]
                self.show_transaction_details(txn)
                self.setup_category_dropdowns(txn['type'])
                self.apply_label_btn.setEnabled(True)
    
    def show_transaction_details(self, txn: Dict[str, Any]):
        """Show transaction details in the details panel"""
        details = f"""Date: {txn['date']}
Description: {txn['description']}
Amount: {txn['amount']:.2f}
Type: {txn['type'].title()}
Bank: {txn['bank_name']}
Source File: {txn['source_file']}
Current Category: {txn['category']}
Current Subcategory: {txn['subcategory']}"""
        
        self.details_text.setPlainText(details)
    
    def setup_category_dropdowns(self, txn_type: str):
        """Setup category dropdowns based on transaction type"""
        self.category_combo.clear()
        self.subcategory_combo.clear()
        
        if txn_type in self.categories:
            categories = list(self.categories[txn_type].keys())
            self.category_combo.addItems(categories)

    def initialize_category_dropdowns(self):
        """Initialize category dropdowns with all available categories"""
        try:
            # Clear existing items for labeling dropdowns
            self.category_combo.clear()
            self.subcategory_combo.clear()

            # Clear existing items for filter dropdowns (keep "All" options)
            self.category_filter_combo.clear()
            self.category_filter_combo.addItem("All Categories")
            self.subcategory_filter_combo.clear()
            self.subcategory_filter_combo.addItem("All Subcategories")

            # Collect all unique categories from all transaction types
            all_categories = set()
            all_subcategories = set()

            for txn_type, categories in self.categories.items():
                for category, subcategories in categories.items():
                    all_categories.add(category)
                    all_subcategories.update(subcategories)

            # Populate labeling dropdowns with all categories
            if all_categories:
                sorted_categories = sorted(all_categories)
                self.category_combo.addItems(sorted_categories)
                self.category_filter_combo.addItems(sorted_categories)

            # Populate labeling dropdowns with all subcategories
            if all_subcategories:
                sorted_subcategories = sorted(all_subcategories)
                self.subcategory_combo.addItems(sorted_subcategories)
                self.subcategory_filter_combo.addItems(sorted_subcategories)

            self.logger.debug(f"Initialized dropdowns with {len(all_categories)} categories and {len(all_subcategories)} subcategories")

        except Exception as e:
            self.logger.error(f"Error initializing category dropdowns: {e}")

    def on_category_filter_changed(self, category_name):
        """Update subcategory filter when category filter changes"""
        try:
            # Clear subcategory filter
            self.subcategory_filter_combo.clear()
            self.subcategory_filter_combo.addItem("All Subcategories")

            if category_name and category_name != "All Categories":
                # Find subcategories for this category across all transaction types
                subcategories = set()
                for txn_type, categories in self.categories.items():
                    if category_name in categories:
                        subcategories.update(categories[category_name])

                if subcategories:
                    sorted_subcategories = sorted(subcategories)
                    self.subcategory_filter_combo.addItems(sorted_subcategories)
            else:
                # If "All Categories" is selected, show all subcategories
                all_subcategories = set()
                for txn_type, categories in self.categories.items():
                    for category, subcats in categories.items():
                        all_subcategories.update(subcats)

                if all_subcategories:
                    sorted_subcategories = sorted(all_subcategories)
                    self.subcategory_filter_combo.addItems(sorted_subcategories)

            # Apply filters after updating subcategories
            self.apply_all_filters()

        except Exception as e:
            self.logger.error(f"Error updating subcategory filter: {e}")

    def update_filter_categories_by_transaction_type(self, txn_type=None):
        """Update filter categories based on transaction type"""
        try:
            # Clear existing items
            self.category_filter_combo.clear()
            self.category_filter_combo.addItem("All Categories")

            if txn_type and txn_type in self.categories:
                # Show only categories for this transaction type
                categories = list(self.categories[txn_type].keys())
                if categories:
                    sorted_categories = sorted(categories)
                    self.category_filter_combo.addItems(sorted_categories)
            else:
                # Show all categories from all transaction types
                all_categories = set()
                for categories in self.categories.values():
                    all_categories.update(categories.keys())

                if all_categories:
                    sorted_categories = sorted(all_categories)
                    self.category_filter_combo.addItems(sorted_categories)

            # Reset subcategory filter
            self.on_category_filter_changed("All Categories")

        except Exception as e:
            self.logger.error(f"Error updating filter categories by transaction type: {e}")

    def on_transaction_type_changed(self, txn_type_text):
        """Handle transaction type filter changes"""
        try:
            # Map display text to internal transaction type
            txn_type = None
            if txn_type_text == "Credit Only":
                txn_type = "credit"
            elif txn_type_text == "Debit Only":
                txn_type = "debit"
            # "All Types" maps to None (show all)

            # Update category filters based on transaction type
            self.update_filter_categories_by_transaction_type(txn_type)

            # Apply filters
            self.apply_all_filters()

        except Exception as e:
            self.logger.error(f"Error handling transaction type change: {e}")
    
    def on_category_changed(self, category: str):
        """Handle category change"""
        self.subcategory_combo.clear()
        
        current_row = self.transaction_table.currentRow()
        if current_row >= 0:
            filtered_transactions = self.get_filtered_transactions()
            if current_row < len(filtered_transactions):
                txn = filtered_transactions[current_row]
                txn_type = txn['type']
                
                if txn_type in self.categories and category in self.categories[txn_type]:
                    subcategories = self.categories[txn_type][category]
                    self.subcategory_combo.addItems(subcategories)
    
    def apply_label(self):
        """Apply the selected label to the current transaction"""
        current_row = self.transaction_table.currentRow()
        if current_row >= 0:
            filtered_transactions = self.get_filtered_transactions()
            if current_row < len(filtered_transactions):
                txn = filtered_transactions[current_row]

                category = self.category_combo.currentText().strip()
                subcategory = self.subcategory_combo.currentText().strip()

                if not category:
                    QMessageBox.warning(self, "No Category", "Please enter or select a category.")
                    return

                # Get transaction type for proper category organization
                txn_type = txn.get('type', 'debit')

                # Auto-create category if it doesn't exist
                if txn_type not in self.categories:
                    self.categories[txn_type] = {}

                if category not in self.categories[txn_type]:
                    self.categories[txn_type][category] = []
                    self.save_categories_to_json()
                    self.status_bar.showMessage(f"Created new category: {category}")

                # Auto-create subcategory if it doesn't exist and is provided
                if subcategory and subcategory not in self.categories[txn_type][category]:
                    self.categories[txn_type][category].append(subcategory)
                    self.save_categories_to_json()
                    self.status_bar.showMessage(f"Created new subcategory: {subcategory}")

                # Update the transaction
                txn['category'] = category
                txn['subcategory'] = subcategory

                # Update the table - Fix: Use correct column indices (4=Category, 5=Subcategory)
                self.transaction_table.setItem(current_row, 4, QTableWidgetItem(category))
                self.transaction_table.setItem(current_row, 5, QTableWidgetItem(subcategory))

                # Update details
                self.show_transaction_details(txn)

                # Refresh dropdowns to show new items
                self.refresh_category_dropdowns()

                self.status_bar.showMessage(f"Applied label: {category}/{subcategory}")

    def on_selection_changed(self):
        """Handle selection changes to update batch labeling info"""
        selected_rows = self.get_selected_rows()
        count = len(selected_rows)

        if count == 0:
            self.selection_info_label.setText("No transactions selected")
            self.batch_apply_btn.setEnabled(False)
        elif count == 1:
            self.selection_info_label.setText("1 transaction selected")
            self.batch_apply_btn.setEnabled(True)
        else:
            self.selection_info_label.setText(f"{count} transactions selected")
            self.batch_apply_btn.setEnabled(True)

    def get_selected_rows(self):
        """Get list of selected row indices"""
        selected_rows = []
        for item in self.transaction_table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.append(row)
        return sorted(selected_rows)

    def apply_batch_label(self):
        """Apply the selected label to all selected transactions"""
        selected_rows = self.get_selected_rows()
        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select transactions to label.")
            return

        category = self.category_combo.currentText()
        subcategory = self.subcategory_combo.currentText()

        if not category:
            QMessageBox.warning(self, "No Category", "Please select a category first.")
            return

        # Confirm batch operation
        count = len(selected_rows)
        reply = QMessageBox.question(
            self,
            "Confirm Batch Labeling",
            f"Apply label '{category}/{subcategory}' to {count} selected transactions?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return

        # Apply labels to selected transactions
        filtered_transactions = self.get_filtered_transactions()
        updated_count = 0

        for row in selected_rows:
            if row < len(filtered_transactions):
                txn = filtered_transactions[row]

                # Update the transaction
                txn['category'] = category
                txn['subcategory'] = subcategory

                # Update the table
                self.transaction_table.setItem(row, 3, QTableWidgetItem(category))
                self.transaction_table.setItem(row, 4, QTableWidgetItem(subcategory))

                updated_count += 1

        self.status_bar.showMessage(f"Applied label '{category}/{subcategory}' to {updated_count} transactions")
        QMessageBox.information(self, "Batch Labeling Complete", f"Successfully labeled {updated_count} transactions.")

    def select_all_filtered(self):
        """Select all currently filtered transactions"""
        self.transaction_table.selectAll()
        self.status_bar.showMessage("Selected all filtered transactions")

    def clear_selection(self):
        """Clear all selections"""
        self.transaction_table.clearSelection()
        self.status_bar.showMessage("Selection cleared")

    def save_data(self):
        """Save the labeled data back to CSV"""
        if not self.csv_file_path or not self.transactions:
            QMessageBox.warning(self, "No Data", "No data to save.")
            return
        
        try:
            with open(self.csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['date', 'description', 'amount', 'transaction_type', 'bank_name', 'source_file', 'category', 'subcategory']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for txn in self.transactions:
                    writer.writerow({
                        'date': txn['date'],
                        'description': txn['description'],
                        'amount': txn['amount'],
                        'transaction_type': txn.get('type', ''),
                        'bank_name': txn['bank_name'],
                        'source_file': txn['source_file'],
                        'category': txn['category'],
                        'subcategory': txn['subcategory']
                    })
            
            QMessageBox.information(self, "Save Complete", f"Data saved to {self.csv_file_path}")
            self.status_bar.showMessage("Data saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving data: {str(e)}")
            QMessageBox.critical(self, "Save Error", f"Failed to save data:\n{str(e)}")

    # Enhanced filtering methods
    def on_search_changed(self, text: str):
        """Handle search text changes with debouncing"""
        self.search_text = text
        self.search_timer.start()  # Restart timer for debouncing

    def clear_search(self):
        """Clear the search filter"""
        self.search_input.clear()
        self.search_text = ""
        self.apply_all_filters()

    def clear_description_filter(self):
        """Clear the description filter"""
        self.search_input.clear()
        self.desc_operator_combo.setCurrentText("Contains")
        self.search_text = ""
        self.apply_all_filters()

    def clear_amount_filter(self):
        """Clear the amount filter"""
        self.amount_min_input.setValue(self.amount_min_input.minimum())
        self.amount_max_input.setValue(self.amount_max_input.maximum())
        self.amount_operator_combo.setCurrentText("Between")
        self.on_amount_filter_changed()

    def on_amount_operator_changed(self):
        """Handle amount operator changes"""
        operator = self.amount_operator_combo.currentText()

        # Show/hide appropriate controls based on operator
        if operator == "Between":
            self.amount_to_label.show()
            self.amount_max_input.show()
            self.amount_min_input.setPrefix("")
            self.amount_max_input.setPrefix("")
        else:
            self.amount_to_label.hide()
            self.amount_max_input.hide()
            if operator == "Equals":
                self.amount_min_input.setPrefix("= ")
            elif operator == "Greater than":
                self.amount_min_input.setPrefix("> ")
            elif operator == "Less than":
                self.amount_min_input.setPrefix("< ")
            elif operator == "Not equal":
                self.amount_min_input.setPrefix("≠ ")

        self.apply_all_filters()

    def apply_upi_preset(self):
        """Apply UPI transactions preset"""
        self.desc_operator_combo.setCurrentText("Contains")
        self.search_input.setText("UPI")
        self.transaction_type_combo.setCurrentText("All Types")
        self.category_status_combo.setCurrentText("All Status")
        self.apply_all_filters()

    def apply_large_amounts_preset(self):
        """Apply large amounts preset (>1000)"""
        self.amount_operator_combo.setCurrentText("Greater than")
        self.amount_min_input.setValue(1000)
        self.on_amount_operator_changed()

    def apply_unlabeled_upi_preset(self):
        """Apply unlabeled UPI transactions preset"""
        self.desc_operator_combo.setCurrentText("Contains")
        self.search_input.setText("UPI")
        self.transaction_type_combo.setCurrentText("All Types")
        self.category_status_combo.setCurrentText("Unlabeled")
        self.apply_all_filters()

    def count_active_filters(self):
        """Count the number of active filters"""
        count = 0

        # Description filter
        if self.search_text:
            count += 1

        # Transaction type filter
        if self.transaction_type_combo.currentText() != "All Types":
            count += 1

        # Category status filter
        if self.category_status_combo.currentText() != "All Status":
            count += 1

        # Date filters
        if self.date_from or self.date_to:
            count += 1

        # Amount filters
        if (self.amount_min is not None and self.amount_min != self.amount_min_input.minimum()) or \
           (self.amount_max is not None and self.amount_max != self.amount_max_input.maximum()):
            count += 1

        # Category name filters
        if self.category_filter_combo.currentText() != "All Categories":
            count += 1

        # Subcategory name filters
        if self.subcategory_filter_combo.currentText() != "All Subcategories":
            count += 1

        return count

    def on_date_filter_changed(self):
        """Handle date filter changes"""
        # Get dates from inputs (None if cleared)
        self.date_from = self.date_from_input.date().toPython() if not self.date_from_input.text() == self.date_from_input.specialValueText() else None
        self.date_to = self.date_to_input.date().toPython() if not self.date_to_input.text() == self.date_to_input.specialValueText() else None
        self.apply_all_filters()

    def on_amount_filter_changed(self):
        """Handle amount filter changes"""
        # Get amounts from inputs (None if at minimum/maximum)
        self.amount_min = self.amount_min_input.value() if self.amount_min_input.value() != self.amount_min_input.minimum() else None
        self.amount_max = self.amount_max_input.value() if self.amount_max_input.value() != self.amount_max_input.maximum() else None

        # Validate min <= max
        if self.amount_min is not None and self.amount_max is not None and self.amount_min > self.amount_max:
            QMessageBox.warning(self, "Invalid Range", "Minimum amount cannot be greater than maximum amount.")
            return

        self.apply_all_filters()

    def set_date_preset(self, days: int):
        """Set date range to last N days"""
        from datetime import date, timedelta
        end_date = date.today()
        start_date = end_date - timedelta(days=days)

        self.date_from_input.setDate(QDate.fromString(start_date.isoformat(), Qt.ISODate))
        self.date_to_input.setDate(QDate.fromString(end_date.isoformat(), Qt.ISODate))
        self.on_date_filter_changed()

    def set_date_preset_year(self):
        """Set date range to current year"""
        from datetime import date
        current_year = date.today().year
        start_date = date(current_year, 1, 1)
        end_date = date.today()

        self.date_from_input.setDate(QDate.fromString(start_date.isoformat(), Qt.ISODate))
        self.date_to_input.setDate(QDate.fromString(end_date.isoformat(), Qt.ISODate))
        self.on_date_filter_changed()

    def clear_all_filters(self):
        """Clear all filters and reset to default state"""
        # Clear description filter
        self.search_input.clear()
        self.search_text = ""
        self.desc_operator_combo.setCurrentText("Contains")

        # Reset transaction type filters
        self.transaction_type_combo.setCurrentText("All Types")
        self.category_status_combo.setCurrentText("All Status")

        # Reset category name filters
        self.category_filter_combo.setCurrentText("All Categories")
        self.subcategory_filter_combo.setCurrentText("All Subcategories")

        # Clear date filters
        self.date_from_input.clear()
        self.date_to_input.clear()
        self.date_from = None
        self.date_to = None

        # Clear amount filters
        self.amount_min_input.setValue(self.amount_min_input.minimum())
        self.amount_max_input.setValue(self.amount_max_input.maximum())
        self.amount_operator_combo.setCurrentText("Between")
        self.amount_min = None
        self.amount_max = None

        # Reset amount filter UI
        self.on_amount_operator_changed()

        # Reset filter logic to AND
        self.filter_logic_combo.setCurrentText("AND (All must match)")

        # Apply filters
        self.apply_all_filters()

    def on_header_clicked(self, logical_index: int):
        """Handle table header clicks for sorting"""
        # Toggle sort order if same column, otherwise use ascending
        if logical_index == self.current_sort_column:
            self.current_sort_order = Qt.DescendingOrder if self.current_sort_order == Qt.AscendingOrder else Qt.AscendingOrder
        else:
            self.current_sort_column = logical_index
            self.current_sort_order = Qt.AscendingOrder

        # Apply sorting
        self.apply_all_filters()

    def apply_all_filters(self):
        """Apply all active filters and update the table with enhanced filtering"""
        if not self.transactions:
            return

        # Start with all transactions
        filtered = self.transactions.copy()

        # Determine filter logic (AND vs OR)
        use_or_logic = "OR" in self.filter_logic_combo.currentText()

        if use_or_logic:
            # OR logic: collect results from each filter and combine
            or_results = set()

            # Apply each filter separately and collect results
            self._apply_or_filters(or_results)

            # Convert back to list maintaining order
            filtered = [txn for txn in self.transactions if id(txn) in or_results]
        else:
            # AND logic: apply filters sequentially (default behavior)
            filtered = self._apply_and_filters(filtered)

        # Apply sorting
        if filtered:
            column_keys = ['date', 'description', 'amount', 'category', 'subcategory', 'type']
            sort_key = column_keys[self.current_sort_column] if self.current_sort_column < len(column_keys) else 'date'

            reverse_order = self.current_sort_order == Qt.DescendingOrder

            try:
                if sort_key == 'amount':
                    filtered.sort(key=lambda x: float(x.get(sort_key, 0)), reverse=reverse_order)
                elif sort_key == 'date':
                    filtered.sort(key=lambda x: x.get(sort_key, ''), reverse=reverse_order)
                else:
                    filtered.sort(key=lambda x: str(x.get(sort_key, '')).lower(), reverse=reverse_order)
            except Exception as e:
                if hasattr(self, 'logger'):
                    self.logger.warning(f"Error sorting by {sort_key}: {str(e)}")

        # Store filtered results
        self.filtered_transactions = filtered

        # Update table
        self.populate_table()

        # Update results count and active filters
        total_count = len(self.transactions)
        filtered_count = len(filtered)
        active_count = self.count_active_filters()

        self.results_count_label.setText(f"Showing {filtered_count} of {total_count} transactions")
        self.active_filters_label.setText(f"{active_count} filters active")

    def _apply_and_filters(self, filtered):
        """Apply filters with AND logic"""
        # Enhanced description filter with multi-word support
        if self.search_text:
            search_lower = self.search_text.lower()
            operator = self.desc_operator_combo.currentText()

            if operator == "Contains":
                # Multi-word support: split search terms and check if all words are present
                search_words = [word.strip() for word in search_lower.split() if word.strip()]
                if len(search_words) == 1:
                    # Single word - use simple contains
                    filtered = [txn for txn in filtered if search_words[0] in str(txn.get('description', '')).lower()]
                else:
                    # Multiple words - check if all words are present
                    filtered = [txn for txn in filtered
                              if all(word in str(txn.get('description', '')).lower() for word in search_words)]
            elif operator == "Starts with":
                filtered = [txn for txn in filtered if str(txn.get('description', '')).lower().startswith(search_lower)]
            elif operator == "Ends with":
                filtered = [txn for txn in filtered if str(txn.get('description', '')).lower().endswith(search_lower)]
            elif operator == "Equals":
                filtered = [txn for txn in filtered if str(txn.get('description', '')).lower() == search_lower]
            elif operator == "Does not contain":
                # Multi-word support for "does not contain" - none of the words should be present
                search_words = [word.strip() for word in search_lower.split() if word.strip()]
                if len(search_words) == 1:
                    # Single word - use simple not contains
                    filtered = [txn for txn in filtered if search_words[0] not in str(txn.get('description', '')).lower()]
                else:
                    # Multiple words - check if none of the words are present
                    filtered = [txn for txn in filtered
                              if not any(word in str(txn.get('description', '')).lower() for word in search_words)]

        # Apply transaction type filter (Credit/Debit)
        transaction_type = self.transaction_type_combo.currentText()
        if transaction_type == "Credit Only":
            filtered = [txn for txn in filtered if txn.get('type') == 'credit']
        elif transaction_type == "Debit Only":
            filtered = [txn for txn in filtered if txn.get('type') == 'debit']

        # Apply category status filter
        category_status = self.category_status_combo.currentText()
        if category_status == "Labeled":
            filtered = [txn for txn in filtered if txn.get('category') and txn.get('subcategory')]
        elif category_status == "Unlabeled":
            filtered = [txn for txn in filtered if not txn.get('category') or not txn.get('subcategory')]
        elif category_status == "Has Category":
            filtered = [txn for txn in filtered if txn.get('category')]
        elif category_status == "Missing Category":
            filtered = [txn for txn in filtered if not txn.get('category')]
        elif category_status == "Has Subcategory":
            filtered = [txn for txn in filtered if txn.get('subcategory')]
        elif category_status == "Missing Subcategory":
            filtered = [txn for txn in filtered if not txn.get('subcategory')]

        # Category name filter
        category_filter = self.category_filter_combo.currentText()
        if category_filter != "All Categories":
            filtered = [txn for txn in filtered if str(txn.get('category', '')) == category_filter]

        # Subcategory name filter
        subcategory_filter = self.subcategory_filter_combo.currentText()
        if subcategory_filter != "All Subcategories":
            filtered = [txn for txn in filtered if str(txn.get('subcategory', '')) == subcategory_filter]

        # Date range filter
        if self.date_from or self.date_to:
            date_filtered = []
            for txn in filtered:
                txn_date_str = txn.get('date', '')
                if txn_date_str:
                    try:
                        from datetime import datetime
                        txn_date = datetime.strptime(txn_date_str, '%Y-%m-%d').date()

                        if self.date_from and txn_date < self.date_from:
                            continue
                        if self.date_to and txn_date > self.date_to:
                            continue

                        date_filtered.append(txn)
                    except ValueError:
                        continue
            filtered = date_filtered

        # Enhanced amount filter
        if self.amount_min is not None or self.amount_max is not None:
            amount_filtered = []
            operator = self.amount_operator_combo.currentText()

            for txn in filtered:
                try:
                    amount = float(txn.get('amount', 0))

                    if operator == "Between":
                        if self.amount_min is not None and amount < self.amount_min:
                            continue
                        if self.amount_max is not None and amount > self.amount_max:
                            continue
                    elif operator == "Equals":
                        if self.amount_min is not None and amount != self.amount_min:
                            continue
                    elif operator == "Greater than":
                        if self.amount_min is not None and amount <= self.amount_min:
                            continue
                    elif operator == "Less than":
                        if self.amount_min is not None and amount >= self.amount_min:
                            continue
                    elif operator == "Not equal":
                        if self.amount_min is not None and amount == self.amount_min:
                            continue

                    amount_filtered.append(txn)
                except (ValueError, TypeError):
                    continue
            filtered = amount_filtered

        return filtered

    def _apply_or_filters(self, or_results):
        """Apply filters with OR logic - collect results from each active filter"""
        # Description filter with multi-word support
        if self.search_text:
            search_lower = self.search_text.lower()
            operator = self.desc_operator_combo.currentText()

            for txn in self.transactions:
                desc = str(txn.get('description', '')).lower()
                match = False

                if operator == "Contains":
                    # Multi-word support: split search terms and check if all words are present
                    search_words = [word.strip() for word in search_lower.split() if word.strip()]
                    if len(search_words) == 1:
                        # Single word - use simple contains
                        match = search_words[0] in desc
                    else:
                        # Multiple words - check if all words are present
                        match = all(word in desc for word in search_words)
                elif operator == "Starts with" and desc.startswith(search_lower):
                    match = True
                elif operator == "Ends with" and desc.endswith(search_lower):
                    match = True
                elif operator == "Equals" and desc == search_lower:
                    match = True
                elif operator == "Does not contain":
                    # Multi-word support for "does not contain" - none of the words should be present
                    search_words = [word.strip() for word in search_lower.split() if word.strip()]
                    if len(search_words) == 1:
                        # Single word - use simple not contains
                        match = search_words[0] not in desc
                    else:
                        # Multiple words - check if none of the words are present
                        match = not any(word in desc for word in search_words)

                if match:
                    or_results.add(id(txn))

        # Transaction type filter (Credit/Debit)
        transaction_type = self.transaction_type_combo.currentText()
        if transaction_type != "All Types":
            for txn in self.transactions:
                match = False

                if transaction_type == "Credit Only" and txn.get('type') == 'credit':
                    match = True
                elif transaction_type == "Debit Only" and txn.get('type') == 'debit':
                    match = True

                if match:
                    or_results.add(id(txn))

        # Category status filter
        category_status = self.category_status_combo.currentText()
        if category_status != "All Status":
            for txn in self.transactions:
                match = False

                if category_status == "Labeled" and txn.get('category') and txn.get('subcategory'):
                    match = True
                elif category_status == "Unlabeled" and (not txn.get('category') or not txn.get('subcategory')):
                    match = True
                elif category_status == "Has Category" and txn.get('category'):
                    match = True
                elif category_status == "Missing Category" and not txn.get('category'):
                    match = True
                elif category_status == "Has Subcategory" and txn.get('subcategory'):
                    match = True
                elif category_status == "Missing Subcategory" and not txn.get('subcategory'):
                    match = True

                if match:
                    or_results.add(id(txn))

        # Category name filter
        category_filter = self.category_filter_combo.currentText()
        if category_filter != "All Categories":
            for txn in self.transactions:
                if str(txn.get('category', '')) == category_filter:
                    or_results.add(id(txn))

        # Subcategory name filter
        subcategory_filter = self.subcategory_filter_combo.currentText()
        if subcategory_filter != "All Subcategories":
            for txn in self.transactions:
                if str(txn.get('subcategory', '')) == subcategory_filter:
                    or_results.add(id(txn))

        # Date range filter
        if self.date_from or self.date_to:
            from datetime import datetime
            for txn in self.transactions:
                txn_date_str = txn.get('date', '')
                if txn_date_str:
                    try:
                        txn_date = datetime.strptime(txn_date_str, '%Y-%m-%d').date()

                        date_match = True
                        if self.date_from and txn_date < self.date_from:
                            date_match = False
                        if self.date_to and txn_date > self.date_to:
                            date_match = False

                        if date_match:
                            or_results.add(id(txn))
                    except ValueError:
                        continue

        # Amount filter
        if self.amount_min is not None or self.amount_max is not None:
            operator = self.amount_operator_combo.currentText()

            for txn in self.transactions:
                try:
                    amount = float(txn.get('amount', 0))
                    match = False

                    if operator == "Between":
                        if ((self.amount_min is None or amount >= self.amount_min) and
                            (self.amount_max is None or amount <= self.amount_max)):
                            match = True
                    elif operator == "Equals" and self.amount_min is not None and amount == self.amount_min:
                        match = True
                    elif operator == "Greater than" and self.amount_min is not None and amount > self.amount_min:
                        match = True
                    elif operator == "Less than" and self.amount_min is not None and amount < self.amount_min:
                        match = True
                    elif operator == "Not equal" and self.amount_min is not None and amount != self.amount_min:
                        match = True

                    if match:
                        or_results.add(id(txn))
                except (ValueError, TypeError):
                    continue

    def get_filtered_transactions(self):
        """Get transactions based on current filters"""
        return self.filtered_transactions if hasattr(self, 'filtered_transactions') else self.transactions

    def on_manual_selection_changed(self):
        """Handle manual checkbox selection changes"""
        self.on_selection_changed()

    def get_selected_rows(self):
        """Get list of selected row indices from checkboxes"""
        selected_rows = []
        for row in range(self.transaction_table.rowCount()):
            checkbox = self.transaction_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_rows.append(row)
        return selected_rows

    def on_selection_changed(self):
        """Handle selection changes to update batch labeling info"""
        selected_rows = self.get_selected_rows()
        count = len(selected_rows)

        if count == 0:
            self.selection_info_label.setText("No transactions selected")
            self.batch_apply_btn.setEnabled(False)
        elif count == 1:
            self.selection_info_label.setText("1 transaction selected")
            self.batch_apply_btn.setEnabled(True)
        else:
            self.selection_info_label.setText(f"{count} transactions selected")
            self.batch_apply_btn.setEnabled(True)

    def apply_batch_label(self):
        """Apply the selected label to all selected transactions"""
        selected_rows = self.get_selected_rows()
        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select transactions to label.")
            return

        category = self.category_combo.currentText().strip()
        subcategory = self.subcategory_combo.currentText().strip()

        if not category:
            QMessageBox.warning(self, "No Category", "Please enter or select a category first.")
            return

        # Auto-create category/subcategory if needed (check first transaction for type)
        filtered_transactions = self.get_filtered_transactions()
        if selected_rows and filtered_transactions:
            first_txn = filtered_transactions[selected_rows[0]]
            txn_type = first_txn.get('type', 'debit')

            # Auto-create category if it doesn't exist
            if txn_type not in self.categories:
                self.categories[txn_type] = {}

            if category not in self.categories[txn_type]:
                self.categories[txn_type][category] = []
                self.save_categories_to_json()

            # Auto-create subcategory if it doesn't exist and is provided
            if subcategory and subcategory not in self.categories[txn_type][category]:
                self.categories[txn_type][category].append(subcategory)
                self.save_categories_to_json()

        # Confirm batch operation
        count = len(selected_rows)
        reply = QMessageBox.question(
            self,
            "Confirm Batch Labeling",
            f"Apply label '{category}/{subcategory}' to {count} selected transactions?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return

        # Apply labels to selected transactions
        updated_count = 0

        for row in selected_rows:
            if row < len(filtered_transactions):
                txn = filtered_transactions[row]

                # Update the transaction
                txn['category'] = category
                txn['subcategory'] = subcategory

                # Update the table (adjust for checkbox column)
                self.transaction_table.setItem(row, 4, QTableWidgetItem(category))
                self.transaction_table.setItem(row, 5, QTableWidgetItem(subcategory))

                updated_count += 1

        # Refresh dropdowns to show any new categories/subcategories
        self.refresh_category_dropdowns()

        self.status_bar.showMessage(f"Applied label '{category}/{subcategory}' to {updated_count} transactions")
        QMessageBox.information(self, "Batch Labeling Complete", f"Successfully labeled {updated_count} transactions.")

    def select_all_filtered(self):
        """Select all currently filtered transactions"""
        for row in range(self.transaction_table.rowCount()):
            checkbox = self.transaction_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
        self.status_bar.showMessage("Selected all filtered transactions")

    def clear_selection(self):
        """Clear all selections"""
        for row in range(self.transaction_table.rowCount()):
            checkbox = self.transaction_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)
        self.status_bar.showMessage("Selection cleared")

    def create_category(self):
        """Create a new category"""
        # Ask for transaction type

        dialog = QDialog(self)
        dialog.setWindowTitle("Create New Category")
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # Transaction type selection
        layout.addWidget(QLabel("Transaction Type:"))
        credit_radio = QRadioButton("Credit (Income)")
        debit_radio = QRadioButton("Debit (Expense)")
        debit_radio.setChecked(True)  # Default to debit

        type_layout = QHBoxLayout()
        type_layout.addWidget(credit_radio)
        type_layout.addWidget(debit_radio)
        layout.addLayout(type_layout)

        # Category name input
        layout.addWidget(QLabel("Category Name:"))
        name_input = QLineEdit()
        layout.addWidget(name_input)

        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        if dialog.exec() == QDialog.Accepted:
            category_name = name_input.text().strip()
            if category_name:
                txn_type = 'credit' if credit_radio.isChecked() else 'debit'

                # Add to categories
                if category_name not in self.categories[txn_type]:
                    self.categories[txn_type][category_name] = []
                    self.save_categories_to_json()
                    self.refresh_category_dropdowns()
                    QMessageBox.information(self, "Success", f"Category '{category_name}' created successfully!")
                else:
                    QMessageBox.warning(self, "Duplicate", f"Category '{category_name}' already exists!")

    def edit_category(self):
        """Edit an existing category"""
        current_category = self.category_combo.currentText()
        if not current_category:
            QMessageBox.warning(self, "No Selection", "Please select a category to edit.")
            return

        new_name, ok = QInputDialog.getText(self, "Edit Category", "Category Name:", text=current_category)
        if ok and new_name.strip() and new_name.strip() != current_category:
            # Find the transaction type for this category
            txn_type = None
            for t_type in ['credit', 'debit']:
                if current_category in self.categories[t_type]:
                    txn_type = t_type
                    break

            if txn_type:
                # Update category name
                subcategories = self.categories[txn_type][current_category]
                del self.categories[txn_type][current_category]
                self.categories[txn_type][new_name.strip()] = subcategories

                self.save_categories_to_json()
                self.refresh_category_dropdowns()
                QMessageBox.information(self, "Success", f"Category renamed to '{new_name.strip()}'!")

    def create_subcategory(self):
        """Create a new subcategory"""
        current_category = self.category_combo.currentText()
        if not current_category:
            QMessageBox.warning(self, "No Category", "Please select a category first.")
            return

        subcategory_name, ok = QInputDialog.getText(self, "Create Subcategory", f"Subcategory name for '{current_category}':")
        if ok and subcategory_name.strip():
            # Find the transaction type for this category
            txn_type = None
            for t_type in ['credit', 'debit']:
                if current_category in self.categories[t_type]:
                    txn_type = t_type
                    break

            if txn_type:
                if subcategory_name.strip() not in self.categories[txn_type][current_category]:
                    self.categories[txn_type][current_category].append(subcategory_name.strip())
                    self.save_categories_to_json()
                    self.refresh_category_dropdowns()
                    QMessageBox.information(self, "Success", f"Subcategory '{subcategory_name.strip()}' created!")
                else:
                    QMessageBox.warning(self, "Duplicate", f"Subcategory '{subcategory_name.strip()}' already exists!")

    def edit_subcategory(self):
        """Edit an existing subcategory"""
        current_category = self.category_combo.currentText()
        current_subcategory = self.subcategory_combo.currentText()

        if not current_category or not current_subcategory:
            QMessageBox.warning(self, "No Selection", "Please select a category and subcategory to edit.")
            return

        new_name, ok = QInputDialog.getText(self, "Edit Subcategory", "Subcategory Name:", text=current_subcategory)
        if ok and new_name.strip() and new_name.strip() != current_subcategory:
            # Find the transaction type for this category
            txn_type = None
            for t_type in ['credit', 'debit']:
                if current_category in self.categories[t_type]:
                    txn_type = t_type
                    break

            if txn_type:
                subcategories = self.categories[txn_type][current_category]
                if current_subcategory in subcategories:
                    index = subcategories.index(current_subcategory)
                    subcategories[index] = new_name.strip()

                    self.save_categories_to_json()
                    self.refresh_category_dropdowns()
                    QMessageBox.information(self, "Success", f"Subcategory renamed to '{new_name.strip()}'!")

    def save_categories_to_json(self):
        """Save categories back to JSON file"""
        import json
        from pathlib import Path

        json_file = Path("bank_analyzer_config/ml_data/ml_categories.json")

        try:
            # Create new JSON structure from current categories
            json_data = []
            category_id = 1

            for txn_type, categories in self.categories.items():
                for category_name, subcategories in categories.items():
                    # Add parent category
                    parent_id = f"ml_{category_name.lower().replace(' ', '_')}_{category_id}"
                    json_data.append({
                        "id": parent_id,
                        "name": category_name,
                        "parent_id": None,
                        "description": "",
                        "is_active": True,
                        "is_system": False,
                        "color": "#007ACC",
                        "icon": "folder",
                        "category_type": "income" if txn_type == 'credit' else "expense",
                        "created_at": "2025-01-01T00:00:00.000000",
                        "updated_at": "2025-01-01T00:00:00.000000"
                    })
                    category_id += 1

                    # Add subcategories
                    for subcategory in subcategories:
                        sub_id = f"ml_{subcategory.lower().replace(' ', '_')}_{category_id}"
                        json_data.append({
                            "id": sub_id,
                            "name": subcategory,
                            "parent_id": parent_id,
                            "description": "",
                            "is_active": True,
                            "is_system": False,
                            "color": "#007ACC",
                            "icon": "folder",
                            "category_type": "income" if txn_type == 'credit' else "expense",
                            "created_at": "2025-01-01T00:00:00.000000",
                            "updated_at": "2025-01-01T00:00:00.000000"
                        })
                        category_id += 1

            # Save to file
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Categories saved to {json_file}")

        except Exception as e:
            self.logger.error(f"Error saving categories to JSON: {str(e)}")
            QMessageBox.critical(self, "Save Error", f"Failed to save categories:\n{str(e)}")

    def refresh_category_dropdowns(self):
        """Refresh the category dropdowns after changes"""
        # Store current selections
        current_category = self.category_combo.currentText()
        current_subcategory = self.subcategory_combo.currentText()

        # Reinitialize with all categories
        self.initialize_category_dropdowns()

        # Restore selections if possible
        if current_category:
            index = self.category_combo.findText(current_category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)

        if current_subcategory:
            index = self.subcategory_combo.findText(current_subcategory)
            if index >= 0:
                self.subcategory_combo.setCurrentIndex(index)

    def organize_categories(self):
        """Open category organization dialog with collapsible tree view"""
        dialog = CategoryOrganizationDialog(self.categories, self)
        if dialog.exec() == QDialog.Accepted:
            # Get the reorganized categories
            self.categories = dialog.get_categories()
            self.save_categories_to_json()
            self.refresh_category_dropdowns()
            QMessageBox.information(self, "Success", "Categories reorganized successfully!")


class CategoryOrganizationDialog(QDialog):
    """Dialog for organizing categories in a collapsible tree view"""

    def __init__(self, categories, parent=None):
        super().__init__(parent)
        self.categories = categories.copy()
        self.setWindowTitle("Organize Categories")
        self.setModal(True)
        self.resize(600, 500)

        self.setup_ui()
        self.populate_tree()

    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)

        # Instructions
        instructions = QLabel(
            "Drag and drop to rearrange categories and subcategories.\n"
            "Categories are organized by transaction type (Credit/Debit)."
        )
        instructions.setStyleSheet("font-size: 11px; color: #666; margin: 10px;")
        layout.addWidget(instructions)

        # Tree widget
        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(["Category", "Type", "Count"])
        self.tree.setDragDropMode(QTreeWidget.InternalMove)
        self.tree.setDefaultDropAction(Qt.MoveAction)
        self.tree.setSelectionMode(QTreeWidget.SingleSelection)

        # Enable drag and drop
        self.tree.setDragEnabled(True)
        self.tree.setAcceptDrops(True)
        self.tree.setDropIndicatorShown(True)

        layout.addWidget(self.tree)

        # Buttons
        button_layout = QHBoxLayout()

        # Expand/Collapse buttons
        expand_all_btn = QPushButton("Expand All")
        expand_all_btn.clicked.connect(self.tree.expandAll)
        button_layout.addWidget(expand_all_btn)

        collapse_all_btn = QPushButton("Collapse All")
        collapse_all_btn.clicked.connect(self.tree.collapseAll)
        button_layout.addWidget(collapse_all_btn)

        button_layout.addStretch()

        # Dialog buttons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        button_layout.addWidget(buttons)

        layout.addLayout(button_layout)

    def populate_tree(self):
        """Populate the tree with categories"""
        self.tree.clear()

        # Create root items for Credit and Debit
        credit_root = QTreeWidgetItem(self.tree, ["💰 Credit (Income)", "Root", ""])
        credit_root.setExpanded(True)
        credit_root.setData(0, Qt.UserRole, {"type": "root", "txn_type": "credit"})

        debit_root = QTreeWidgetItem(self.tree, ["💸 Debit (Expense)", "Root", ""])
        debit_root.setExpanded(True)
        debit_root.setData(0, Qt.UserRole, {"type": "root", "txn_type": "debit"})

        # Add credit categories
        for category, subcategories in self.categories.get('credit', {}).items():
            cat_item = QTreeWidgetItem(credit_root, [category, "Category", str(len(subcategories))])
            cat_item.setExpanded(False)
            cat_item.setData(0, Qt.UserRole, {"type": "category", "txn_type": "credit", "name": category})

            # Add subcategories
            for subcategory in subcategories:
                sub_item = QTreeWidgetItem(cat_item, [subcategory, "Subcategory", ""])
                sub_item.setData(0, Qt.UserRole, {"type": "subcategory", "txn_type": "credit", "parent": category, "name": subcategory})

        # Add debit categories
        for category, subcategories in self.categories.get('debit', {}).items():
            cat_item = QTreeWidgetItem(debit_root, [category, "Category", str(len(subcategories))])
            cat_item.setExpanded(False)
            cat_item.setData(0, Qt.UserRole, {"type": "category", "txn_type": "debit", "name": category})

            # Add subcategories
            for subcategory in subcategories:
                sub_item = QTreeWidgetItem(cat_item, [subcategory, "Subcategory", ""])
                sub_item.setData(0, Qt.UserRole, {"type": "subcategory", "txn_type": "debit", "parent": category, "name": subcategory})

    def get_categories(self):
        """Extract categories from the tree structure"""
        categories = {'credit': {}, 'debit': {}}

        # Process each root item
        for i in range(self.tree.topLevelItemCount()):
            root_item = self.tree.topLevelItem(i)
            root_data = root_item.data(0, Qt.UserRole)

            if root_data and root_data.get("type") == "root":
                txn_type = root_data.get("txn_type")

                # Process categories under this root
                for j in range(root_item.childCount()):
                    cat_item = root_item.child(j)
                    cat_data = cat_item.data(0, Qt.UserRole)

                    if cat_data and cat_data.get("type") == "category":
                        category_name = cat_item.text(0)
                        subcategories = []

                        # Process subcategories under this category
                        for k in range(cat_item.childCount()):
                            sub_item = cat_item.child(k)
                            subcategory_name = sub_item.text(0)
                            subcategories.append(subcategory_name)

                        categories[txn_type][category_name] = subcategories

        return categories
