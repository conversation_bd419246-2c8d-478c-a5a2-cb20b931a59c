"""
GPU-Accelerated Deep Learning Transaction Categorizer

This module implements a sophisticated neural network architecture optimized for
Indian banking transaction descriptions with CUDA acceleration targeting 95%+ accuracy.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModel
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import json
import logging
from datetime import datetime
import re
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib

from ..core.logger import get_logger


class TransactionDataset(Dataset):
    """Custom dataset for transaction text classification"""
    
    def __init__(self, texts: List[str], labels: List[int], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        # Tokenize text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }


class TransactionClassifierModel(nn.Module):
    """
    Advanced neural network for transaction classification
    Combines BERT-like transformer with custom layers for banking transactions
    """
    
    def __init__(self, num_classes: int, model_name: str = 'distilbert-base-uncased', 
                 dropout_rate: float = 0.3):
        super(TransactionClassifierModel, self).__init__()
        
        # Load pre-trained transformer
        self.transformer = AutoModel.from_pretrained(model_name)
        self.dropout = nn.Dropout(dropout_rate)
        
        # Custom layers for banking transaction features
        transformer_dim = self.transformer.config.hidden_size
        
        # Multi-layer classification head
        self.classifier = nn.Sequential(
            nn.Linear(transformer_dim, 512),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, num_classes)
        )
        
        # Banking-specific feature extractors
        self.amount_attention = nn.MultiheadAttention(transformer_dim, num_heads=8)
        self.upi_attention = nn.MultiheadAttention(transformer_dim, num_heads=8)
        
    def forward(self, input_ids, attention_mask):
        # Get transformer outputs
        transformer_output = self.transformer(
            input_ids=input_ids,
            attention_mask=attention_mask
        )
        
        # Use [CLS] token representation
        pooled_output = transformer_output.last_hidden_state[:, 0, :]
        
        # Apply dropout
        pooled_output = self.dropout(pooled_output)
        
        # Classification
        logits = self.classifier(pooled_output)
        
        return logits


class GPUDeepLearningCategorizer:
    """
    GPU-accelerated deep learning categorizer for bank transactions
    Targets 95%+ accuracy with intelligent fallback strategies
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config/ml_models"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # GPU setup
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger.info(f"Using device: {self.device}")
        
        if torch.cuda.is_available():
            self.logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
            self.logger.info(f"CUDA Version: {torch.version.cuda}")
        
        # Model components
        self.model = None
        self.tokenizer = None
        self.label_encoder = None
        self.category_stats = {}
        self.model_config = {}
        
        # Training parameters
        self.max_length = 512
        self.batch_size = 16
        self.learning_rate = 2e-5
        self.num_epochs = 10
        self.min_category_samples = 50  # Minimum samples for ML usage
        self.min_category_accuracy = 0.90  # Minimum accuracy for ML usage
        
    def preprocess_transaction_text(self, text: str) -> str:
        """
        Preprocess transaction description for optimal neural network performance
        Optimized for Indian banking transaction formats (UPI, IMPS, etc.)
        """
        if pd.isna(text) or not text:
            return ""
        
        text = str(text).strip()
        
        # Normalize common banking terms
        banking_normalizations = {
            r'XXXXX\d+': '[MASKED_ACCOUNT]',
            r'/UPI/\d+': '/UPI/[TRANSACTION_ID]',
            r'/IMPS/\d+': '/IMPS/[TRANSACTION_ID]',
            r'BRANCH\s*:\s*': 'BRANCH:',
            r'\s+': ' ',  # Multiple spaces to single space
        }
        
        for pattern, replacement in banking_normalizations.items():
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        # Keep important banking keywords
        text = text.lower()
        
        return text.strip()
    
    def analyze_category_readiness(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """
        Analyze each category to determine ML readiness
        Returns category statistics and ML usage recommendations
        """
        category_analysis = {}
        
        for category in df['category'].unique():
            if pd.isna(category):
                continue
                
            category_data = df[df['category'] == category]
            sample_count = len(category_data)
            
            # Calculate text diversity
            unique_descriptions = category_data['description'].nunique()
            diversity_ratio = unique_descriptions / sample_count if sample_count > 0 else 0
            
            # Determine ML readiness
            ml_ready = (
                sample_count >= self.min_category_samples and
                diversity_ratio > 0.3  # At least 30% unique descriptions
            )
            
            category_analysis[category] = {
                'sample_count': sample_count,
                'unique_descriptions': unique_descriptions,
                'diversity_ratio': diversity_ratio,
                'ml_ready': ml_ready,
                'recommendation': 'ML' if ml_ready else 'Pattern_Matching'
            }
        
        return category_analysis
    
    def prepare_training_data(self, csv_path: str) -> Tuple[List[str], List[int], Dict[str, Any]]:
        """
        Prepare training data with intelligent category filtering
        """
        self.logger.info(f"Loading training data from {csv_path}")
        
        # Load data
        df = pd.read_csv(csv_path)
        
        # Filter to manually labeled data
        if 'is_manually_labeled' in df.columns:
            df = df[df['is_manually_labeled'] == True].copy()
        else:
            df = df[df['category'].notna() & (df['category'] != '')].copy()
        
        self.logger.info(f"Found {len(df)} labeled transactions")
        
        # Analyze category readiness
        self.category_stats = self.analyze_category_readiness(df)
        
        # Filter to ML-ready categories only
        ml_ready_categories = [
            cat for cat, stats in self.category_stats.items() 
            if stats['ml_ready']
        ]
        
        self.logger.info(f"ML-ready categories: {len(ml_ready_categories)}/{len(self.category_stats)}")
        
        # Filter data to ML-ready categories
        df_ml = df[df['category'].isin(ml_ready_categories)].copy()
        
        if len(df_ml) == 0:
            raise ValueError("No categories have sufficient data for ML training")
        
        # Preprocess text
        df_ml['processed_text'] = df_ml['description'].apply(self.preprocess_transaction_text)
        
        # Remove empty texts
        df_ml = df_ml[df_ml['processed_text'].str.len() > 0].copy()
        
        # Encode labels
        self.label_encoder = LabelEncoder()
        encoded_labels = self.label_encoder.fit_transform(df_ml['category'])
        
        texts = df_ml['processed_text'].tolist()
        labels = encoded_labels.tolist()
        
        # Create metadata
        metadata = {
            'total_samples': len(texts),
            'num_classes': len(self.label_encoder.classes_),
            'classes': self.label_encoder.classes_.tolist(),
            'ml_ready_categories': ml_ready_categories,
            'category_stats': self.category_stats
        }
        
        self.logger.info(f"Prepared {len(texts)} samples for {metadata['num_classes']} classes")

        return texts, labels, metadata

    def train_model(self, csv_path: str) -> Dict[str, Any]:
        """
        Train the GPU-accelerated deep learning model
        """
        self.logger.info("Starting GPU-accelerated deep learning training")

        # Prepare data
        texts, labels, metadata = self.prepare_training_data(csv_path)

        # Initialize tokenizer
        model_name = 'distilbert-base-uncased'  # Faster than full BERT
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)

        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            texts, labels, test_size=0.2, random_state=42, stratify=labels
        )

        # Create datasets
        train_dataset = TransactionDataset(X_train, y_train, self.tokenizer, self.max_length)
        val_dataset = TransactionDataset(X_val, y_val, self.tokenizer, self.max_length)

        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)

        # Initialize model
        self.model = TransactionClassifierModel(
            num_classes=metadata['num_classes'],
            model_name=model_name
        ).to(self.device)

        # Optimizer and loss
        optimizer = optim.AdamW(self.model.parameters(), lr=self.learning_rate)
        criterion = nn.CrossEntropyLoss()

        # Training loop
        best_val_accuracy = 0.0
        training_history = []

        for epoch in range(self.num_epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for batch in train_loader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels_batch = batch['labels'].to(self.device)

                optimizer.zero_grad()

                outputs = self.model(input_ids, attention_mask)
                loss = criterion(outputs, labels_batch)

                loss.backward()
                optimizer.step()

                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += labels_batch.size(0)
                train_correct += (predicted == labels_batch).sum().item()

            # Validation phase
            self.model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0

            with torch.no_grad():
                for batch in val_loader:
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels_batch = batch['labels'].to(self.device)

                    outputs = self.model(input_ids, attention_mask)
                    loss = criterion(outputs, labels_batch)

                    val_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += labels_batch.size(0)
                    val_correct += (predicted == labels_batch).sum().item()

            # Calculate metrics
            train_accuracy = train_correct / train_total
            val_accuracy = val_correct / val_total

            epoch_stats = {
                'epoch': epoch + 1,
                'train_loss': train_loss / len(train_loader),
                'train_accuracy': train_accuracy,
                'val_loss': val_loss / len(val_loader),
                'val_accuracy': val_accuracy
            }

            training_history.append(epoch_stats)

            self.logger.info(
                f"Epoch {epoch+1}/{self.num_epochs}: "
                f"Train Acc: {train_accuracy:.4f}, Val Acc: {val_accuracy:.4f}"
            )

            # Save best model
            if val_accuracy > best_val_accuracy:
                best_val_accuracy = val_accuracy
                self.save_model(metadata, training_history)

        # Final evaluation
        final_results = {
            'best_validation_accuracy': best_val_accuracy,
            'training_history': training_history,
            'metadata': metadata,
            'model_config': {
                'model_name': model_name,
                'max_length': self.max_length,
                'batch_size': self.batch_size,
                'learning_rate': self.learning_rate,
                'num_epochs': self.num_epochs
            }
        }

        self.logger.info(f"Training completed. Best validation accuracy: {best_val_accuracy:.4f}")

        return final_results

    def predict(self, text: str) -> Tuple[str, float, Dict[str, Any]]:
        """
        Predict category for a single transaction with confidence scoring
        """
        if self.model is None or self.tokenizer is None:
            raise ValueError("Model not loaded. Please train or load a model first.")

        # Preprocess text
        processed_text = self.preprocess_transaction_text(text)

        # Tokenize
        encoding = self.tokenizer(
            processed_text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )

        # Move to device
        input_ids = encoding['input_ids'].to(self.device)
        attention_mask = encoding['attention_mask'].to(self.device)

        # Predict
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(input_ids, attention_mask)
            probabilities = torch.softmax(outputs, dim=1)
            confidence, predicted_idx = torch.max(probabilities, 1)

            predicted_category = self.label_encoder.inverse_transform([predicted_idx.cpu().item()])[0]
            confidence_score = confidence.cpu().item()

            # Get top 3 predictions
            top_probs, top_indices = torch.topk(probabilities, k=min(3, len(self.label_encoder.classes_)))
            top_predictions = []

            for prob, idx in zip(top_probs[0], top_indices[0]):
                category = self.label_encoder.inverse_transform([idx.cpu().item()])[0]
                top_predictions.append({
                    'category': category,
                    'confidence': prob.cpu().item()
                })

        prediction_info = {
            'top_predictions': top_predictions,
            'processed_text': processed_text,
            'model_used': 'GPU_Deep_Learning'
        }

        return predicted_category, confidence_score, prediction_info

    def save_model(self, metadata: Dict[str, Any], training_history: List[Dict[str, Any]]):
        """Save the trained model and metadata"""

        # Save PyTorch model
        model_path = self.config_dir / "gpu_deep_learning_model.pt"
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'model_config': self.model_config,
            'metadata': metadata,
            'training_history': training_history
        }, model_path)

        # Save tokenizer
        tokenizer_path = self.config_dir / "tokenizer"
        self.tokenizer.save_pretrained(tokenizer_path)

        # Save label encoder
        encoder_path = self.config_dir / "gpu_label_encoder.joblib"
        joblib.dump(self.label_encoder, encoder_path)

        # Save category statistics
        stats_path = self.config_dir / "category_readiness_stats.json"
        with open(stats_path, 'w') as f:
            json.dump(self.category_stats, f, indent=2)

        # Save comprehensive model info
        model_info = {
            'model_type': 'GPU_Deep_Learning_Transformer',
            'architecture': 'DistilBERT + Custom Classification Head',
            'training_date': datetime.now().isoformat(),
            'device': str(self.device),
            'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU',
            'total_parameters': sum(p.numel() for p in self.model.parameters()),
            'trainable_parameters': sum(p.numel() for p in self.model.parameters() if p.requires_grad),
            'best_validation_accuracy': max([h['val_accuracy'] for h in training_history]),
            'ml_ready_categories': metadata['ml_ready_categories'],
            'total_categories': len(self.category_stats),
            'training_samples': metadata['total_samples'],
            'model_config': self.model_config
        }

        info_path = self.config_dir / "gpu_model_info.json"
        with open(info_path, 'w') as f:
            json.dump(model_info, f, indent=2)

        self.logger.info(f"Model saved to {model_path}")

    def load_model(self) -> bool:
        """Load the trained model"""
        try:
            model_path = self.config_dir / "gpu_deep_learning_model.pt"
            tokenizer_path = self.config_dir / "tokenizer"
            encoder_path = self.config_dir / "gpu_label_encoder.joblib"
            stats_path = self.config_dir / "category_readiness_stats.json"

            if not all(path.exists() for path in [model_path, tokenizer_path, encoder_path, stats_path]):
                self.logger.warning("GPU model files not found")
                return False

            # Load model checkpoint
            checkpoint = torch.load(model_path, map_location=self.device)
            metadata = checkpoint['metadata']

            # Initialize model
            self.model = TransactionClassifierModel(
                num_classes=metadata['num_classes']
            ).to(self.device)

            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()

            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)

            # Load label encoder
            self.label_encoder = joblib.load(encoder_path)

            # Load category stats
            with open(stats_path, 'r') as f:
                self.category_stats = json.load(f)

            self.model_config = checkpoint.get('model_config', {})

            self.logger.info("GPU deep learning model loaded successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error loading GPU model: {e}")
            return False

    def is_category_ml_ready(self, category: str) -> bool:
        """Check if a category should use ML prediction"""
        if category not in self.category_stats:
            return False

        stats = self.category_stats[category]
        return stats.get('ml_ready', False)

    def get_category_recommendation(self, category: str) -> str:
        """Get recommendation for how to handle a category"""
        if category not in self.category_stats:
            return 'Pattern_Matching'

        return self.category_stats[category].get('recommendation', 'Pattern_Matching')

    def get_model_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive model performance summary"""
        if not self.category_stats:
            return {}

        ml_ready_count = sum(1 for stats in self.category_stats.values() if stats['ml_ready'])
        total_categories = len(self.category_stats)

        return {
            'total_categories': total_categories,
            'ml_ready_categories': ml_ready_count,
            'pattern_matching_categories': total_categories - ml_ready_count,
            'ml_coverage_percentage': (ml_ready_count / total_categories * 100) if total_categories > 0 else 0,
            'category_breakdown': self.category_stats,
            'device': str(self.device),
            'model_loaded': self.model is not None
        }

    def prepare_training_data_from_df(self, df: pd.DataFrame) -> Tuple[List[str], List[int], Dict[str, Any]]:
        """
        Prepare training data from a DataFrame
        """
        # Filter to ML-ready categories
        self.category_stats = self.analyze_category_readiness(df)

        ml_ready_categories = [
            cat for cat, stats in self.category_stats.items()
            if stats['ml_ready']
        ]

        # Filter data
        df_ml = df[df['category'].isin(ml_ready_categories)].copy()

        if len(df_ml) == 0:
            raise ValueError("No categories have sufficient data for ML training")

        # Preprocess text
        df_ml['processed_text'] = df_ml['description'].apply(self.preprocess_transaction_text)
        df_ml = df_ml[df_ml['processed_text'].str.len() > 0].copy()

        # Encode labels
        self.label_encoder = LabelEncoder()
        encoded_labels = self.label_encoder.fit_transform(df_ml['category'])

        texts = df_ml['processed_text'].tolist()
        labels = encoded_labels.tolist()

        metadata = {
            'total_samples': len(texts),
            'num_classes': len(self.label_encoder.classes_),
            'classes': self.label_encoder.classes_.tolist(),
            'ml_ready_categories': ml_ready_categories,
            'category_stats': self.category_stats
        }

        return texts, labels, metadata

    def train_model_from_prepared_data(self, texts: List[str], labels: List[int], metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Train model from already prepared data
        """
        # Initialize tokenizer
        model_name = 'distilbert-base-uncased'
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)

        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            texts, labels, test_size=0.2, random_state=42, stratify=labels
        )

        # Create datasets
        train_dataset = TransactionDataset(X_train, y_train, self.tokenizer, self.max_length)
        val_dataset = TransactionDataset(X_val, y_val, self.tokenizer, self.max_length)

        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)

        # Initialize model
        self.model = TransactionClassifierModel(
            num_classes=metadata['num_classes'],
            model_name=model_name
        ).to(self.device)

        # Training loop (simplified version)
        optimizer = optim.AdamW(self.model.parameters(), lr=self.learning_rate)
        criterion = nn.CrossEntropyLoss()

        best_val_accuracy = 0.0
        training_history = []

        for epoch in range(self.num_epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0

            for batch in train_loader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels_batch = batch['labels'].to(self.device)

                optimizer.zero_grad()
                outputs = self.model(input_ids, attention_mask)
                loss = criterion(outputs, labels_batch)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

            # Validation phase
            self.model.eval()
            val_correct = 0
            val_total = 0

            with torch.no_grad():
                for batch in val_loader:
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels_batch = batch['labels'].to(self.device)

                    outputs = self.model(input_ids, attention_mask)
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += labels_batch.size(0)
                    val_correct += (predicted == labels_batch).sum().item()

            val_accuracy = val_correct / val_total

            epoch_stats = {
                'epoch': epoch + 1,
                'train_loss': train_loss / len(train_loader),
                'val_accuracy': val_accuracy
            }

            training_history.append(epoch_stats)

            if val_accuracy > best_val_accuracy:
                best_val_accuracy = val_accuracy
                self.save_model(metadata, training_history)

        return {
            'best_validation_accuracy': best_val_accuracy,
            'training_history': training_history,
            'metadata': metadata
        }
