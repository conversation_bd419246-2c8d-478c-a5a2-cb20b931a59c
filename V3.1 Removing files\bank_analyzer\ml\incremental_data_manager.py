"""
Incremental Training Data Management System
Handles data versioning, separation of old vs new data, and data integrity for incremental ML training
"""

import json
import pandas as pd
import hashlib
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict

from ..core.logger import get_logger


@dataclass
class DataVersion:
    """Represents a version of training data"""
    version_id: str
    timestamp: str
    transaction_count: int
    labeled_count: int
    categories: List[str]
    source_description: str
    is_incremental: bool
    parent_version: Optional[str] = None


@dataclass
class IncrementalDataSummary:
    """Summary of training data state for incremental training"""
    total_transactions: int
    labeled_transactions: int
    unlabeled_transactions: int
    categories: List[str]
    subcategories: List[str]
    latest_version: Optional[DataVersion]
    has_existing_data: bool
    can_do_incremental: bool


class IncrementalDataManager:
    """
    Manages training data with versioning and clear separation of old vs new data
    
    Features:
    - Data versioning for tracking changes
    - Clear separation of existing vs new data
    - Data integrity validation
    - Backup and restore capabilities
    - Incremental data management
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        self.ml_data_dir = self.config_dir / "ml_data"
        
        # Ensure directories exist
        self.ml_data_dir.mkdir(parents=True, exist_ok=True)
        (self.ml_data_dir / "versions").mkdir(exist_ok=True)
        (self.ml_data_dir / "backups").mkdir(exist_ok=True)
        
        # File paths
        self.training_data_file = self.ml_data_dir / "unique_transactions.csv"
        self.versions_file = self.ml_data_dir / "data_versions.json"
        self.integrity_file = self.ml_data_dir / "data_integrity.json"
        
        # Initialize version tracking
        self._ensure_version_tracking()
    
    def _ensure_version_tracking(self):
        """Ensure version tracking file exists"""
        if not self.versions_file.exists():
            versions = {
                "current_version": None,
                "versions": [],
                "created_at": datetime.now().isoformat()
            }
            
            with open(self.versions_file, 'w') as f:
                json.dump(versions, f, indent=2)
    
    def get_data_summary(self) -> IncrementalDataSummary:
        """Get comprehensive summary of training data"""
        try:
            if not self.training_data_file.exists():
                return IncrementalDataSummary(
                    total_transactions=0,
                    labeled_transactions=0,
                    unlabeled_transactions=0,
                    categories=[],
                    subcategories=[],
                    latest_version=None,
                    has_existing_data=False,
                    can_do_incremental=False
                )
            
            df = pd.read_csv(self.training_data_file)
            
            # Analyze data
            total_count = len(df)
            labeled_df = df[
                (df['category'].notna()) & 
                (df['category'] != '') &
                (df['is_manually_labeled'] == True)
            ]
            labeled_count = len(labeled_df)
            unlabeled_count = total_count - labeled_count
            
            categories = list(labeled_df['category'].unique()) if labeled_count > 0 else []
            subcategories = list(labeled_df['sub_category'].dropna().unique()) if labeled_count > 0 else []
            
            # Get latest version
            latest_version = self._get_latest_version()
            
            return IncrementalDataSummary(
                total_transactions=total_count,
                labeled_transactions=labeled_count,
                unlabeled_transactions=unlabeled_count,
                categories=categories,
                subcategories=subcategories,
                latest_version=latest_version,
                has_existing_data=total_count > 0,
                can_do_incremental=labeled_count > 0
            )
            
        except Exception as e:
            self.logger.error(f"Error getting data summary: {str(e)}")
            return IncrementalDataSummary(
                total_transactions=0,
                labeled_transactions=0,
                unlabeled_transactions=0,
                categories=[],
                subcategories=[],
                latest_version=None,
                has_existing_data=False,
                can_do_incremental=False
            )
    
    def analyze_new_data_for_incremental(self, new_transactions: List[Dict]) -> Dict[str, Any]:
        """
        Analyze new transaction data for incremental training
        
        Args:
            new_transactions: List of new transaction dictionaries
            
        Returns:
            Detailed analysis for incremental training decision
        """
        summary = self.get_data_summary()
        new_df = pd.DataFrame(new_transactions)
        
        analysis = {
            "existing_data": {
                "total_transactions": summary.total_transactions,
                "labeled_transactions": summary.labeled_transactions,
                "categories": summary.categories,
                "subcategories": summary.subcategories
            },
            "new_data": {
                "total_transactions": len(new_df),
                "labeled_transactions": 0,
                "categories": [],
                "subcategories": []
            },
            "incremental_analysis": {
                "can_do_incremental": summary.can_do_incremental,
                "is_recommended": False,
                "new_categories": [],
                "overlapping_categories": [],
                "category_expansion": False
            },
            "recommendations": []
        }
        
        # Analyze new data
        if 'category' in new_df.columns:
            new_labeled_df = new_df[
                (new_df['category'].notna()) & 
                (new_df['category'] != '')
            ]
            
            if len(new_labeled_df) > 0:
                new_categories = set(new_labeled_df['category'].unique())
                new_subcategories = set(new_labeled_df['sub_category'].dropna().unique())
                existing_categories = set(summary.categories)
                
                analysis["new_data"]["labeled_transactions"] = len(new_labeled_df)
                analysis["new_data"]["categories"] = list(new_categories)
                analysis["new_data"]["subcategories"] = list(new_subcategories)
                
                # Incremental analysis
                overlapping = new_categories.intersection(existing_categories)
                truly_new = new_categories - existing_categories
                
                analysis["incremental_analysis"]["new_categories"] = list(truly_new)
                analysis["incremental_analysis"]["overlapping_categories"] = list(overlapping)
                analysis["incremental_analysis"]["category_expansion"] = len(truly_new) > 0
                
                # Recommendations
                if summary.can_do_incremental:
                    analysis["incremental_analysis"]["is_recommended"] = True
                    
                    if len(truly_new) > 0:
                        analysis["recommendations"].append(
                            f"Incremental training recommended: Will learn {len(truly_new)} new categories while retaining knowledge of {len(existing_categories)} existing categories"
                        )
                    else:
                        analysis["recommendations"].append(
                            f"Incremental training recommended: Will improve existing {len(overlapping)} categories with new examples"
                        )
                else:
                    analysis["recommendations"].append(
                        "Fresh training required: No existing labeled data found"
                    )
        
        return analysis
    
    def prepare_incremental_data(self, new_transactions: List[Dict], session_description: str) -> Tuple[pd.DataFrame, pd.DataFrame, Dict[str, Any]]:
        """
        Prepare data for incremental training with clear separation
        
        Args:
            new_transactions: New transaction data
            session_description: Description of this training session
            
        Returns:
            Tuple of (existing_data, new_data, preparation_info)
        """
        # Get existing data
        existing_df = pd.DataFrame()
        if self.training_data_file.exists():
            existing_df = pd.read_csv(self.training_data_file)
        
        # Prepare new data
        new_df = pd.DataFrame(new_transactions)
        
        # Create preparation info
        preparation_info = {
            "session_description": session_description,
            "existing_data": {
                "count": len(existing_df),
                "labeled_count": len(existing_df[existing_df['is_manually_labeled'] == True]) if len(existing_df) > 0 else 0
            },
            "new_data": {
                "count": len(new_df),
                "labeled_count": len(new_df[new_df.get('is_manually_labeled', False) == True]) if len(new_df) > 0 else 0
            },
            "is_incremental": len(existing_df) > 0,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"Prepared incremental data: {preparation_info['existing_data']['count']} existing + {preparation_info['new_data']['count']} new")
        
        return existing_df, new_df, preparation_info
    
    def save_incremental_data(self, existing_df: pd.DataFrame, new_df: pd.DataFrame, preparation_info: Dict[str, Any]) -> DataVersion:
        """
        Save incremental training data and create version
        
        Args:
            existing_df: Existing training data
            new_df: New training data
            preparation_info: Information about the preparation
            
        Returns:
            Created data version
        """
        # Combine data
        if len(existing_df) > 0:
            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
        else:
            combined_df = new_df
        
        # Create backup of current data
        if self.training_data_file.exists():
            self._create_backup()
        
        # Save combined data
        combined_df.to_csv(self.training_data_file, index=False)
        
        # Create version
        version = self._create_data_version(combined_df, preparation_info)
        
        # Update integrity tracking
        self._update_integrity_tracking(combined_df, version)
        
        self.logger.info(f"Saved incremental data: {len(combined_df)} total transactions, version {version.version_id}")
        
        return version
    
    def _create_data_version(self, df: pd.DataFrame, preparation_info: Dict[str, Any]) -> DataVersion:
        """Create a new data version"""
        # Generate version ID
        content_hash = hashlib.md5(str(df.values.tobytes()).encode()).hexdigest()[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version_id = f"v_{timestamp}_{content_hash}"
        
        # Analyze data
        labeled_df = df[df['is_manually_labeled'] == True] if 'is_manually_labeled' in df.columns else pd.DataFrame()
        categories = list(labeled_df['category'].unique()) if len(labeled_df) > 0 else []
        
        # Get parent version
        parent_version = self._get_latest_version()
        parent_version_id = parent_version.version_id if parent_version else None
        
        # Create version
        version = DataVersion(
            version_id=version_id,
            timestamp=datetime.now().isoformat(),
            transaction_count=len(df),
            labeled_count=len(labeled_df),
            categories=categories,
            source_description=preparation_info.get("session_description", "Incremental training"),
            is_incremental=preparation_info.get("is_incremental", False),
            parent_version=parent_version_id
        )
        
        # Save version
        self._save_version(version)

        return version

    def _save_version(self, version: DataVersion):
        """Save version to tracking file"""
        try:
            with open(self.versions_file, 'r') as f:
                versions_data = json.load(f)
        except:
            versions_data = {"current_version": None, "versions": []}

        # Add new version
        versions_data["versions"].append(asdict(version))
        versions_data["current_version"] = version.version_id

        # Save updated versions
        with open(self.versions_file, 'w') as f:
            json.dump(versions_data, f, indent=2)

        # Save version snapshot
        version_file = self.ml_data_dir / "versions" / f"{version.version_id}.csv"
        if self.training_data_file.exists():
            shutil.copy2(self.training_data_file, version_file)

    def _get_latest_version(self) -> Optional[DataVersion]:
        """Get the latest data version"""
        try:
            with open(self.versions_file, 'r') as f:
                versions_data = json.load(f)

            if versions_data["versions"]:
                latest = versions_data["versions"][-1]
                return DataVersion(**latest)

            return None
        except:
            return None

    def _create_backup(self):
        """Create backup of current training data"""
        if self.training_data_file.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.ml_data_dir / "backups" / f"unique_transactions_backup_{timestamp}.csv"
            shutil.copy2(self.training_data_file, backup_file)
            self.logger.info(f"Created backup: {backup_file}")

    def _update_integrity_tracking(self, df: pd.DataFrame, version: DataVersion):
        """Update data integrity tracking"""
        integrity_info = {
            "last_updated": datetime.now().isoformat(),
            "version_id": version.version_id,
            "transaction_count": len(df),
            "labeled_count": version.labeled_count,
            "data_hash": hashlib.md5(str(df.values.tobytes()).encode()).hexdigest(),
            "categories_count": len(version.categories),
            "integrity_checks": {
                "has_required_columns": self._check_required_columns(df),
                "no_duplicate_hashes": self._check_duplicate_hashes(df),
                "valid_categories": self._check_valid_categories(df)
            }
        }

        with open(self.integrity_file, 'w') as f:
            json.dump(integrity_info, f, indent=2)

    def _check_required_columns(self, df: pd.DataFrame) -> bool:
        """Check if all required columns are present"""
        required_columns = [
            'hash_id', 'description', 'category', 'sub_category',
            'is_manually_labeled', 'labeled_by', 'labeled_at'
        ]
        return all(col in df.columns for col in required_columns)

    def _check_duplicate_hashes(self, df: pd.DataFrame) -> bool:
        """Check for duplicate hash IDs"""
        if 'hash_id' in df.columns:
            return df['hash_id'].nunique() == len(df)
        return False

    def _check_valid_categories(self, df: pd.DataFrame) -> bool:
        """Check for valid category data"""
        if 'category' in df.columns:
            labeled_df = df[df['is_manually_labeled'] == True]
            if len(labeled_df) > 0:
                return not labeled_df['category'].isna().any()
        return True

    def get_version_history(self) -> List[DataVersion]:
        """Get complete version history"""
        try:
            with open(self.versions_file, 'r') as f:
                versions_data = json.load(f)

            return [DataVersion(**v) for v in versions_data["versions"]]
        except:
            return []

    def restore_version(self, version_id: str) -> bool:
        """Restore a specific version"""
        try:
            version_file = self.ml_data_dir / "versions" / f"{version_id}.csv"
            if version_file.exists():
                # Create backup first
                self._create_backup()

                # Restore version
                shutil.copy2(version_file, self.training_data_file)

                self.logger.info(f"Restored version: {version_id}")
                return True
            else:
                self.logger.error(f"Version file not found: {version_id}")
                return False
        except Exception as e:
            self.logger.error(f"Error restoring version: {str(e)}")
            return False
