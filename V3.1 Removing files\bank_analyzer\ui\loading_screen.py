"""
Loading screen component for Bank Statement Analyzer
Provides a modal loading screen with progress bar and status messages
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QProgressBar, QPushButton, QFrame, QWidget)
from PySide6.QtCore import Qt, QTimer, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush
import time


class LoadingScreen(QDialog):
    """
    Modal loading screen with progress bar and status messages
    """
    
    # Signals
    cancel_requested = Signal()
    
    def __init__(self, parent=None, title="Loading Data", show_cancel=True):
        super().__init__(parent)
        self.title = title
        self.show_cancel = show_cancel
        self.start_time = None
        self.is_cancelled = False
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Setup the loading screen UI"""
        self.setWindowTitle(self.title)
        self.setModal(True)
        self.setFixedSize(450, 220)
        self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # Set dialog background
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
            }
        """)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 25, 30, 25)
        
        # Title
        self.title_label = QLabel(self.title)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 16px;
        """)
        layout.addWidget(self.title_label)

        # Status message
        self.status_label = QLabel("Initializing...")
        status_font = QFont()
        status_font.setPointSize(12)
        self.status_label.setFont(status_font)
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            color: #34495e;
            font-size: 12px;
            font-weight: 500;
            margin: 5px 0px;
        """)
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setMinimumHeight(25)
        progress_font = QFont()
        progress_font.setPointSize(10)
        progress_font.setBold(True)
        self.progress_bar.setFont(progress_font)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 10px;
                color: #2c3e50;
                background-color: #ecf0f1;
                min-height: 25px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # Time and details
        details_layout = QHBoxLayout()

        self.time_label = QLabel("Elapsed: 0s")
        time_font = QFont()
        time_font.setPointSize(10)
        self.time_label.setFont(time_font)
        self.time_label.setStyleSheet("""
            color: #5d6d7e;
            font-size: 10px;
            font-weight: 500;
        """)
        details_layout.addWidget(self.time_label)

        details_layout.addStretch()

        self.details_label = QLabel("")
        details_font = QFont()
        details_font.setPointSize(10)
        self.details_label.setFont(details_font)
        self.details_label.setStyleSheet("""
            color: #5d6d7e;
            font-size: 10px;
            font-weight: 500;
        """)
        details_layout.addWidget(self.details_label)

        layout.addLayout(details_layout)
        
        # Cancel button (optional)
        if self.show_cancel:
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            
            self.cancel_button = QPushButton("Cancel")
            self.cancel_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
            self.cancel_button.clicked.connect(self.on_cancel_clicked)
            button_layout.addWidget(self.cancel_button)
            
            layout.addLayout(button_layout)
        
        # Timer for elapsed time updates
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_elapsed_time)
    
    def setup_animations(self):
        """Setup progress bar animations"""
        self.progress_animation = QPropertyAnimation(self.progress_bar, b"value")
        self.progress_animation.setDuration(300)
        self.progress_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def show_loading(self):
        """Show the loading screen and start timing"""
        self.start_time = time.time()
        self.is_cancelled = False
        self.timer.start(100)  # Update every 100ms
        self.show()
        self.raise_()
        self.activateWindow()
    
    def hide_loading(self):
        """Hide the loading screen and stop timing"""
        self.timer.stop()
        self.hide()
    
    def update_progress(self, value, status_message="", details=""):
        """Update progress bar and status messages"""
        if self.is_cancelled:
            return

        # Animate progress bar
        self.progress_animation.setStartValue(self.progress_bar.value())
        self.progress_animation.setEndValue(value)
        self.progress_animation.start()

        # Update status message with better visibility
        if status_message:
            self.status_label.setText(status_message)
            # Ensure text is visible by setting explicit styling
            self.status_label.setStyleSheet("""
                color: #2c3e50;
                font-size: 12px;
                font-weight: 600;
                margin: 5px 0px;
                background-color: transparent;
            """)

        # Update details with better visibility
        if details:
            self.details_label.setText(details)
            # Ensure details text is visible
            self.details_label.setStyleSheet("""
                color: #34495e;
                font-size: 10px;
                font-weight: 500;
                background-color: transparent;
            """)

        # Process events to keep UI responsive
        from PySide6.QtWidgets import QApplication
        QApplication.processEvents()
    
    def update_elapsed_time(self):
        """Update the elapsed time display"""
        if self.start_time:
            elapsed = time.time() - self.start_time
            self.time_label.setText(f"Elapsed: {elapsed:.1f}s")
            # Ensure time text is visible
            self.time_label.setStyleSheet("""
                color: #34495e;
                font-size: 10px;
                font-weight: 600;
                background-color: transparent;
            """)
    
    def on_cancel_clicked(self):
        """Handle cancel button click"""
        self.is_cancelled = True
        self.cancel_button.setEnabled(False)
        self.cancel_button.setText("Cancelling...")
        self.status_label.setText("Cancelling operation...")
        self.cancel_requested.emit()
    
    def set_title(self, title):
        """Update the loading screen title"""
        self.title = title
        self.setWindowTitle(title)
        self.title_label.setText(title)
    
    def is_cancel_requested(self):
        """Check if cancel was requested"""
        return self.is_cancelled


class DataLoadingScreen(LoadingScreen):
    """
    Specialized loading screen for data loading operations
    """
    
    def __init__(self, parent=None):
        super().__init__(parent, "Loading Transaction Data", show_cancel=True)
        self.setup_data_loading_ui()
    
    def setup_data_loading_ui(self):
        """Setup UI specific to data loading"""
        # Add data-specific styling with better contrast
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border: 3px solid #3498db;
                border-radius: 12px;
            }
            QLabel {
                color: #2c3e50;
            }
        """)
    
    def update_data_progress(self, stage, progress, message="", count=0, total=0):
        """Update progress for data loading stages"""
        stage_messages = {
            "loading": "Loading raw transaction data...",
            "processing": "Processing transactions...",
            "filtering": "Applying filters...",
            "preparing": "Preparing display...",
            "complete": "Loading complete!"
        }
        
        status_msg = stage_messages.get(stage, message or "Processing...")
        
        details = ""
        if count > 0 and total > 0:
            details = f"{count:,} / {total:,} items"
        elif count > 0:
            details = f"{count:,} items processed"
        
        self.update_progress(progress, status_msg, details)
