"""
Hybrid Prediction System Demonstration

This script demonstrates how to use the hybrid prediction system to achieve
perfect alignment with manual transaction labels while maintaining high
performance on new transactions.
"""

import time
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any

from .hybrid_prediction_engine import HybridPredictionEngine
from .performance_monitor import PerformanceMonitor
from .mismatch_analyzer import MLMismatchAnalyzer
from .manual_label_priority_system import ManualLabelPrioritySystem
from ..core.logger import get_logger


class HybridSystemDemo:
    """
    Demonstration of the hybrid prediction system capabilities
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.data_dir = data_dir
        
        # Initialize components
        self.hybrid_engine = HybridPredictionEngine(data_dir)
        self.performance_monitor = PerformanceMonitor(data_dir)
        self.mismatch_analyzer = MLMismatchAnalyzer(data_dir)
        self.manual_priority_system = ManualLabelPrioritySystem(data_dir)
        
        self.logger.info("Hybrid system demo initialized")
    
    def run_complete_demo(self):
        """Run complete demonstration of hybrid system capabilities"""
        print("=" * 80)
        print("HYBRID PREDICTION SYSTEM DEMONSTRATION")
        print("=" * 80)
        
        try:
            # Step 1: Analyze current mismatches
            print("\n1. ANALYZING CURRENT ML MODEL MISMATCHES")
            print("-" * 50)
            self.demo_mismatch_analysis()
            
            # Step 2: Initialize manual label priority system
            print("\n2. INITIALIZING MANUAL LABEL PRIORITY SYSTEM")
            print("-" * 50)
            self.demo_manual_priority_system()
            
            # Step 3: Train hybrid system
            print("\n3. TRAINING HYBRID PREDICTION SYSTEM")
            print("-" * 50)
            self.demo_hybrid_training()
            
            # Step 4: Demonstrate predictions
            print("\n4. DEMONSTRATING HYBRID PREDICTIONS")
            print("-" * 50)
            self.demo_hybrid_predictions()
            
            # Step 5: Performance monitoring
            print("\n5. PERFORMANCE MONITORING AND ANALYSIS")
            print("-" * 50)
            self.demo_performance_monitoring()
            
            # Step 6: Active learning recommendations
            print("\n6. ACTIVE LEARNING RECOMMENDATIONS")
            print("-" * 50)
            self.demo_active_learning()
            
            print("\n" + "=" * 80)
            print("DEMONSTRATION COMPLETED SUCCESSFULLY")
            print("=" * 80)
            
        except Exception as e:
            self.logger.error(f"Error in demo: {str(e)}")
            print(f"Demo failed with error: {str(e)}")
    
    def demo_mismatch_analysis(self):
        """Demonstrate mismatch analysis capabilities"""
        try:
            print("Running mismatch analysis...")
            
            # Analyze mismatches
            analysis_results = self.mismatch_analyzer.analyze_mismatches()
            
            print(f"✓ Total transactions analyzed: {analysis_results.total_transactions}")
            print(f"✓ Total mismatches found: {analysis_results.total_mismatches}")
            print(f"✓ Category mismatches: {analysis_results.category_mismatches}")
            print(f"✓ Subcategory mismatches: {analysis_results.subcategory_mismatches}")
            
            if analysis_results.total_transactions > 0:
                accuracy = ((analysis_results.total_transactions - analysis_results.total_mismatches) / 
                           analysis_results.total_transactions * 100)
                print(f"✓ Current ML accuracy: {accuracy:.2f}%")
            
            # Show top mismatch patterns
            print("\nTop mismatch patterns:")
            for i, pattern in enumerate(analysis_results.mismatch_patterns[:5], 1):
                print(f"  {i}. {pattern.pattern_type}: {pattern.pattern_value} "
                      f"(frequency: {pattern.frequency})")
                print(f"     ML: {pattern.ml_category}/{pattern.ml_subcategory}")
                print(f"     Manual: {pattern.manual_category}/{pattern.manual_subcategory}")
            
            # Show recommendations
            print("\nRecommendations:")
            for i, rec in enumerate(analysis_results.recommendations[:3], 1):
                print(f"  {i}. {rec}")
            
        except Exception as e:
            print(f"✗ Mismatch analysis failed: {str(e)}")
    
    def demo_manual_priority_system(self):
        """Demonstrate manual label priority system"""
        try:
            print("Initializing manual label priority system...")
            
            # Load manual labels
            labels_loaded = self.manual_priority_system.bulk_load_manual_labels(force_refresh=True)
            print(f"✓ Loaded {labels_loaded} manual labels into priority cache")
            
            # Get cache statistics
            cache_stats = self.manual_priority_system.get_cache_stats()
            print(f"✓ Exact cache size: {cache_stats['exact_cache_size']}")
            print(f"✓ Normalized cache size: {cache_stats['normalized_cache_size']}")
            print(f"✓ Fuzzy cache size: {cache_stats['fuzzy_cache_size']}")
            
            # Test lookup functionality
            print("\nTesting manual label lookup...")
            
            # Example lookups (you would use real transaction descriptions)
            test_descriptions = [
                "UPI-ZOMATO-12345",
                "ATM WITHDRAWAL SBI",
                "SALARY CREDIT COMPANY"
            ]
            
            for desc in test_descriptions:
                result = self.manual_priority_system.lookup_manual_label(desc)
                if result.found:
                    print(f"  ✓ Found manual label for '{desc[:30]}...': "
                          f"{result.entry.category}/{result.entry.sub_category} "
                          f"(confidence: {result.confidence:.3f})")
                else:
                    print(f"  - No manual label found for '{desc[:30]}...'")
            
        except Exception as e:
            print(f"✗ Manual priority system initialization failed: {str(e)}")
    
    def demo_hybrid_training(self):
        """Demonstrate hybrid system training"""
        try:
            print("Training hybrid prediction system...")
            
            # Train the hybrid system
            training_results = self.hybrid_engine.train_hybrid_system(force_retrain=True)
            
            print(f"✓ Manual cache refresh: {training_results.get('manual_cache_refresh', False)}")
            print(f"✓ Hierarchical training: {training_results.get('hierarchical_training', False)}")
            print(f"✓ Standard ML training: {training_results.get('standard_ml_training', False)}")
            print(f"✓ Overall success: {training_results.get('overall_success', False)}")
            
            if training_results.get('manual_labels_loaded'):
                print(f"✓ Manual labels loaded: {training_results['manual_labels_loaded']}")
            
            # Show any training errors
            if training_results.get('training_errors'):
                print("\nTraining warnings/errors:")
                for error in training_results['training_errors']:
                    print(f"  ⚠ {error}")
            
            # Validate manual label accuracy
            print("\nValidating manual label accuracy guarantee...")
            validation_results = self.hybrid_engine.validate_manual_label_accuracy()
            print(f"✓ Manual label accuracy maintained: {validation_results.get('accuracy_maintained', False)}")
            
        except Exception as e:
            print(f"✗ Hybrid training failed: {str(e)}")
    
    def demo_hybrid_predictions(self):
        """Demonstrate hybrid prediction capabilities"""
        try:
            print("Testing hybrid predictions...")
            
            # Example transactions for testing
            test_transactions = [
                {
                    "description": "UPI-ZOMATO-PAYMENT-12345",
                    "amount": -450.0,
                    "date": "2024-01-15",
                    "transaction_type": "debit"
                },
                {
                    "description": "SALARY CREDIT FROM COMPANY XYZ",
                    "amount": 75000.0,
                    "date": "2024-01-01",
                    "transaction_type": "credit"
                },
                {
                    "description": "ATM WITHDRAWAL SBI BRANCH",
                    "amount": -2000.0,
                    "date": "2024-01-10",
                    "transaction_type": "debit"
                },
                {
                    "description": "UNKNOWN MERCHANT PAYMENT",
                    "amount": -150.0,
                    "date": "2024-01-12",
                    "transaction_type": "debit"
                }
            ]
            
            print(f"Testing {len(test_transactions)} sample transactions...")
            
            for i, txn in enumerate(test_transactions, 1):
                start_time = time.time()
                
                # Make prediction
                result = self.hybrid_engine.predict(
                    description=txn["description"],
                    amount=txn["amount"],
                    transaction_date=pd.to_datetime(txn["date"]),
                    transaction_type=txn["transaction_type"]
                )
                
                processing_time = time.time() - start_time
                
                # Log prediction for monitoring
                self.performance_monitor.log_prediction(
                    txn["description"], result, processing_time
                )
                
                print(f"\n  Transaction {i}: {txn['description'][:40]}...")
                print(f"    Prediction: {result.category}/{result.sub_category}")
                print(f"    Confidence: {result.confidence:.3f}")
                print(f"    Source: {result.source.value}")
                print(f"    Processing time: {processing_time*1000:.1f}ms")
                
                if result.processing_notes:
                    print(f"    Notes: {'; '.join(result.processing_notes)}")
            
            # Show prediction statistics
            stats = self.hybrid_engine.get_prediction_stats()
            print(f"\nPrediction Statistics:")
            print(f"  Total predictions: {stats['total_predictions']}")
            print(f"  Manual accuracy: {stats['source_percentages']['manual_accuracy']:.1f}%")
            print(f"  ML coverage: {stats['source_percentages']['ml_coverage']:.1f}%")
            print(f"  Fallback rate: {stats['source_percentages']['fallback_rate']:.1f}%")
            
        except Exception as e:
            print(f"✗ Hybrid predictions demo failed: {str(e)}")
    
    def demo_performance_monitoring(self):
        """Demonstrate performance monitoring capabilities"""
        try:
            print("Analyzing system performance...")
            
            # Get current metrics
            current_metrics = self.performance_monitor.calculate_current_metrics()
            print(f"✓ Total predictions monitored: {current_metrics.total_predictions}")
            print(f"✓ Error rate: {current_metrics.error_rate:.2%}")
            print(f"✓ Manual hit rate: {current_metrics.manual_label_hit_rate:.2%}")
            print(f"✓ ML coverage: {current_metrics.ml_coverage:.2%}")
            print(f"✓ Fallback rate: {current_metrics.fallback_rate:.2%}")
            
            # Assess system health
            health_status = self.performance_monitor.assess_system_health()
            print(f"\nSystem Health: {health_status.status.upper()}")
            print(f"  Manual Priority System: {health_status.manual_priority_system}")
            print(f"  Hierarchical Classifier: {health_status.hierarchical_classifier}")
            print(f"  Standard ML System: {health_status.standard_ml_system}")
            print(f"  Overall Accuracy: {health_status.overall_accuracy:.2%}")
            
            if health_status.issues:
                print("\nIssues detected:")
                for issue in health_status.issues:
                    print(f"  ⚠ {issue}")
            
            if health_status.recommendations:
                print("\nRecommendations:")
                for rec in health_status.recommendations:
                    print(f"  → {rec}")
            
            # Generate performance report
            print("\nGenerating performance report...")
            report = self.performance_monitor.generate_performance_report(days_back=1)
            
            if "error" not in report:
                print(f"✓ Report generated for {report['report_period']['days_covered']} day(s)")
                print(f"✓ Total predictions in period: {report['summary_statistics']['total_predictions']}")
                print(f"✓ Average accuracy: {report['summary_statistics']['average_accuracy']:.2%}")
            
        except Exception as e:
            print(f"✗ Performance monitoring demo failed: {str(e)}")
    
    def demo_active_learning(self):
        """Demonstrate active learning recommendations"""
        try:
            print("Identifying active learning candidates...")
            
            # Example unlabeled transactions
            unlabeled_transactions = [
                {
                    "hash_id": "example1",
                    "description": "UNKNOWN PAYMENT GATEWAY TXN",
                    "amount": -299.0,
                    "date": "2024-01-15"
                },
                {
                    "hash_id": "example2", 
                    "description": "REFUND FROM MERCHANT ABC",
                    "amount": 150.0,
                    "date": "2024-01-16"
                }
            ]
            
            # Get active learning candidates
            candidates = self.hybrid_engine.get_active_learning_candidates(unlabeled_transactions)
            
            print(f"✓ Found {len(candidates)} candidates for manual labeling")
            
            for i, candidate in enumerate(candidates, 1):
                print(f"\n  Candidate {i}:")
                print(f"    Description: {candidate['description'][:50]}...")
                print(f"    Predicted: {candidate['predicted_category']}/{candidate['predicted_subcategory']}")
                print(f"    Confidence: {candidate['confidence']:.3f}")
                print(f"    Uncertainty: {candidate['uncertainty_score']:.3f}")
                print(f"    Issue: {candidate['disagreement_type']}")
                print(f"    Recommendation: {candidate['recommendation']}")
            
            if not candidates:
                print("  No high-uncertainty candidates found - system is performing well!")
            
        except Exception as e:
            print(f"✗ Active learning demo failed: {str(e)}")


def main():
    """Main demonstration function"""
    try:
        demo = HybridSystemDemo()
        demo.run_complete_demo()
    except Exception as e:
        print(f"Demo initialization failed: {str(e)}")


if __name__ == "__main__":
    main()
