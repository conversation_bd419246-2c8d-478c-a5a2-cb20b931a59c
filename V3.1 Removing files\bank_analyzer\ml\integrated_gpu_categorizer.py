"""
Integrated GPU Categorizer

This module integrates the GPU deep learning categorizer with the existing
bank analyzer system, providing seamless backward compatibility while
leveraging advanced ML capabilities.
"""

import os
import sys
import time
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import json
import logging
from datetime import datetime

# Add current directory to path for imports
current_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(current_dir))

from .intelligent_category_router import IntelligentCategoryRouter, CategorizationResult
from .gpu_training_pipeline import AdvancedTrainingPipeline
from .gpu_deep_learning_categorizer import GPUDeepLearningCategorizer
from ..core.logger import get_logger


class IntegratedGPUCategorizer:
    """
    Main integration class that provides a unified interface for the
    enhanced categorization system with GPU acceleration
    """
    
    def __init__(self, config_dir: str = "bank_analyzer_config"):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        
        # Initialize components
        self.router = IntelligentCategoryRouter(config_dir)
        self.training_pipeline = AdvancedTrainingPipeline(f"{config_dir}/ml_models")
        
        # Performance tracking
        self.performance_stats = {
            'total_categorizations': 0,
            'successful_categorizations': 0,
            'avg_processing_time': 0.0,
            'method_usage': {
                'manual_labels': 0,
                'gpu_ml': 0,
                'pattern_matching': 0,
                'fallback': 0
            },
            'accuracy_by_method': {
                'manual_labels': 1.0,  # Always 100% accurate
                'gpu_ml': 0.0,
                'pattern_matching': 0.0,
                'fallback': 0.0
            }
        }
        
        # System status
        self.system_status = {
            'gpu_available': self.router.gpu_model_available,
            'initialization_time': datetime.now(),
            'last_training': None,
            'model_version': None
        }
        
        self.logger.info("Integrated GPU Categorizer initialized")
        self.logger.info(f"GPU Model Available: {self.system_status['gpu_available']}")
    
    def categorize_transaction(self, transaction: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main categorization method with comprehensive result tracking
        """
        start_time = time.time()
        
        try:
            # Use intelligent router for categorization
            result = self.router.categorize_transaction(transaction)
            
            # Update performance statistics
            processing_time = time.time() - start_time
            self.update_performance_stats(result, processing_time, success=True)
            
            # Convert to standard format
            categorization_result = {
                'category': result.category,
                'sub_category': result.sub_category,
                'confidence': result.confidence,
                'method_used': result.method_used,
                'processing_time': result.processing_time,
                'success': True,
                'additional_info': result.additional_info
            }
            
            self.logger.debug(f"Categorized '{transaction.get('description', '')[:50]}...' as '{result.category}' using {result.method_used}")
            
            return categorization_result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Categorization failed: {e}")
            
            # Update stats for failure
            self.update_performance_stats(None, processing_time, success=False)
            
            return {
                'category': 'Uncategorized',
                'sub_category': 'Error',
                'confidence': 0.0,
                'method_used': 'Error',
                'processing_time': processing_time,
                'success': False,
                'error': str(e)
            }
    
    def categorize_batch(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Categorize multiple transactions efficiently
        """
        self.logger.info(f"Categorizing batch of {len(transactions)} transactions")
        
        results = []
        start_time = time.time()
        
        for i, transaction in enumerate(transactions):
            result = self.categorize_transaction(transaction)
            results.append(result)
            
            # Log progress for large batches
            if (i + 1) % 100 == 0:
                elapsed = time.time() - start_time
                rate = (i + 1) / elapsed
                self.logger.info(f"Processed {i + 1}/{len(transactions)} transactions ({rate:.1f} tx/sec)")
        
        total_time = time.time() - start_time
        self.logger.info(f"Batch categorization completed in {total_time:.2f}s")
        
        return results
    
    def train_gpu_model(self, csv_path: str, use_cross_validation: bool = True) -> Dict[str, Any]:
        """
        Train or retrain the GPU model with comprehensive validation
        """
        self.logger.info("Starting GPU model training...")
        
        try:
            if use_cross_validation:
                # Use cross-validation for robust training
                training_results = self.training_pipeline.train_with_cross_validation(csv_path)
            else:
                # Direct training
                training_results = self.router.retrain_gpu_model(csv_path)
            
            # Update system status
            self.system_status['last_training'] = datetime.now()
            self.system_status['gpu_available'] = True
            
            # Generate performance report
            if 'cross_validation_results' in training_results:
                report = self.training_pipeline.generate_performance_report(training_results)
                self.logger.info(f"Training completed:\n{report}")
            
            return {
                'success': True,
                'results': training_results,
                'target_achieved': training_results.get('target_achieved', False),
                'accuracy': training_results.get('average_accuracy', training_results.get('best_validation_accuracy', 0))
            }
            
        except Exception as e:
            self.logger.error(f"GPU model training failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def evaluate_system_performance(self, test_csv_path: str) -> Dict[str, Any]:
        """
        Comprehensive system performance evaluation
        """
        self.logger.info("Evaluating system performance...")
        
        try:
            # Evaluate GPU model performance
            gpu_results = self.training_pipeline.evaluate_real_world_performance(test_csv_path)
            
            # Get router performance summary
            router_summary = self.router.get_performance_summary()
            
            # Get overall system statistics
            system_stats = self.get_system_statistics()
            
            evaluation_results = {
                'gpu_model_performance': gpu_results,
                'router_performance': router_summary,
                'system_statistics': system_stats,
                'evaluation_date': datetime.now().isoformat(),
                'overall_score': self.calculate_overall_score(gpu_results, router_summary)
            }
            
            return evaluation_results
            
        except Exception as e:
            self.logger.error(f"Performance evaluation failed: {e}")
            return {'error': str(e)}
    
    def calculate_overall_score(self, gpu_results: Dict[str, Any], router_summary: Dict[str, Any]) -> float:
        """
        Calculate overall system performance score (0-100)
        """
        score = 0.0
        
        # GPU model contribution (40% of score)
        if 'overall_accuracy' in gpu_results:
            gpu_accuracy = gpu_results['overall_accuracy']
            score += gpu_accuracy * 40
        
        # Method distribution balance (30% of score)
        if 'method_distribution' in router_summary:
            dist = router_summary['method_distribution']
            # Prefer balanced usage with high manual label hits
            manual_ratio = dist.get('manual_labels', 0) / 100
            ml_ratio = dist.get('gpu_ml', 0) / 100
            
            # Ideal: 40% manual, 40% ML, 20% pattern matching
            balance_score = 1.0 - abs(manual_ratio - 0.4) - abs(ml_ratio - 0.4)
            score += max(0, balance_score) * 30
        
        # Processing speed (20% of score)
        if 'avg_processing_time' in gpu_results:
            # Target: <100ms per transaction
            avg_time = gpu_results['avg_processing_time']
            speed_score = max(0, 1.0 - (avg_time / 0.1))  # Normalize to 100ms
            score += speed_score * 20
        
        # System reliability (10% of score)
        success_rate = self.performance_stats['successful_categorizations'] / max(1, self.performance_stats['total_categorizations'])
        score += success_rate * 10
        
        return min(100.0, max(0.0, score))
    
    def update_performance_stats(self, result: Optional[CategorizationResult], processing_time: float, success: bool):
        """
        Update internal performance statistics
        """
        self.performance_stats['total_categorizations'] += 1
        
        if success:
            self.performance_stats['successful_categorizations'] += 1
            
            if result:
                # Update method usage
                method = result.method_used.lower().replace('_', '_')
                if method in self.performance_stats['method_usage']:
                    self.performance_stats['method_usage'][method] += 1
        
        # Update average processing time (exponential moving average)
        alpha = 0.1
        self.performance_stats['avg_processing_time'] = (
            alpha * processing_time + 
            (1 - alpha) * self.performance_stats['avg_processing_time']
        )
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive system statistics
        """
        return {
            'performance_stats': self.performance_stats,
            'system_status': self.system_status,
            'gpu_model_summary': self.router.gpu_categorizer.get_model_performance_summary() if self.router.gpu_model_available else None,
            'router_config': self.router.config,
            'uptime_seconds': (datetime.now() - self.system_status['initialization_time']).total_seconds()
        }
    
    def optimize_system(self):
        """
        Automatically optimize system parameters based on performance data
        """
        self.logger.info("Optimizing system parameters...")
        
        # Optimize router thresholds
        self.router.optimize_thresholds()
        
        # Save performance metrics
        self.router.save_performance_metrics()
        
        self.logger.info("System optimization completed")
    
    def get_categorization_strategy(self, category: str) -> Dict[str, Any]:
        """
        Get the current categorization strategy for a specific category
        """
        return self.router.get_category_routing_strategy(category)
    
    def backup_models(self) -> Dict[str, Any]:
        """
        Create backup of current models and configurations
        """
        backup_dir = self.config_dir / "ml_models" / f"backup_gpu_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # Copy model files
            model_files = [
                "gpu_deep_learning_model.pt",
                "gpu_label_encoder.joblib",
                "gpu_model_info.json",
                "category_readiness_stats.json"
            ]
            
            source_dir = self.config_dir / "ml_models"
            backed_up_files = []
            
            for file_name in model_files:
                source_file = source_dir / file_name
                if source_file.exists():
                    backup_file = backup_dir / file_name
                    backup_file.write_bytes(source_file.read_bytes())
                    backed_up_files.append(file_name)
            
            # Save system statistics
            stats_file = backup_dir / "system_stats.json"
            with open(stats_file, 'w') as f:
                json.dump(self.get_system_statistics(), f, indent=2, default=str)
            
            return {
                'success': True,
                'backup_dir': str(backup_dir),
                'files_backed_up': backed_up_files,
                'backup_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Model backup failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
