"""
Enhanced Feature Engineering for Transaction Categorization

This module provides advanced feature extraction to better capture patterns
that align with manual categorization logic.
"""

import re
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, date
from collections import Counter, defaultdict
import calendar

from ..core.logger import get_logger


@dataclass
class ExtractedFeatures:
    """Container for extracted transaction features"""
    # Merchant features
    merchant_name: Optional[str] = None
    merchant_category: Optional[str] = None
    merchant_confidence: float = 0.0
    
    # UPI-specific features
    upi_id: Optional[str] = None
    upi_provider: Optional[str] = None
    upi_merchant_code: Optional[str] = None
    
    # Temporal features
    day_of_week: int = 0
    day_of_month: int = 0
    month: int = 0
    is_weekend: bool = False
    is_month_end: bool = False
    is_salary_day: bool = False
    
    # Amount features
    amount_category: str = "unknown"
    amount_percentile: float = 0.0
    is_round_amount: bool = False
    amount_frequency_score: float = 0.0
    
    # Transaction type features
    payment_method: str = "unknown"
    transaction_channel: str = "unknown"
    is_recurring: bool = False
    
    # Text features
    description_length: int = 0
    word_count: int = 0
    has_numbers: bool = False
    has_special_chars: bool = False
    
    # Pattern features
    matches_salary_pattern: bool = False
    matches_utility_pattern: bool = False
    matches_shopping_pattern: bool = False
    matches_food_pattern: bool = False
    matches_transport_pattern: bool = False


class EnhancedFeatureEngineer:
    """
    Advanced feature engineering that mimics manual categorization patterns
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Merchant extraction patterns
        self.merchant_patterns = [
            # UPI patterns
            (r'UPI-(.+?)-(.+?)(?:-|$)', 'upi'),
            (r'UPI/(.+?)/(.+?)(?:/|$)', 'upi'),
            (r'UPI\s+(.+?)\s+(.+?)(?:\s|$)', 'upi'),
            
            # IMPS/NEFT patterns
            (r'IMPS-(.+?)-(.+?)(?:-|$)', 'imps'),
            (r'NEFT-(.+?)-(.+?)(?:-|$)', 'neft'),
            
            # Card patterns
            (r'POS\s+(.+?)(?:\s+\d|\s*$)', 'pos'),
            (r'(.+?)\s+ATM(?:\s|$)', 'atm'),
            
            # Online patterns
            (r'(.+?)\s+ONLINE(?:\s|$)', 'online'),
            (r'(.+?)\s+ECOM(?:\s|$)', 'ecom'),
        ]
        
        # Category patterns based on manual labeling logic
        self.category_patterns = {
            'salary': [
                r'.*SALARY.*', r'.*SAL\s+CR.*', r'.*PAYROLL.*',
                r'.*WAGES.*', r'.*INCOME.*'
            ],
            'utility': [
                r'.*ELECTRICITY.*', r'.*WATER.*', r'.*GAS.*',
                r'.*INTERNET.*', r'.*MOBILE.*', r'.*PHONE.*',
                r'.*BROADBAND.*', r'.*WIFI.*'
            ],
            'shopping': [
                r'.*AMAZON.*', r'.*FLIPKART.*', r'.*MYNTRA.*',
                r'.*SHOPPING.*', r'.*MALL.*', r'.*STORE.*',
                r'.*RETAIL.*', r'.*PURCHASE.*'
            ],
            'food': [
                r'.*ZOMATO.*', r'.*SWIGGY.*', r'.*RESTAURANT.*',
                r'.*FOOD.*', r'.*CAFE.*', r'.*HOTEL.*',
                r'.*DINING.*', r'.*KITCHEN.*'
            ],
            'transport': [
                r'.*UBER.*', r'.*OLA.*', r'.*TAXI.*',
                r'.*METRO.*', r'.*BUS.*', r'.*PETROL.*',
                r'.*FUEL.*', r'.*PARKING.*'
            ],
            'investment': [
                r'.*MUTUAL.*', r'.*SIP.*', r'.*INVESTMENT.*',
                r'.*EQUITY.*', r'.*FUND.*', r'.*TRADING.*'
            ],
            'insurance': [
                r'.*INSURANCE.*', r'.*PREMIUM.*', r'.*POLICY.*',
                r'.*LIC.*', r'.*HEALTH.*PLAN.*'
            ]
        }
        
        # Amount categories
        self.amount_ranges = [
            (0, 100, "micro"),
            (100, 500, "small"),
            (500, 2000, "medium"),
            (2000, 10000, "large"),
            (10000, 50000, "very_large"),
            (50000, float('inf'), "huge")
        ]
        
        # Common UPI providers
        self.upi_providers = {
            'paytm', 'phonepe', 'googlepay', 'bhim', 'amazonpay',
            'mobikwik', 'freecharge', 'paypal', 'whatsapp'
        }
        
        # Salary day patterns (common salary days)
        self.salary_days = {1, 2, 3, 28, 29, 30, 31}  # Beginning and end of month
        
    def extract_features(self, description: str, amount: float, 
                        transaction_date: date, transaction_type: str) -> ExtractedFeatures:
        """
        Extract comprehensive features from transaction data
        
        Args:
            description: Transaction description
            amount: Transaction amount
            transaction_date: Transaction date
            transaction_type: Transaction type (debit/credit)
            
        Returns:
            ExtractedFeatures object with all extracted features
        """
        features = ExtractedFeatures()
        
        try:
            # Extract merchant features
            self._extract_merchant_features(description, features)
            
            # Extract UPI-specific features
            self._extract_upi_features(description, features)
            
            # Extract temporal features
            self._extract_temporal_features(transaction_date, features)
            
            # Extract amount features
            self._extract_amount_features(amount, features)
            
            # Extract transaction type features
            self._extract_transaction_type_features(description, transaction_type, features)
            
            # Extract text features
            self._extract_text_features(description, features)
            
            # Extract pattern features
            self._extract_pattern_features(description, features)
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error extracting features: {str(e)}")
            return features
    
    def _extract_merchant_features(self, description: str, features: ExtractedFeatures):
        """Extract merchant-related features"""
        try:
            description_upper = description.upper().strip()
            
            best_match = None
            best_confidence = 0.0
            
            # Try each merchant pattern
            for pattern, method in self.merchant_patterns:
                match = re.search(pattern, description_upper)
                if match:
                    if method == 'upi' and len(match.groups()) >= 2:
                        # UPI format: UPI-MERCHANT-ID or UPI/MERCHANT/ID
                        merchant_name = match.group(1).strip()
                        merchant_id = match.group(2).strip()
                        
                        # Clean merchant name
                        merchant_name = self._clean_merchant_name(merchant_name)
                        
                        if len(merchant_name) > 2:
                            confidence = 0.9 if method == 'upi' else 0.7
                            if confidence > best_confidence:
                                best_match = merchant_name
                                best_confidence = confidence
                                features.upi_id = merchant_id
                    
                    elif len(match.groups()) >= 1:
                        # Other patterns
                        merchant_name = match.group(1).strip()
                        merchant_name = self._clean_merchant_name(merchant_name)
                        
                        if len(merchant_name) > 2:
                            confidence = 0.8 if method in ['pos', 'online'] else 0.6
                            if confidence > best_confidence:
                                best_match = merchant_name
                                best_confidence = confidence
            
            # Set merchant features
            if best_match:
                features.merchant_name = best_match
                features.merchant_confidence = best_confidence
                features.merchant_category = self._categorize_merchant(best_match)
            
        except Exception as e:
            self.logger.debug(f"Error extracting merchant features: {str(e)}")
    
    def _extract_upi_features(self, description: str, features: ExtractedFeatures):
        """Extract UPI-specific features"""
        try:
            if 'UPI' not in description.upper():
                return
            
            description_lower = description.lower()
            
            # Detect UPI provider
            for provider in self.upi_providers:
                if provider in description_lower:
                    features.upi_provider = provider
                    break
            
            # Extract merchant code from UPI string
            upi_match = re.search(r'UPI[/-]([^-/\s]+)', description.upper())
            if upi_match:
                features.upi_merchant_code = upi_match.group(1)
            
        except Exception as e:
            self.logger.debug(f"Error extracting UPI features: {str(e)}")
    
    def _extract_temporal_features(self, transaction_date: date, features: ExtractedFeatures):
        """Extract temporal features"""
        try:
            features.day_of_week = transaction_date.weekday()  # 0=Monday, 6=Sunday
            features.day_of_month = transaction_date.day
            features.month = transaction_date.month
            
            # Weekend check
            features.is_weekend = features.day_of_week >= 5  # Saturday=5, Sunday=6
            
            # Month end check (last 3 days of month)
            last_day = calendar.monthrange(transaction_date.year, transaction_date.month)[1]
            features.is_month_end = features.day_of_month >= (last_day - 2)
            
            # Salary day check
            features.is_salary_day = features.day_of_month in self.salary_days
            
        except Exception as e:
            self.logger.debug(f"Error extracting temporal features: {str(e)}")
    
    def _extract_amount_features(self, amount: float, features: ExtractedFeatures):
        """Extract amount-related features"""
        try:
            abs_amount = abs(amount)
            
            # Amount category
            for min_amt, max_amt, category in self.amount_ranges:
                if min_amt <= abs_amount < max_amt:
                    features.amount_category = category
                    break
            
            # Round amount check (ends in 00, 50, etc.)
            features.is_round_amount = (abs_amount % 50 == 0) or (abs_amount % 100 == 0)
            
            # Amount percentile (would need historical data for accurate calculation)
            # For now, use a simple heuristic
            if abs_amount < 100:
                features.amount_percentile = 0.2
            elif abs_amount < 1000:
                features.amount_percentile = 0.5
            elif abs_amount < 5000:
                features.amount_percentile = 0.8
            else:
                features.amount_percentile = 0.95
            
        except Exception as e:
            self.logger.debug(f"Error extracting amount features: {str(e)}")
    
    def _extract_transaction_type_features(self, description: str, 
                                         transaction_type: str, features: ExtractedFeatures):
        """Extract transaction type and method features"""
        try:
            description_upper = description.upper()
            
            # Payment method detection
            if 'UPI' in description_upper:
                features.payment_method = 'upi'
            elif 'ATM' in description_upper:
                features.payment_method = 'atm'
            elif 'POS' in description_upper:
                features.payment_method = 'pos'
            elif any(x in description_upper for x in ['NEFT', 'RTGS', 'IMPS']):
                features.payment_method = 'bank_transfer'
            elif 'CHEQUE' in description_upper:
                features.payment_method = 'cheque'
            else:
                features.payment_method = 'other'
            
            # Transaction channel
            if any(x in description_upper for x in ['ONLINE', 'INTERNET', 'WEB']):
                features.transaction_channel = 'online'
            elif any(x in description_upper for x in ['MOBILE', 'APP']):
                features.transaction_channel = 'mobile'
            elif any(x in description_upper for x in ['ATM', 'POS']):
                features.transaction_channel = 'physical'
            else:
                features.transaction_channel = 'unknown'
            
        except Exception as e:
            self.logger.debug(f"Error extracting transaction type features: {str(e)}")
    
    def _extract_text_features(self, description: str, features: ExtractedFeatures):
        """Extract text-based features"""
        try:
            features.description_length = len(description)
            features.word_count = len(description.split())
            features.has_numbers = bool(re.search(r'\d', description))
            features.has_special_chars = bool(re.search(r'[^a-zA-Z0-9\s]', description))
            
        except Exception as e:
            self.logger.debug(f"Error extracting text features: {str(e)}")
    
    def _extract_pattern_features(self, description: str, features: ExtractedFeatures):
        """Extract pattern-based features that match manual categorization logic"""
        try:
            description_upper = description.upper()
            
            # Check each category pattern
            for category, patterns in self.category_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, description_upper):
                        setattr(features, f'matches_{category}_pattern', True)
                        break
            
        except Exception as e:
            self.logger.debug(f"Error extracting pattern features: {str(e)}")
    
    def _clean_merchant_name(self, merchant_name: str) -> str:
        """Clean and normalize merchant name"""
        try:
            # Remove common prefixes/suffixes
            cleaned = merchant_name.strip()
            
            # Remove common UPI suffixes
            suffixes_to_remove = ['PAYTM', 'PHONEPE', 'GOOGLEPAY', 'BHIM', 'UPI']
            for suffix in suffixes_to_remove:
                if cleaned.endswith(suffix):
                    cleaned = cleaned[:-len(suffix)].strip()
            
            # Remove special characters except spaces
            cleaned = re.sub(r'[^A-Z0-9\s]', '', cleaned)
            
            # Normalize whitespace
            cleaned = ' '.join(cleaned.split())
            
            return cleaned
            
        except Exception as e:
            self.logger.debug(f"Error cleaning merchant name: {str(e)}")
            return merchant_name
    
    def _categorize_merchant(self, merchant_name: str) -> Optional[str]:
        """Categorize merchant based on name patterns"""
        try:
            merchant_upper = merchant_name.upper()
            
            # Check against known merchant categories
            merchant_categories = {
                'food': ['ZOMATO', 'SWIGGY', 'DOMINOS', 'PIZZA', 'RESTAURANT', 'CAFE'],
                'shopping': ['AMAZON', 'FLIPKART', 'MYNTRA', 'AJIO', 'NYKAA'],
                'transport': ['UBER', 'OLA', 'RAPIDO', 'METRO', 'PETROL'],
                'utility': ['AIRTEL', 'JIO', 'VODAFONE', 'BSNL', 'ELECTRICITY'],
                'entertainment': ['NETFLIX', 'HOTSTAR', 'PRIME', 'SPOTIFY', 'YOUTUBE'],
                'finance': ['PAYTM', 'PHONEPE', 'GOOGLEPAY', 'BANK', 'MUTUAL']
            }
            
            for category, keywords in merchant_categories.items():
                for keyword in keywords:
                    if keyword in merchant_upper:
                        return category
            
            return None
            
        except Exception as e:
            self.logger.debug(f"Error categorizing merchant: {str(e)}")
            return None

    def features_to_vector(self, features: ExtractedFeatures) -> np.ndarray:
        """
        Convert extracted features to numerical vector for ML

        Args:
            features: ExtractedFeatures object

        Returns:
            Numerical feature vector
        """
        try:
            vector = []

            # Merchant features (one-hot encoded)
            merchant_categories = ['food', 'shopping', 'transport', 'utility', 'entertainment', 'finance']
            for cat in merchant_categories:
                vector.append(1.0 if features.merchant_category == cat else 0.0)

            vector.append(features.merchant_confidence)

            # UPI features
            vector.append(1.0 if features.upi_provider else 0.0)

            # Temporal features
            vector.append(features.day_of_week / 6.0)  # Normalize to 0-1
            vector.append(features.day_of_month / 31.0)  # Normalize to 0-1
            vector.append(features.month / 12.0)  # Normalize to 0-1
            vector.append(1.0 if features.is_weekend else 0.0)
            vector.append(1.0 if features.is_month_end else 0.0)
            vector.append(1.0 if features.is_salary_day else 0.0)

            # Amount features
            amount_categories = ['micro', 'small', 'medium', 'large', 'very_large', 'huge']
            for cat in amount_categories:
                vector.append(1.0 if features.amount_category == cat else 0.0)

            vector.append(features.amount_percentile)
            vector.append(1.0 if features.is_round_amount else 0.0)

            # Payment method features
            payment_methods = ['upi', 'atm', 'pos', 'bank_transfer', 'cheque', 'other']
            for method in payment_methods:
                vector.append(1.0 if features.payment_method == method else 0.0)

            # Transaction channel features
            channels = ['online', 'mobile', 'physical', 'unknown']
            for channel in channels:
                vector.append(1.0 if features.transaction_channel == channel else 0.0)

            # Text features
            vector.append(min(features.description_length / 100.0, 1.0))  # Normalize, cap at 1
            vector.append(min(features.word_count / 20.0, 1.0))  # Normalize, cap at 1
            vector.append(1.0 if features.has_numbers else 0.0)
            vector.append(1.0 if features.has_special_chars else 0.0)

            # Pattern features
            pattern_features = [
                'matches_salary_pattern', 'matches_utility_pattern', 'matches_shopping_pattern',
                'matches_food_pattern', 'matches_transport_pattern'
            ]
            for pattern_attr in pattern_features:
                vector.append(1.0 if getattr(features, pattern_attr, False) else 0.0)

            return np.array(vector, dtype=np.float32)

        except Exception as e:
            self.logger.error(f"Error converting features to vector: {str(e)}")
            # Return zero vector as fallback
            return np.zeros(50, dtype=np.float32)  # Adjust size as needed

    def get_feature_names(self) -> List[str]:
        """Get names of all features in the vector"""
        names = []

        # Merchant category features
        merchant_categories = ['food', 'shopping', 'transport', 'utility', 'entertainment', 'finance']
        names.extend([f'merchant_cat_{cat}' for cat in merchant_categories])
        names.append('merchant_confidence')

        # UPI features
        names.append('has_upi_provider')

        # Temporal features
        names.extend(['day_of_week_norm', 'day_of_month_norm', 'month_norm',
                     'is_weekend', 'is_month_end', 'is_salary_day'])

        # Amount features
        amount_categories = ['micro', 'small', 'medium', 'large', 'very_large', 'huge']
        names.extend([f'amount_cat_{cat}' for cat in amount_categories])
        names.extend(['amount_percentile', 'is_round_amount'])

        # Payment method features
        payment_methods = ['upi', 'atm', 'pos', 'bank_transfer', 'cheque', 'other']
        names.extend([f'payment_method_{method}' for method in payment_methods])

        # Transaction channel features
        channels = ['online', 'mobile', 'physical', 'unknown']
        names.extend([f'channel_{channel}' for channel in channels])

        # Text features
        names.extend(['description_length_norm', 'word_count_norm', 'has_numbers', 'has_special_chars'])

        # Pattern features
        names.extend(['matches_salary_pattern', 'matches_utility_pattern', 'matches_shopping_pattern',
                     'matches_food_pattern', 'matches_transport_pattern'])

        return names

    def batch_extract_features(self, transactions_df: pd.DataFrame) -> pd.DataFrame:
        """
        Extract features for a batch of transactions

        Args:
            transactions_df: DataFrame with columns: description, amount, date, transaction_type

        Returns:
            DataFrame with extracted features
        """
        try:
            feature_rows = []

            for _, row in transactions_df.iterrows():
                # Extract features
                features = self.extract_features(
                    description=row['description'],
                    amount=float(row['amount']),
                    transaction_date=pd.to_datetime(row['date']).date(),
                    transaction_type=row.get('transaction_type', 'unknown')
                )

                # Convert to dictionary
                feature_dict = {
                    'hash_id': row.get('hash_id', ''),
                    'merchant_name': features.merchant_name,
                    'merchant_category': features.merchant_category,
                    'merchant_confidence': features.merchant_confidence,
                    'upi_provider': features.upi_provider,
                    'payment_method': features.payment_method,
                    'transaction_channel': features.transaction_channel,
                    'amount_category': features.amount_category,
                    'is_weekend': features.is_weekend,
                    'is_month_end': features.is_month_end,
                    'is_salary_day': features.is_salary_day,
                    'matches_salary_pattern': features.matches_salary_pattern,
                    'matches_utility_pattern': features.matches_utility_pattern,
                    'matches_shopping_pattern': features.matches_shopping_pattern,
                    'matches_food_pattern': features.matches_food_pattern,
                    'matches_transport_pattern': features.matches_transport_pattern,
                }

                # Add feature vector
                feature_vector = self.features_to_vector(features)
                for i, feature_name in enumerate(self.get_feature_names()):
                    feature_dict[f'feature_{feature_name}'] = feature_vector[i]

                feature_rows.append(feature_dict)

            return pd.DataFrame(feature_rows)

        except Exception as e:
            self.logger.error(f"Error in batch feature extraction: {str(e)}")
            return pd.DataFrame()

    def analyze_feature_importance(self, transactions_df: pd.DataFrame,
                                 target_column: str = 'category') -> Dict[str, float]:
        """
        Analyze which features are most important for categorization

        Args:
            transactions_df: DataFrame with transactions and labels
            target_column: Column name for target labels

        Returns:
            Dictionary of feature importance scores
        """
        try:
            # Extract features for all transactions
            features_df = self.batch_extract_features(transactions_df)

            if features_df.empty or target_column not in transactions_df.columns:
                return {}

            # Simple correlation-based importance
            feature_cols = [col for col in features_df.columns if col.startswith('feature_')]
            importance_scores = {}

            for feature_col in feature_cols:
                # Calculate correlation with target (simplified)
                feature_name = feature_col.replace('feature_', '')

                # For categorical targets, use frequency-based importance
                feature_values = features_df[feature_col].values
                target_values = transactions_df[target_column].values

                # Simple importance based on variance and target correlation
                if len(set(feature_values)) > 1:  # Feature has variance
                    importance = np.var(feature_values) * 0.5 + np.mean(feature_values) * 0.5
                else:
                    importance = 0.0

                importance_scores[feature_name] = importance

            # Sort by importance
            sorted_importance = dict(sorted(importance_scores.items(),
                                          key=lambda x: x[1], reverse=True))

            return sorted_importance

        except Exception as e:
            self.logger.error(f"Error analyzing feature importance: {str(e)}")
            return {}
