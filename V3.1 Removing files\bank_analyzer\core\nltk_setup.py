"""
NLTK Setup and Initialization
Handles NLTK data setup with graceful fallback for offline operation
"""

import os
import sys
from pathlib import Path
import logging

from .logger import get_logger


def setup_nltk_offline():
    """
    Setup NLTK for offline operation with graceful fallback
    
    This function ensures the application can start even without internet connectivity
    by setting up NLTK data paths and handling download failures gracefully.
    """
    logger = get_logger(__name__)
    
    try:
        import nltk
        
        # Get the bundled NLTK data path
        current_dir = Path(__file__).parent.parent
        bundled_nltk_path = current_dir / "ml" / "nltk_data"
        
        # Add bundled data path to NLTK search paths
        if bundled_nltk_path.exists():
            bundled_path_str = str(bundled_nltk_path)
            if bundled_path_str not in nltk.data.path:
                nltk.data.path.insert(0, bundled_path_str)
                logger.info(f"Added bundled NLTK data path: {bundled_path_str}")
        
        # Try to download essential data with timeout (optional)
        try:
            import socket
            original_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(5)  # Short timeout
            
            # Try to download quietly, but don't fail if it doesn't work
            nltk.download('stopwords', quiet=True)
            nltk.download('punkt', quiet=True)
            logger.info("Successfully downloaded NLTK data")
            
            socket.setdefaulttimeout(original_timeout)
            
        except Exception as e:
            logger.info(f"NLTK download failed (using offline data): {e}")
            # This is fine - we'll use bundled data or fallback
        
        return True
        
    except ImportError:
        logger.info("NLTK not available - using offline text processing")
        return False
    except Exception as e:
        logger.warning(f"NLTK setup failed: {e} - using offline text processing")
        return False


def verify_nltk_data():
    """
    Verify that NLTK data is available (either downloaded or bundled)
    
    Returns:
        dict: Status of NLTK data availability
    """
    logger = get_logger(__name__)
    status = {
        'nltk_available': False,
        'stopwords_available': False,
        'punkt_available': False,
        'using_bundled_data': False,
        'data_source': 'none'
    }
    
    try:
        import nltk
        status['nltk_available'] = True
        
        # Check stopwords
        try:
            from nltk.corpus import stopwords
            stopwords.words('english')
            status['stopwords_available'] = True
            status['data_source'] = 'nltk'
        except Exception:
            # Check if bundled data is available
            current_dir = Path(__file__).parent.parent
            bundled_stopwords = current_dir / "ml" / "nltk_data" / "corpora" / "stopwords" / "english"
            if bundled_stopwords.exists():
                status['stopwords_available'] = True
                status['using_bundled_data'] = True
                status['data_source'] = 'bundled'
        
        # Check punkt tokenizer
        try:
            from nltk.tokenize import word_tokenize
            word_tokenize("test")
            status['punkt_available'] = True
        except Exception:
            # Punkt not critical - we have fallback tokenization
            pass
        
    except ImportError:
        logger.info("NLTK not installed")
    
    return status


def initialize_text_processing():
    """
    Initialize text processing components with appropriate fallbacks
    
    Returns:
        dict: Configuration for text processing
    """
    logger = get_logger(__name__)
    
    # Setup NLTK if available
    nltk_setup_success = setup_nltk_offline()
    
    # Verify data availability
    nltk_status = verify_nltk_data()
    
    config = {
        'nltk_setup_success': nltk_setup_success,
        'nltk_status': nltk_status,
        'text_processing_mode': 'offline'  # Default to offline mode
    }
    
    if nltk_status['stopwords_available']:
        if nltk_status['using_bundled_data']:
            config['text_processing_mode'] = 'bundled_nltk'
            logger.info("Using bundled NLTK data for text processing")
        else:
            config['text_processing_mode'] = 'full_nltk'
            logger.info("Using full NLTK for text processing")
    else:
        logger.info("Using offline text processing (no NLTK data)")
    
    return config


def get_text_processing_status():
    """
    Get current text processing status for diagnostics
    
    Returns:
        str: Human-readable status message
    """
    try:
        config = initialize_text_processing()
        status = config['nltk_status']
        
        if config['text_processing_mode'] == 'full_nltk':
            return "✅ Full NLTK text processing available"
        elif config['text_processing_mode'] == 'bundled_nltk':
            return "✅ Bundled NLTK data available (offline mode)"
        else:
            return "⚠️ Using basic text processing (NLTK data unavailable)"
            
    except Exception as e:
        return f"❌ Text processing setup failed: {e}"


# Global configuration - initialized when needed
_text_config = None

def get_text_config():
    """Get or initialize text configuration"""
    global _text_config
    if _text_config is None:
        try:
            _text_config = initialize_text_processing()
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"Text processing initialization failed: {e}")
            _text_config = {
                'nltk_setup_success': False,
                'text_processing_mode': 'offline',
                'nltk_status': {'nltk_available': False}
            }
    return _text_config
