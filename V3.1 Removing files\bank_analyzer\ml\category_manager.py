"""
Category management system for ML-based transaction categorization
Handles hierarchical categories, custom categories, and category validation
"""

import pandas as pd
import json
import threading
import time
from pathlib import Path
from typing import List, Dict, Optional, Set, Tuple, Any
from datetime import datetime
from dataclasses import dataclass, field
import logging

from ..core.logger import get_logger


@dataclass
class Category:
    """Represents a transaction category"""
    id: str
    name: str
    parent_id: Optional[str] = None
    description: str = ""
    is_active: bool = True
    is_system: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    color: str = "#007ACC"  # Default color
    icon: str = "folder"  # Default icon
    category_type: str = "expense"  # "expense", "income", or "both" for categories that can be used for either


@dataclass
class CategoryHierarchy:
    """Represents the category hierarchy"""
    categories: Dict[str, Category] = field(default_factory=dict)
    hierarchy: Dict[str, List[str]] = field(default_factory=dict)  # parent_id -> [child_ids]


class CategoryManager:
    """
    Manages transaction categories with hierarchical support
    Integrates with existing category system and provides ML-specific features
    """

    def __init__(self, main_app_data_dir: str = "data", ml_data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.main_app_data_dir = Path(main_app_data_dir)
        self.ml_data_dir = Path(ml_data_dir)
        self.ml_data_dir.mkdir(parents=True, exist_ok=True)

        # File paths
        self.main_categories_file = self.main_app_data_dir / "expenses" / "categories.csv"
        self.ml_categories_file = self.ml_data_dir / "ml_categories.json"
        self.category_mappings_file = self.ml_data_dir / "category_mappings.json"
        self.main_category_metadata_file = self.ml_data_dir / "main_category_metadata.json"

        # Category hierarchy
        self.hierarchy = CategoryHierarchy()

        # Performance optimization: Caching and file monitoring
        self._cache_valid = False
        self._last_csv_mtime = 0
        self._last_json_mtime = 0
        self._refresh_lock = threading.Lock()
        self._refresh_pending = False

        # Load main category metadata
        self.main_category_metadata = self._load_main_category_metadata()

        # Load categories
        self._load_categories()

    def refresh_categories(self, force: bool = False):
        """Refresh categories from all sources with intelligent caching"""
        with self._refresh_lock:
            if self._refresh_pending:
                return  # Another refresh is already pending

            if not force and self._is_cache_valid():
                self.logger.debug("Categories cache is valid, skipping refresh")
                return

            self.logger.info("Refreshing categories...")
            self.hierarchy = CategoryHierarchy()
            # Reload main category metadata
            self.main_category_metadata = self._load_main_category_metadata()
            self._load_categories()
            self._cache_valid = True
            self.logger.info("Categories refreshed successfully")

    def force_refresh_from_files(self):
        """Force a complete refresh by reloading from files"""
        with self._refresh_lock:
            self.logger.info("Force refreshing categories from files...")

            # Invalidate cache
            self._cache_valid = False

            # Clear current hierarchy
            self.hierarchy = CategoryHierarchy()

            # Reload from files
            self._load_categories()
            self._cache_valid = True

            self.logger.info(f"Force refresh complete - loaded {len(self.hierarchy.categories)} categories")

    def _is_cache_valid(self) -> bool:
        """Check if the current cache is still valid based on file modification times"""
        try:
            if not self._cache_valid:
                return False

            # Check CSV file modification time
            if self.main_categories_file.exists():
                csv_mtime = self.main_categories_file.stat().st_mtime
                if csv_mtime > self._last_csv_mtime:
                    return False

            # Check JSON file modification time
            if self.ml_categories_file.exists():
                json_mtime = self.ml_categories_file.stat().st_mtime
                if json_mtime > self._last_json_mtime:
                    return False

            return True

        except Exception as e:
            self.logger.debug(f"Error checking cache validity: {e}")
            return False

    def _update_file_timestamps(self):
        """Update stored file modification timestamps"""
        try:
            if self.main_categories_file.exists():
                self._last_csv_mtime = self.main_categories_file.stat().st_mtime

            if self.ml_categories_file.exists():
                self._last_json_mtime = self.ml_categories_file.stat().st_mtime

        except Exception as e:
            self.logger.debug(f"Error updating file timestamps: {e}")
    
    def _load_categories(self):
        """Load categories from both main app and ML-specific sources"""
        start_time = time.time()

        # Load from main application
        self._load_main_app_categories()

        # Load ML-specific categories
        self._load_ml_categories()

        # Build hierarchy
        self._build_hierarchy()

        # Update file timestamps for cache validation
        self._update_file_timestamps()

        load_time = time.time() - start_time
        self.logger.debug(f"Category loading completed in {load_time:.3f} seconds")

    def _create_default_categories_file(self):
        """Create a default categories file if it doesn't exist"""
        try:
            # Ensure the directory exists
            self.main_categories_file.parent.mkdir(parents=True, exist_ok=True)

            # Create default categories content
            default_categories = """id,category,sub_category,is_active,category_type,color,description
1,Food & Dining,Restaurants,True,expense,#FF6B6B,Dining out at restaurants
2,Food & Dining,Groceries,True,expense,#FF6B6B,Grocery shopping
3,Transportation,Fuel,True,expense,#4ECDC4,Petrol and diesel
4,Transportation,Public Transport,True,expense,#4ECDC4,Bus and train fares
5,Shopping,Clothing,True,expense,#45B7D1,Clothes and accessories
6,Shopping,Electronics,True,expense,#45B7D1,Gadgets and electronics
7,Entertainment,Movies,True,expense,#96CEB4,Cinema and streaming
8,Bills & Utilities,Electricity,True,expense,#FFEAA7,Power bills
9,Bills & Utilities,Internet,True,expense,#FFEAA7,Internet and broadband
10,Healthcare,Medical,True,expense,#DDA0DD,Doctor visits and medical
11,Income,Salary,True,income,#58D68D,Regular salary
12,Transfer,Bank Transfer,True,both,#AED6F1,Money transfers"""

            # Write the file
            with open(self.main_categories_file, 'w', encoding='utf-8') as f:
                f.write(default_categories)

            self.logger.info(f"Created default categories file: {self.main_categories_file}")

        except Exception as e:
            self.logger.error(f"Failed to create default categories file: {str(e)}")

    def _load_main_app_categories(self):
        """Load categories from main application"""
        if not self.main_categories_file.exists():
            self.logger.warning(f"Main application categories file not found: {self.main_categories_file}")
            self.logger.info("Creating default categories file...")
            self._create_default_categories_file()
            if not self.main_categories_file.exists():
                self.logger.error("Failed to create default categories file")
                return

        try:
            df = pd.read_csv(self.main_categories_file)

            for _, row in df.iterrows():
                category_id = f"main_{row['id']}"

                # Create main category if not exists
                main_category_name = row['category']
                main_category_id = f"main_cat_{main_category_name.lower().replace(' ', '_')}"

                if main_category_id not in self.hierarchy.categories:
                    # Check if we have saved metadata for this category
                    metadata = self.main_category_metadata.get(main_category_name, {})

                    # Use saved category type or infer for backward compatibility
                    category_type = metadata.get('category_type', self._infer_category_type_from_name(main_category_name))
                    color = metadata.get('color', '#007ACC')
                    description = metadata.get('description', '')

                    # Handle datetime conversion safely
                    created_at_value = row.get('created_at', datetime.now())
                    if isinstance(created_at_value, str):
                        try:
                            created_at = pd.to_datetime(created_at_value)
                        except:
                            created_at = datetime.now()
                    elif isinstance(created_at_value, datetime):
                        created_at = created_at_value
                    else:
                        created_at = datetime.now()

                    self.hierarchy.categories[main_category_id] = Category(
                        id=main_category_id,
                        name=main_category_name,
                        is_system=True,
                        created_at=created_at,
                        category_type=category_type,
                        color=color,
                        description=description
                    )

                # Create subcategory (inherit category type from parent)
                parent_category = self.hierarchy.categories.get(main_category_id)
                parent_category_type = parent_category.category_type if parent_category else "expense"

                # Handle datetime conversion safely for subcategory
                created_at_value = row.get('created_at', datetime.now())
                if isinstance(created_at_value, str):
                    try:
                        created_at = pd.to_datetime(created_at_value)
                    except:
                        created_at = datetime.now()
                elif isinstance(created_at_value, datetime):
                    created_at = created_at_value
                else:
                    created_at = datetime.now()

                sub_category = Category(
                    id=category_id,
                    name=row['sub_category'],
                    parent_id=main_category_id,
                    is_active=row.get('is_active', True),
                    is_system=True,
                    created_at=created_at,
                    category_type=parent_category_type
                )

                self.hierarchy.categories[category_id] = sub_category

            self.logger.info(f"Loaded {len(df)} categories from main application")

        except Exception as e:
            self.logger.error(f"Error loading main app categories: {str(e)}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
    
    def _load_ml_categories(self):
        """Load ML-specific categories"""
        if not self.ml_categories_file.exists():
            return
        
        try:
            with open(self.ml_categories_file, 'r') as f:
                ml_categories_data = json.load(f)
            
            for cat_data in ml_categories_data:
                category = Category(
                    id=cat_data['id'],
                    name=cat_data['name'],
                    parent_id=cat_data.get('parent_id'),
                    description=cat_data.get('description', ''),
                    is_active=cat_data.get('is_active', True),
                    is_system=cat_data.get('is_system', False),
                    created_at=datetime.fromisoformat(cat_data['created_at']) if cat_data.get('created_at') else None,
                    updated_at=datetime.fromisoformat(cat_data['updated_at']) if cat_data.get('updated_at') else None,
                    color=cat_data.get('color', '#007ACC'),
                    icon=cat_data.get('icon', 'folder'),
                    category_type=cat_data.get('category_type', 'expense')  # Default to expense for backward compatibility
                )
                
                self.hierarchy.categories[category.id] = category
            
            self.logger.info(f"Loaded {len(ml_categories_data)} ML-specific categories")
            
        except Exception as e:
            self.logger.error(f"Error loading ML categories: {str(e)}")
    
    def _build_hierarchy(self):
        """Build category hierarchy mapping"""
        self.hierarchy.hierarchy = {}
        
        for category in self.hierarchy.categories.values():
            if category.parent_id:
                if category.parent_id not in self.hierarchy.hierarchy:
                    self.hierarchy.hierarchy[category.parent_id] = []
                self.hierarchy.hierarchy[category.parent_id].append(category.id)
    
    def get_all_categories(self, include_inactive: bool = False) -> List[Category]:
        """
        Get all categories
        
        Args:
            include_inactive: Whether to include inactive categories
            
        Returns:
            List of categories
        """
        categories = list(self.hierarchy.categories.values())
        
        if not include_inactive:
            categories = [cat for cat in categories if cat.is_active]
        
        return sorted(categories, key=lambda x: x.name)
    
    def get_main_categories(self, category_type: Optional[str] = None) -> List[Category]:
        """
        Get top-level categories (no parent)

        Args:
            category_type: Filter by category type ("expense", "income", "both", or None for all)
        """
        categories = [
            cat for cat in self.hierarchy.categories.values()
            if cat.parent_id is None and cat.is_active
        ]

        if category_type:
            categories = [
                cat for cat in categories
                if cat.category_type == category_type or cat.category_type == "both"
            ]

        return categories
    
    def get_subcategories(self, parent_category_name: str) -> List[Category]:
        """
        Get subcategories for a parent category
        
        Args:
            parent_category_name: Name of the parent category
            
        Returns:
            List of subcategories
        """
        # Find parent category by name
        parent_category = None
        for cat in self.hierarchy.categories.values():
            if cat.name == parent_category_name and cat.parent_id is None:
                parent_category = cat
                break
        
        if not parent_category:
            return []
        
        # Get subcategories
        subcategory_ids = self.hierarchy.hierarchy.get(parent_category.id, [])
        subcategories = [
            self.hierarchy.categories[cat_id] 
            for cat_id in subcategory_ids
            if cat_id in self.hierarchy.categories and self.hierarchy.categories[cat_id].is_active
        ]
        
        return sorted(subcategories, key=lambda x: x.name)
    
    def get_category_path(self, category_id: str) -> List[str]:
        """
        Get the full path to a category
        
        Args:
            category_id: ID of the category
            
        Returns:
            List of category names from root to target
        """
        if category_id not in self.hierarchy.categories:
            return []
        
        path = []
        current_category = self.hierarchy.categories[category_id]
        
        while current_category:
            path.insert(0, current_category.name)
            
            if current_category.parent_id:
                current_category = self.hierarchy.categories.get(current_category.parent_id)
            else:
                break
        
        return path
    
    def create_category(self, name: str, parent_name: Optional[str] = None,
                       description: str = "", color: str = "#007ACC", icon: str = "folder",
                       category_type: str = "expense") -> Optional[str]:
        """
        Create a new category

        Args:
            name: Category name
            parent_name: Parent category name (None for top-level)
            description: Category description
            color: Category color
            icon: Category icon
            category_type: Category type ("expense", "income", or "both")

        Returns:
            Category ID if created successfully, None otherwise
        """
        try:
            # Generate unique ID
            category_id = f"ml_{name.lower().replace(' ', '_')}_{int(datetime.now().timestamp())}"
            
            # Find parent ID if specified
            parent_id = None
            if parent_name:
                for cat in self.hierarchy.categories.values():
                    if cat.name == parent_name and cat.parent_id is None:
                        parent_id = cat.id
                        break
                
                if not parent_id:
                    self.logger.error(f"Parent category '{parent_name}' not found")
                    return None
            
            # Check if category already exists
            for cat in self.hierarchy.categories.values():
                if cat.name == name and cat.parent_id == parent_id:
                    self.logger.warning(f"Category '{name}' already exists under parent '{parent_name}'")
                    return cat.id
            
            # Create category
            category = Category(
                id=category_id,
                name=name,
                parent_id=parent_id,
                description=description,
                is_active=True,
                is_system=False,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                color=color,
                icon=icon,
                category_type=category_type
            )
            
            # Add to hierarchy
            self.hierarchy.categories[category_id] = category
            
            # Update hierarchy mapping
            if parent_id:
                if parent_id not in self.hierarchy.hierarchy:
                    self.hierarchy.hierarchy[parent_id] = []
                self.hierarchy.hierarchy[parent_id].append(category_id)
            
            # Save to file
            self._save_ml_categories()
            
            self.logger.info(f"Created category: {name} (ID: {category_id})")
            return category_id
            
        except Exception as e:
            self.logger.error(f"Error creating category: {str(e)}")
            return None
    
    def update_category(self, category_id: str, **kwargs) -> bool:
        """
        Update an existing category

        Args:
            category_id: ID of the category to update
            **kwargs: Fields to update

        Returns:
            True if updated successfully
        """
        if category_id not in self.hierarchy.categories:
            self.logger.error(f"Category {category_id} not found")
            return False

        category = self.hierarchy.categories[category_id]

        try:
            # Update fields
            for field, value in kwargs.items():
                if hasattr(category, field):
                    setattr(category, field, value)

            category.updated_at = datetime.now()

            # Save changes based on category type
            if category.is_system:
                # For system categories (from main app CSV), update the CSV file
                self._save_main_app_categories()

                # If it's a main category (no parent), also save metadata
                if category.parent_id is None:
                    self._save_main_category_metadata()
            else:
                # For ML-specific categories, save to JSON
                self._save_ml_categories()

            # Invalidate cache after successful update
            self._cache_valid = False

            self.logger.info(f"Updated category: {category_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error updating category: {str(e)}")
            return False
    
    def delete_category(self, category_id: str, force: bool = False, allow_system: bool = False) -> bool:
        """
        Delete a category with enhanced validation

        Args:
            category_id: ID of the category to delete
            force: Force deletion even if category has subcategories
            allow_system: Allow deletion of system categories (use with extreme caution)

        Returns:
            True if deleted successfully
        """
        if category_id not in self.hierarchy.categories:
            self.logger.error(f"Category {category_id} not found")
            return False

        category = self.hierarchy.categories[category_id]

        # Enhanced system category protection
        if category.is_system and not allow_system:
            self.logger.warning(f"Cannot delete system category {category_id} (name: {category.name}). "
                              f"System categories are protected from deletion to maintain data integrity.")
            return False

        # Check for active transactions using this category
        if self._category_has_active_transactions(category_id):
            self.logger.error(f"Cannot delete category {category_id} - it has active transaction labels. "
                            f"Please reassign transactions to another category first.")
            return False

        # Check for subcategories
        if category_id in self.hierarchy.hierarchy and not force:
            subcategory_count = len(self.hierarchy.hierarchy[category_id])
            self.logger.error(f"Category {category_id} has {subcategory_count} subcategories. "
                            f"Use force=True to delete all subcategories.")
            return False

        try:
            # Delete subcategories if force is True
            if force and category_id in self.hierarchy.hierarchy:
                subcategory_ids = self.hierarchy.hierarchy[category_id].copy()
                for sub_id in subcategory_ids:
                    # Recursively delete subcategories
                    self.delete_category(sub_id, force=True, allow_system=allow_system)

            # Remove from parent's hierarchy
            if category.parent_id and category.parent_id in self.hierarchy.hierarchy:
                try:
                    self.hierarchy.hierarchy[category.parent_id].remove(category_id)
                except ValueError:
                    # Category not in parent's list - this is OK
                    pass

            # Remove category from main hierarchy
            del self.hierarchy.categories[category_id]

            # Remove from hierarchy mapping
            if category_id in self.hierarchy.hierarchy:
                del self.hierarchy.hierarchy[category_id]

            # Save changes based on category type
            if category.is_system and allow_system:
                # For system categories, update the main app CSV
                self._save_main_app_categories()
            else:
                # For ML-specific categories, save to JSON
                self._save_ml_categories()

            # Invalidate cache
            self._cache_valid = False

            self.logger.info(f"Successfully deleted category: {category_id} (name: {category.name})")
            return True

        except Exception as e:
            self.logger.error(f"Error deleting category {category_id}: {str(e)}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return False

    def _category_has_active_transactions(self, category_id: str) -> bool:
        """Check if a category has active transaction labels"""
        try:
            # This is a placeholder - in a real implementation, you would check
            # the transaction database for any transactions using this category
            # For now, we'll assume it's safe to delete if it's not a system category
            category = self.hierarchy.categories.get(category_id)
            if not category:
                return False

            # System categories are more likely to have transactions
            if category.is_system:
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking category usage: {str(e)}")
            return True  # Err on the side of caution
    
    def get_category_suggestions(self, partial_name: str, limit: int = 10) -> List[Dict[str, str]]:
        """
        Get category suggestions based on partial name
        
        Args:
            partial_name: Partial category name
            limit: Maximum number of suggestions
            
        Returns:
            List of category suggestions
        """
        partial_lower = partial_name.lower()
        suggestions = []
        
        for category in self.hierarchy.categories.values():
            if not category.is_active:
                continue
            
            if partial_lower in category.name.lower():
                path = self.get_category_path(category.id)
                suggestions.append({
                    "id": category.id,
                    "name": category.name,
                    "full_path": " > ".join(path),
                    "parent_name": path[0] if len(path) > 1 else None
                })
        
        # Sort by relevance (exact matches first, then by name)
        suggestions.sort(key=lambda x: (
            not x["name"].lower().startswith(partial_lower),
            x["name"].lower()
        ))
        
        return suggestions[:limit]

    def _infer_category_type_from_name(self, category_name: str) -> str:
        """
        Infer category type from category name for backward compatibility

        Args:
            category_name: Name of the category

        Returns:
            "income", "expense", or "both" based on category name
        """
        name_lower = category_name.lower()

        # Income indicators
        income_keywords = [
            'salary', 'income', 'wage', 'bonus', 'dividend', 'interest', 'refund',
            'cashback', 'reward', 'gift', 'freelance', 'consulting', 'business income',
            'rental income', 'investment', 'return', 'profit', 'commission'
        ]

        # Check if it's likely an income category
        for keyword in income_keywords:
            if keyword in name_lower:
                return "income"

        # Default to expense for backward compatibility
        return "expense"

    def migrate_categories_for_transaction_types(self):
        """
        Migrate existing categories to include category_type for backward compatibility
        This method should be called once to update existing categories
        """
        updated_count = 0

        for category in self.hierarchy.categories.values():
            # Skip if category already has a category_type
            if hasattr(category, 'category_type') and category.category_type:
                continue

            # Infer category type from name
            inferred_type = self._infer_category_type_from_name(category.name)
            category.category_type = inferred_type
            updated_count += 1

            self.logger.debug(f"Migrated category '{category.name}' to type '{inferred_type}'")

        if updated_count > 0:
            # Save the updated categories
            self._save_ml_categories()
            self.logger.info(f"Migrated {updated_count} categories with transaction types")

        return updated_count

    def get_categories_by_type(self, category_type: str, include_subcategories: bool = True) -> List[Category]:
        """
        Get categories filtered by type

        Args:
            category_type: Category type ("expense", "income", or "both")
            include_subcategories: Whether to include subcategories

        Returns:
            List of categories matching the type
        """
        categories = []

        for category in self.hierarchy.categories.values():
            if not category.is_active:
                continue

            # Include if category type matches or is "both"
            if category.category_type == category_type or category.category_type == "both":
                categories.append(category)

            # For subcategories, also check if they should be included
            elif include_subcategories and category.parent_id:
                parent = self.hierarchy.categories.get(category.parent_id)
                if parent and (parent.category_type == category_type or parent.category_type == "both"):
                    categories.append(category)

        return sorted(categories, key=lambda x: (x.parent_id is not None, x.name))
    
    def validate_category_combination(self, category_name: str, sub_category_name: str) -> bool:
        """
        Validate if a category/subcategory combination is valid
        
        Args:
            category_name: Main category name
            sub_category_name: Sub-category name
            
        Returns:
            True if combination is valid
        """
        # Find main category
        main_category = None
        for cat in self.hierarchy.categories.values():
            if cat.name == category_name and cat.parent_id is None:
                main_category = cat
                break
        
        if not main_category:
            return False
        
        # Check if subcategory exists under this main category
        subcategory_ids = self.hierarchy.hierarchy.get(main_category.id, [])
        for sub_id in subcategory_ids:
            if sub_id in self.hierarchy.categories:
                sub_cat = self.hierarchy.categories[sub_id]
                if sub_cat.name == sub_category_name:
                    return True
        
        return False
    
    def get_flat_category_list(self) -> List[Dict[str, str]]:
        """
        Get a flat list of all category/subcategory combinations
        
        Returns:
            List of category combinations
        """
        combinations = []
        
        for category in self.hierarchy.categories.values():
            if category.parent_id is None:  # Main category
                subcategory_ids = self.hierarchy.hierarchy.get(category.id, [])
                
                if subcategory_ids:
                    # Has subcategories
                    for sub_id in subcategory_ids:
                        if sub_id in self.hierarchy.categories:
                            sub_cat = self.hierarchy.categories[sub_id]
                            if sub_cat.is_active:
                                combinations.append({
                                    "category": category.name,
                                    "sub_category": sub_cat.name,
                                    "full_path": f"{category.name} > {sub_cat.name}"
                                })
                else:
                    # No subcategories, use as both category and subcategory
                    if category.is_active:
                        combinations.append({
                            "category": category.name,
                            "sub_category": "General",
                            "full_path": f"{category.name} > General"
                        })
        
        return sorted(combinations, key=lambda x: x["full_path"])
    
    def _save_main_app_categories(self):
        """Save main application categories back to CSV file"""
        try:
            # Get all system categories (main app categories)
            system_categories = [
                cat for cat in self.hierarchy.categories.values()
                if cat.is_system
            ]

            # Create DataFrame for CSV export
            csv_data = []
            category_id_counter = 1

            for cat in system_categories:
                if cat.parent_id is None:
                    # This is a main category, skip it as we only save subcategories to CSV
                    continue

                # Find the parent category
                parent_cat = self.hierarchy.categories.get(cat.parent_id)
                if not parent_cat:
                    continue

                # Extract the original ID from the category_id (format: main_X)
                original_id = cat.id.replace('main_', '') if cat.id.startswith('main_') else str(category_id_counter)

                csv_data.append({
                    'id': original_id,
                    'category': parent_cat.name,
                    'sub_category': cat.name,
                    'is_active': cat.is_active,
                    'created_at': cat.created_at.strftime('%Y-%m-%d %H:%M:%S') if cat.created_at else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                category_id_counter += 1

            # Sort by ID to maintain order
            csv_data.sort(key=lambda x: int(x['id']) if x['id'].isdigit() else 999999)

            # Create DataFrame and save to CSV
            df = pd.DataFrame(csv_data)
            df.to_csv(self.main_categories_file, index=False)

            self.logger.info(f"Saved {len(csv_data)} main app categories to CSV")

        except Exception as e:
            self.logger.error(f"Error saving main app categories: {str(e)}")

    def _load_main_category_metadata(self) -> Dict[str, Dict[str, Any]]:
        """Load main category metadata (types, colors, etc.)"""
        if not self.main_category_metadata_file.exists():
            return {}

        try:
            with open(self.main_category_metadata_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading main category metadata: {str(e)}")
            return {}

    def _save_main_category_metadata(self):
        """Save main category metadata to preserve custom settings"""
        try:
            metadata = {}

            # Save metadata for all main categories (system categories without parent)
            for cat in self.hierarchy.categories.values():
                if cat.is_system and cat.parent_id is None:
                    metadata[cat.name] = {
                        "category_type": cat.category_type,
                        "color": cat.color,
                        "description": cat.description
                    }

            with open(self.main_category_metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)

            self.logger.debug(f"Saved metadata for {len(metadata)} main categories")

        except Exception as e:
            self.logger.error(f"Error saving main category metadata: {str(e)}")

    def _save_ml_categories(self):
        """Save ML-specific categories to file"""
        try:
            ml_categories = [
                cat for cat in self.hierarchy.categories.values()
                if not cat.is_system
            ]

            categories_data = []
            for cat in ml_categories:
                cat_data = {
                    "id": cat.id,
                    "name": cat.name,
                    "parent_id": cat.parent_id,
                    "description": cat.description,
                    "is_active": cat.is_active,
                    "is_system": cat.is_system,
                    "color": cat.color,
                    "icon": cat.icon,
                    "category_type": cat.category_type
                }

                if cat.created_at:
                    cat_data["created_at"] = cat.created_at.isoformat()
                if cat.updated_at:
                    cat_data["updated_at"] = cat.updated_at.isoformat()

                categories_data.append(cat_data)

            with open(self.ml_categories_file, 'w') as f:
                json.dump(categories_data, f, indent=2)

        except Exception as e:
            self.logger.error(f"Error saving ML categories: {str(e)}")
    
    def export_categories(self, format: str = "json") -> Optional[str]:
        """
        Export categories to file
        
        Args:
            format: Export format ("json", "csv")
            
        Returns:
            Path to exported file or None if failed
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format == "json":
                export_file = self.ml_data_dir / f"categories_export_{timestamp}.json"
                categories_data = []
                
                for cat in self.hierarchy.categories.values():
                    cat_data = {
                        "id": cat.id,
                        "name": cat.name,
                        "parent_id": cat.parent_id,
                        "description": cat.description,
                        "is_active": cat.is_active,
                        "is_system": cat.is_system,
                        "path": " > ".join(self.get_category_path(cat.id))
                    }
                    categories_data.append(cat_data)
                
                with open(export_file, 'w') as f:
                    json.dump(categories_data, f, indent=2)
                    
            elif format == "csv":
                export_file = self.ml_data_dir / f"categories_export_{timestamp}.csv"
                combinations = self.get_flat_category_list()
                df = pd.DataFrame(combinations)
                df.to_csv(export_file, index=False)
            else:
                self.logger.error(f"Unsupported export format: {format}")
                return None
            
            self.logger.info(f"Categories exported to: {export_file}")
            return str(export_file)
            
        except Exception as e:
            self.logger.error(f"Error exporting categories: {str(e)}")
            return None
