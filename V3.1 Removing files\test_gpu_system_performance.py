#!/usr/bin/env python3
"""
GPU System Performance Test

Comprehensive testing script for the new GPU-accelerated deep learning
categorization system. Tests real-world performance and validates
the 95%+ accuracy target.
"""

import sys
import time
import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add bank_analyzer to path
sys.path.append('.')

from bank_analyzer.ml.integrated_gpu_categorizer import IntegratedGPUCategorizer
from bank_analyzer.ml.gpu_training_pipeline import AdvancedTrainingPipeline


def test_gpu_system_comprehensive():
    """
    Comprehensive test of the GPU-accelerated categorization system
    """
    print("🚀 GPU SYSTEM COMPREHENSIVE PERFORMANCE TEST")
    print("=" * 80)
    
    # Initialize system
    print("📦 Initializing GPU categorization system...")
    categorizer = IntegratedGPUCategorizer()
    
    # Check GPU availability
    gpu_available = categorizer.system_status['gpu_available']
    print(f"🔧 GPU Available: {'✅ Yes' if gpu_available else '❌ No'}")
    
    if not gpu_available:
        print("⚠️ GPU model not available. Training new model...")
        
        # Train new GPU model
        training_results = test_gpu_training(categorizer)
        if not training_results.get('success', False):
            print("❌ GPU training failed. Cannot proceed with testing.")
            return
    
    # Test categorization performance
    print("\n📊 Testing categorization performance...")
    performance_results = test_categorization_performance(categorizer)
    
    # Test batch processing
    print("\n📦 Testing batch processing...")
    batch_results = test_batch_processing(categorizer)
    
    # Evaluate system performance
    print("\n🔍 Evaluating overall system performance...")
    evaluation_results = test_system_evaluation(categorizer)
    
    # Generate final report
    print("\n📋 Generating performance report...")
    generate_final_report(performance_results, batch_results, evaluation_results, categorizer)


def test_gpu_training(categorizer: IntegratedGPUCategorizer) -> Dict[str, Any]:
    """
    Test GPU model training with cross-validation
    """
    print("\n🤖 TESTING GPU MODEL TRAINING")
    print("-" * 50)
    
    csv_path = "colab_training_data.csv"
    
    if not Path(csv_path).exists():
        print(f"❌ Training data not found: {csv_path}")
        return {'success': False, 'error': 'Training data not found'}
    
    print(f"📁 Using training data: {csv_path}")
    
    # Start training
    start_time = time.time()
    training_results = categorizer.train_gpu_model(csv_path, use_cross_validation=True)
    training_time = time.time() - start_time
    
    if training_results['success']:
        accuracy = training_results.get('accuracy', 0)
        target_achieved = training_results.get('target_achieved', False)
        
        print(f"✅ Training completed in {training_time:.1f}s")
        print(f"🎯 Accuracy: {accuracy*100:.1f}%")
        print(f"🏆 Target 95% achieved: {'✅ Yes' if target_achieved else '❌ No'}")
        
        if target_achieved:
            print("🎉 Excellent! GPU model meets performance targets.")
        else:
            print("⚠️ Model accuracy below 95% target. Consider more training data.")
    else:
        print(f"❌ Training failed: {training_results.get('error', 'Unknown error')}")
    
    return training_results


def test_categorization_performance(categorizer: IntegratedGPUCategorizer) -> Dict[str, Any]:
    """
    Test individual transaction categorization performance
    """
    print("\n📊 TESTING CATEGORIZATION PERFORMANCE")
    print("-" * 50)
    
    # Sample transactions for testing
    test_transactions = [
        {
            'description': 'UPI-SALARY CREDIT FROM COMPANY ABC PVT LTD-GPAY',
            'amount': 50000.0,
            'date': '2025-01-01'
        },
        {
            'description': 'UPI-P2P-GROCERY STORE BIG BAZAAR-PHONEPE',
            'amount': -1500.0,
            'date': '2025-01-01'
        },
        {
            'description': 'IMPS-ATM CASH WITHDRAWAL HDFC BANK',
            'amount': -2000.0,
            'date': '2025-01-01'
        },
        {
            'description': 'UPI-ELECTRICITY BILL PAYMENT ONLINE-PAYTM',
            'amount': -800.0,
            'date': '2025-01-01'
        },
        {
            'description': 'UPI-FUEL STATION PETROL PUMP-GPAY',
            'amount': -1200.0,
            'date': '2025-01-01'
        }
    ]
    
    results = []
    total_time = 0
    
    print("🧪 Testing sample transactions:")
    print()
    
    for i, transaction in enumerate(test_transactions, 1):
        start_time = time.time()
        result = categorizer.categorize_transaction(transaction)
        processing_time = time.time() - start_time
        total_time += processing_time
        
        results.append(result)
        
        # Display result
        desc_short = transaction['description'][:50] + "..." if len(transaction['description']) > 50 else transaction['description']
        status = "✅" if result['success'] else "❌"
        
        print(f"{i}. {status} '{desc_short}'")
        print(f"   Category: {result['category']}")
        print(f"   Method: {result['method_used']}")
        print(f"   Confidence: {result['confidence']:.2f}")
        print(f"   Time: {processing_time*1000:.1f}ms")
        print()
    
    # Calculate statistics
    successful = sum(1 for r in results if r['success'])
    avg_time = total_time / len(results)
    avg_confidence = sum(r['confidence'] for r in results if r['success']) / max(1, successful)
    
    print(f"📈 PERFORMANCE SUMMARY:")
    print(f"   Success Rate: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
    print(f"   Average Time: {avg_time*1000:.1f}ms")
    print(f"   Average Confidence: {avg_confidence:.2f}")
    
    # Method usage breakdown
    method_counts = {}
    for result in results:
        if result['success']:
            method = result['method_used']
            method_counts[method] = method_counts.get(method, 0) + 1
    
    print(f"   Method Usage:")
    for method, count in method_counts.items():
        print(f"     {method}: {count} ({count/successful*100:.1f}%)")
    
    return {
        'total_tests': len(results),
        'successful': successful,
        'success_rate': successful / len(results),
        'avg_processing_time': avg_time,
        'avg_confidence': avg_confidence,
        'method_usage': method_counts,
        'detailed_results': results
    }


def test_batch_processing(categorizer: IntegratedGPUCategorizer) -> Dict[str, Any]:
    """
    Test batch processing performance
    """
    print("\n📦 TESTING BATCH PROCESSING")
    print("-" * 50)
    
    # Load test data
    try:
        df = pd.read_csv("colab_training_data.csv")
        
        # Take a sample for testing
        test_sample = df.sample(n=min(50, len(df)), random_state=42)
        
        transactions = []
        for _, row in test_sample.iterrows():
            transactions.append({
                'description': row['description'],
                'amount': row.get('amount', 0.0),
                'date': row.get('date', '2025-01-01')
            })
        
        print(f"📊 Testing batch of {len(transactions)} transactions...")
        
        # Process batch
        start_time = time.time()
        results = categorizer.categorize_batch(transactions)
        total_time = time.time() - start_time
        
        # Calculate statistics
        successful = sum(1 for r in results if r['success'])
        avg_time_per_transaction = total_time / len(transactions)
        throughput = len(transactions) / total_time
        
        print(f"✅ Batch processing completed")
        print(f"📈 Results:")
        print(f"   Total Time: {total_time:.2f}s")
        print(f"   Success Rate: {successful}/{len(transactions)} ({successful/len(transactions)*100:.1f}%)")
        print(f"   Avg Time/Transaction: {avg_time_per_transaction*1000:.1f}ms")
        print(f"   Throughput: {throughput:.1f} transactions/second")
        
        return {
            'batch_size': len(transactions),
            'total_time': total_time,
            'successful': successful,
            'success_rate': successful / len(transactions),
            'avg_time_per_transaction': avg_time_per_transaction,
            'throughput': throughput
        }
        
    except Exception as e:
        print(f"❌ Batch processing test failed: {e}")
        return {'error': str(e)}


def test_system_evaluation(categorizer: IntegratedGPUCategorizer) -> Dict[str, Any]:
    """
    Test comprehensive system evaluation
    """
    print("\n🔍 TESTING SYSTEM EVALUATION")
    print("-" * 50)
    
    try:
        # Run system evaluation
        evaluation_results = categorizer.evaluate_system_performance("colab_training_data.csv")
        
        if 'error' in evaluation_results:
            print(f"❌ Evaluation failed: {evaluation_results['error']}")
            return evaluation_results
        
        # Display key metrics
        gpu_perf = evaluation_results.get('gpu_model_performance', {})
        router_perf = evaluation_results.get('router_performance', {})
        overall_score = evaluation_results.get('overall_score', 0)
        
        print(f"📊 EVALUATION RESULTS:")
        
        if 'overall_accuracy' in gpu_perf:
            print(f"   GPU Model Accuracy: {gpu_perf['overall_accuracy']*100:.1f}%")
            print(f"   Target 95% Achieved: {'✅ Yes' if gpu_perf.get('target_achieved', False) else '❌ No'}")
        
        if 'total_categorizations' in router_perf:
            print(f"   Total Categorizations: {router_perf['total_categorizations']:,}")
        
        if 'method_distribution' in router_perf:
            dist = router_perf['method_distribution']
            print(f"   Method Distribution:")
            print(f"     Manual Labels: {dist.get('manual_labels', 0):.1f}%")
            print(f"     GPU ML: {dist.get('gpu_ml', 0):.1f}%")
            print(f"     Pattern Matching: {dist.get('pattern_matching', 0):.1f}%")
        
        print(f"   Overall System Score: {overall_score:.1f}/100")
        
        # Performance assessment
        if overall_score >= 90:
            print("🎉 Excellent system performance!")
        elif overall_score >= 75:
            print("👍 Good system performance")
        elif overall_score >= 60:
            print("⚠️ Moderate system performance")
        else:
            print("❌ System needs improvement")
        
        return evaluation_results
        
    except Exception as e:
        print(f"❌ System evaluation failed: {e}")
        return {'error': str(e)}


def generate_final_report(performance_results: Dict[str, Any], 
                         batch_results: Dict[str, Any],
                         evaluation_results: Dict[str, Any],
                         categorizer: IntegratedGPUCategorizer):
    """
    Generate comprehensive final performance report
    """
    print("\n📋 FINAL PERFORMANCE REPORT")
    print("=" * 80)
    
    # System overview
    system_stats = categorizer.get_system_statistics()
    
    print(f"🖥️  SYSTEM OVERVIEW:")
    print(f"   GPU Available: {'✅ Yes' if categorizer.system_status['gpu_available'] else '❌ No'}")
    print(f"   Initialization: {categorizer.system_status['initialization_time'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Performance summary
    print(f"\n📊 PERFORMANCE SUMMARY:")
    
    if 'success_rate' in performance_results:
        print(f"   Individual Transaction Success: {performance_results['success_rate']*100:.1f}%")
        print(f"   Average Processing Time: {performance_results['avg_processing_time']*1000:.1f}ms")
    
    if 'throughput' in batch_results:
        print(f"   Batch Processing Throughput: {batch_results['throughput']:.1f} tx/sec")
    
    # GPU model performance
    if 'gpu_model_performance' in evaluation_results:
        gpu_perf = evaluation_results['gpu_model_performance']
        if 'overall_accuracy' in gpu_perf:
            print(f"   GPU Model Accuracy: {gpu_perf['overall_accuracy']*100:.1f}%")
            print(f"   95% Target Achieved: {'✅ Yes' if gpu_perf.get('target_achieved', False) else '❌ No'}")
    
    # Overall assessment
    overall_score = evaluation_results.get('overall_score', 0)
    print(f"\n🏆 OVERALL SYSTEM SCORE: {overall_score:.1f}/100")
    
    if overall_score >= 90:
        print("🎉 EXCELLENT! System exceeds performance expectations.")
        print("   Ready for production use with high confidence.")
    elif overall_score >= 75:
        print("👍 GOOD! System meets most performance requirements.")
        print("   Suitable for production with minor optimizations.")
    elif overall_score >= 60:
        print("⚠️ MODERATE! System has acceptable performance.")
        print("   Consider additional training or optimization.")
    else:
        print("❌ NEEDS IMPROVEMENT! System requires significant work.")
        print("   Additional training data and optimization needed.")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if overall_score < 95:
        print("   • Consider adding more training data for underperforming categories")
        print("   • Optimize confidence thresholds for better routing")
    
    if 'avg_processing_time' in performance_results and performance_results['avg_processing_time'] > 0.1:
        print("   • Consider batch processing for large volumes")
        print("   • Optimize GPU memory usage for faster inference")
    
    print("   • Regular model retraining with new labeled data")
    print("   • Monitor performance metrics and adjust thresholds")
    
    # Save detailed report
    report_data = {
        'test_date': datetime.now().isoformat(),
        'performance_results': performance_results,
        'batch_results': batch_results,
        'evaluation_results': evaluation_results,
        'system_statistics': system_stats,
        'overall_score': overall_score
    }
    
    report_path = Path("gpu_system_performance_report.json")
    with open(report_path, 'w') as f:
        json.dump(report_data, f, indent=2, default=str)
    
    print(f"\n📄 Detailed report saved to: {report_path}")
    print("\n🎯 GPU system testing completed!")


if __name__ == "__main__":
    test_gpu_system_comprehensive()
