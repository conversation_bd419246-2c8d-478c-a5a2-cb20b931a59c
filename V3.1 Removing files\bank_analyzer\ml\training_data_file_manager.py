"""
Training Data File Manager
Handles import/export and management of training data files
"""

import pandas as pd
import json
import hashlib
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict

from ..core.logger import get_logger
from .data_preparation import TransactionDataPreparator, UniqueTransaction


@dataclass
class TrainingDataFile:
    """Represents a training data file in the system"""
    file_id: str
    original_filename: str
    import_date: datetime
    file_size: int
    transaction_count: int
    source_type: str  # "csv", "excel", "processed"
    file_hash: str
    metadata: Dict[str, Any]
    is_active: bool = True


@dataclass
class ImportPreview:
    """Preview of data to be imported"""
    total_rows: int
    valid_rows: int
    invalid_rows: int
    new_transactions: int
    duplicate_transactions: int
    sample_data: List[Dict[str, Any]]
    validation_errors: List[str]
    column_mapping: Dict[str, str]


class TrainingDataFileManager:
    """
    Manages training data files and their lifecycle
    Handles import, removal, and tracking of data sources
    """
    
    def __init__(self, data_dir: str = "bank_analyzer_config/ml_data"):
        self.logger = get_logger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.files_registry_file = self.data_dir / "training_files_registry.json"
        self.import_history_file = self.data_dir / "import_history.json"
        
        # Data preparator
        self.data_preparator = TransactionDataPreparator(str(self.data_dir))
        
        # Load existing registry
        self.files_registry = self._load_files_registry()
    
    def _load_files_registry(self) -> Dict[str, TrainingDataFile]:
        """Load the files registry"""
        if not self.files_registry_file.exists():
            return {}
        
        try:
            with open(self.files_registry_file, 'r') as f:
                data = json.load(f)
            
            registry = {}
            for file_id, file_data in data.items():
                registry[file_id] = TrainingDataFile(
                    file_id=file_data['file_id'],
                    original_filename=file_data['original_filename'],
                    import_date=datetime.fromisoformat(file_data['import_date']),
                    file_size=file_data['file_size'],
                    transaction_count=file_data['transaction_count'],
                    source_type=file_data['source_type'],
                    file_hash=file_data['file_hash'],
                    metadata=file_data.get('metadata', {}),
                    is_active=file_data.get('is_active', True)
                )
            
            return registry
            
        except Exception as e:
            self.logger.error(f"Error loading files registry: {str(e)}")
            return {}
    
    def _save_files_registry(self):
        """Save the files registry"""
        try:
            data = {}
            for file_id, file_obj in self.files_registry.items():
                file_data = asdict(file_obj)
                file_data['import_date'] = file_obj.import_date.isoformat()
                data[file_id] = file_data
            
            with open(self.files_registry_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving files registry: {str(e)}")
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate hash of file content"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def preview_import(self, file_path: Path, column_mapping: Optional[Dict[str, str]] = None) -> ImportPreview:
        """
        Preview data import from file
        
        Args:
            file_path: Path to the file to import
            column_mapping: Optional mapping of file columns to expected columns
            
        Returns:
            ImportPreview with validation results
        """
        try:
            # Read file based on extension
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_path.suffix}")
            
            # Expected columns for transaction data
            expected_columns = {
                'description': ['description', 'desc', 'transaction_description', 'details'],
                'amount': ['amount', 'value', 'transaction_amount'],
                'date': ['date', 'transaction_date', 'txn_date'],
                'category': ['category', 'main_category'],
                'sub_category': ['sub_category', 'subcategory', 'sub_cat']
            }
            
            # Auto-detect column mapping if not provided
            if not column_mapping:
                column_mapping = self._auto_detect_columns(df.columns.tolist(), expected_columns)
            
            # Validate required columns
            validation_errors = []
            if 'description' not in column_mapping:
                validation_errors.append("Description column not found or mapped")
            
            # Count valid/invalid rows
            valid_rows = 0
            invalid_rows = 0
            sample_data = []
            
            for idx, row in df.head(100).iterrows():  # Preview first 100 rows
                is_valid = True
                row_data = {}
                
                # Map columns
                for expected_col, file_col in column_mapping.items():
                    if file_col in df.columns:
                        row_data[expected_col] = row[file_col]
                    else:
                        row_data[expected_col] = None
                
                # Validate description
                if not row_data.get('description') or pd.isna(row_data['description']):
                    is_valid = False
                
                if is_valid:
                    valid_rows += 1
                else:
                    invalid_rows += 1
                
                # Add to sample
                if len(sample_data) < 10:
                    sample_data.append({
                        'row_number': idx + 1,
                        'is_valid': is_valid,
                        'data': row_data
                    })
            
            # Check for duplicates against existing data
            existing_transactions = self.data_preparator.load_unique_transactions()
            new_transactions = 0
            duplicate_transactions = 0
            
            for _, row in df.iterrows():
                if 'description' in column_mapping and column_mapping['description'] in df.columns:
                    desc = str(row[column_mapping['description']])
                    hash_id = self.data_preparator._generate_hash_id(desc, "unknown")
                    
                    if hash_id in existing_transactions:
                        duplicate_transactions += 1
                    else:
                        new_transactions += 1
            
            return ImportPreview(
                total_rows=len(df),
                valid_rows=valid_rows,
                invalid_rows=invalid_rows,
                new_transactions=new_transactions,
                duplicate_transactions=duplicate_transactions,
                sample_data=sample_data,
                validation_errors=validation_errors,
                column_mapping=column_mapping
            )
            
        except Exception as e:
            self.logger.error(f"Error previewing import: {str(e)}")
            return ImportPreview(
                total_rows=0,
                valid_rows=0,
                invalid_rows=0,
                new_transactions=0,
                duplicate_transactions=0,
                sample_data=[],
                validation_errors=[f"Error reading file: {str(e)}"],
                column_mapping={}
            )
    
    def _auto_detect_columns(self, file_columns: List[str], expected_columns: Dict[str, List[str]]) -> Dict[str, str]:
        """Auto-detect column mapping"""
        mapping = {}
        
        for expected_col, possible_names in expected_columns.items():
            for file_col in file_columns:
                if file_col.lower() in [name.lower() for name in possible_names]:
                    mapping[expected_col] = file_col
                    break
        
        return mapping

    def import_training_data(self, file_path: Path, column_mapping: Dict[str, str],
                           merge_duplicates: bool = True) -> Tuple[bool, str, Dict[str, int]]:
        """
        Import training data from file

        Args:
            file_path: Path to the file to import
            column_mapping: Mapping of file columns to expected columns
            merge_duplicates: Whether to merge duplicate transactions

        Returns:
            Tuple of (success, message, stats)
        """
        try:
            # Calculate file hash
            file_hash = self._calculate_file_hash(file_path)

            # Check if file already imported
            for existing_file in self.files_registry.values():
                if existing_file.file_hash == file_hash and existing_file.is_active:
                    return False, "File already imported", {}

            # Read file
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                return False, f"Unsupported file format: {file_path.suffix}", {}

            # Load existing transactions
            existing_transactions = self.data_preparator.load_unique_transactions()

            # Process new transactions
            new_transactions = {}
            updated_transactions = {}
            skipped_count = 0
            error_count = 0

            for idx, row in df.iterrows():
                try:
                    # Extract data using column mapping
                    description = str(row[column_mapping.get('description', '')]) if column_mapping.get('description') else None

                    if not description or pd.isna(description):
                        error_count += 1
                        continue

                    # Generate hash ID
                    hash_id = self.data_preparator._generate_hash_id(description, "unknown")

                    # Check if transaction exists
                    if hash_id in existing_transactions:
                        if merge_duplicates:
                            # Update existing transaction
                            existing_txn = existing_transactions[hash_id]
                            existing_txn.frequency += 1

                            # Update categories if provided
                            if column_mapping.get('category') and not pd.isna(row[column_mapping['category']]):
                                existing_txn.category = str(row[column_mapping['category']])
                            if column_mapping.get('sub_category') and not pd.isna(row[column_mapping['sub_category']]):
                                existing_txn.sub_category = str(row[column_mapping['sub_category']])

                            # Add source file
                            if not hasattr(existing_txn, 'source_files'):
                                existing_txn.source_files = set()
                            existing_txn.source_files.add(file_path.name)

                            updated_transactions[hash_id] = existing_txn
                        else:
                            skipped_count += 1
                    else:
                        # Create new transaction
                        normalized_desc = self.data_preparator._normalize_description(description)

                        # For training data imports, use a higher minimum frequency to avoid
                        # polluting the dataset with low-frequency training examples
                        import_frequency = max(3, int(row.get('frequency', 3)))  # Minimum frequency of 3 for imports

                        new_txn = UniqueTransaction(
                            description=description,
                            normalized_description=normalized_desc,
                            hash_id=hash_id,
                            frequency=import_frequency,
                            first_seen=datetime.now().date(),
                            last_seen=datetime.now().date(),
                            source_files={file_path.name}
                        )

                        # Set categories if provided
                        if column_mapping.get('category') and not pd.isna(row[column_mapping['category']]):
                            new_txn.category = str(row[column_mapping['category']])
                        if column_mapping.get('sub_category') and not pd.isna(row[column_mapping['sub_category']]):
                            new_txn.sub_category = str(row[column_mapping['sub_category']])

                        # Set amount information if provided
                        if column_mapping.get('amount') and not pd.isna(row[column_mapping['amount']]):
                            amount = float(row[column_mapping['amount']])
                            new_txn.amount_range = (amount, amount)
                            new_txn.sample_amounts = [amount]

                        new_transactions[hash_id] = new_txn

                except Exception as e:
                    self.logger.error(f"Error processing row {idx}: {str(e)}")
                    error_count += 1

            # Merge with existing data
            all_transactions = {**existing_transactions, **new_transactions, **updated_transactions}

            # Save updated transactions
            self.data_preparator.save_unique_transactions(all_transactions)

            # Register the file
            file_id = f"file_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file_hash[:8]}"
            training_file = TrainingDataFile(
                file_id=file_id,
                original_filename=file_path.name,
                import_date=datetime.now(),
                file_size=file_path.stat().st_size,
                transaction_count=len(new_transactions) + len(updated_transactions),
                source_type=file_path.suffix.lower()[1:],  # Remove the dot
                file_hash=file_hash,
                metadata={
                    'column_mapping': column_mapping,
                    'merge_duplicates': merge_duplicates,
                    'import_stats': {
                        'new_transactions': len(new_transactions),
                        'updated_transactions': len(updated_transactions),
                        'skipped_transactions': skipped_count,
                        'error_count': error_count
                    }
                }
            )

            self.files_registry[file_id] = training_file
            self._save_files_registry()

            # Log import history
            self._log_import_history(training_file, "import")

            stats = {
                'new_transactions': len(new_transactions),
                'updated_transactions': len(updated_transactions),
                'skipped_transactions': skipped_count,
                'error_count': error_count,
                'total_processed': len(df)
            }

            message = f"Successfully imported {len(new_transactions)} new and updated {len(updated_transactions)} existing transactions"
            return True, message, stats

        except Exception as e:
            self.logger.error(f"Error importing training data: {str(e)}")
            return False, f"Import failed: {str(e)}", {}

    def get_training_files(self, active_only: bool = True) -> List[TrainingDataFile]:
        """Get list of training data files"""
        files = list(self.files_registry.values())
        if active_only:
            files = [f for f in files if f.is_active]
        return sorted(files, key=lambda x: x.import_date, reverse=True)

    def get_file_impact_analysis(self, file_id: str) -> Dict[str, Any]:
        """
        Analyze the impact of removing a specific file

        Args:
            file_id: ID of the file to analyze

        Returns:
            Impact analysis data
        """
        if file_id not in self.files_registry:
            return {"error": "File not found"}

        training_file = self.files_registry[file_id]

        # Load current transactions
        all_transactions = self.data_preparator.load_unique_transactions()

        # Find transactions from this file
        affected_transactions = []
        for txn in all_transactions.values():
            if hasattr(txn, 'source_files') and training_file.original_filename in txn.source_files:
                affected_transactions.append(txn)

        # Analyze impact
        labeled_count = sum(1 for txn in affected_transactions if txn.category)
        unlabeled_count = len(affected_transactions) - labeled_count

        # Check if any transactions would be completely removed
        transactions_to_remove = []
        transactions_to_update = []

        for txn in affected_transactions:
            if hasattr(txn, 'source_files') and len(txn.source_files) == 1:
                # This transaction only exists in this file
                transactions_to_remove.append(txn)
            else:
                # This transaction exists in other files too
                transactions_to_update.append(txn)

        return {
            "file_info": training_file,
            "total_affected_transactions": len(affected_transactions),
            "labeled_transactions": labeled_count,
            "unlabeled_transactions": unlabeled_count,
            "transactions_to_remove": len(transactions_to_remove),
            "transactions_to_update": len(transactions_to_update),
            "affected_transaction_details": [
                {
                    "hash_id": txn.hash_id,
                    "description": txn.description,
                    "category": getattr(txn, 'category', None),
                    "frequency": txn.frequency,
                    "action": "remove" if txn in transactions_to_remove else "update"
                }
                for txn in affected_transactions[:20]  # Limit to first 20 for preview
            ]
        }

    def remove_training_file(self, file_id: str, remove_labeling_history: bool = True) -> Tuple[bool, str, Dict[str, int]]:
        """
        Remove a training data file and its associated data

        Args:
            file_id: ID of the file to remove
            remove_labeling_history: Whether to remove associated labeling history

        Returns:
            Tuple of (success, message, stats)
        """
        try:
            if file_id not in self.files_registry:
                return False, "File not found", {}

            training_file = self.files_registry[file_id]

            # Load current transactions
            all_transactions = self.data_preparator.load_unique_transactions()

            # Process transactions
            transactions_removed = 0
            transactions_updated = 0

            transactions_to_keep = {}

            for hash_id, txn in all_transactions.items():
                if hasattr(txn, 'source_files') and training_file.original_filename in txn.source_files:
                    # This transaction is from the file being removed
                    if len(txn.source_files) == 1:
                        # Only exists in this file - remove completely
                        transactions_removed += 1
                    else:
                        # Exists in other files - update
                        txn.source_files.remove(training_file.original_filename)
                        txn.frequency = max(1, txn.frequency - 1)  # Reduce frequency
                        transactions_to_keep[hash_id] = txn
                        transactions_updated += 1
                else:
                    # Keep transaction as-is
                    transactions_to_keep[hash_id] = txn

            # Save updated transactions
            self.data_preparator.save_unique_transactions(transactions_to_keep)

            # Remove labeling history if requested
            labeling_records_removed = 0
            if remove_labeling_history:
                labeling_records_removed = self._remove_labeling_history_for_file(training_file.original_filename)

            # Mark file as inactive
            training_file.is_active = False
            self.files_registry[file_id] = training_file
            self._save_files_registry()

            # Log removal
            self._log_import_history(training_file, "remove")

            stats = {
                'transactions_removed': transactions_removed,
                'transactions_updated': transactions_updated,
                'labeling_records_removed': labeling_records_removed
            }

            message = f"Removed file: {transactions_removed} transactions deleted, {transactions_updated} updated"
            return True, message, stats

        except Exception as e:
            self.logger.error(f"Error removing training file: {str(e)}")
            return False, f"Removal failed: {str(e)}", {}

    def _remove_labeling_history_for_file(self, filename: str) -> int:
        """Remove labeling history records for a specific file"""
        try:
            from .training_data_manager import TrainingDataManager

            training_manager = TrainingDataManager()
            labeling_history = training_manager.get_labeling_history()

            # Filter out records from this file
            original_count = len(labeling_history)
            filtered_history = [
                record for record in labeling_history
                if not (hasattr(record, 'source_file') and record.source_file == filename)
            ]

            # Save filtered history
            training_manager._save_labeling_history(filtered_history)

            return original_count - len(filtered_history)

        except Exception as e:
            self.logger.error(f"Error removing labeling history: {str(e)}")
            return 0

    def _log_import_history(self, training_file: TrainingDataFile, action: str):
        """Log import/removal history"""
        try:
            history_entry = {
                "timestamp": datetime.now().isoformat(),
                "action": action,
                "file_id": training_file.file_id,
                "filename": training_file.original_filename,
                "transaction_count": training_file.transaction_count,
                "metadata": training_file.metadata
            }

            # Load existing history
            history = []
            if self.import_history_file.exists():
                with open(self.import_history_file, 'r') as f:
                    history = json.load(f)

            # Add new entry
            history.append(history_entry)

            # Keep only last 100 entries
            history = history[-100:]

            # Save history
            with open(self.import_history_file, 'w') as f:
                json.dump(history, f, indent=2)

        except Exception as e:
            self.logger.error(f"Error logging import history: {str(e)}")

    def get_import_history(self) -> List[Dict[str, Any]]:
        """Get import/removal history"""
        try:
            if self.import_history_file.exists():
                with open(self.import_history_file, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            self.logger.error(f"Error loading import history: {str(e)}")
            return []
