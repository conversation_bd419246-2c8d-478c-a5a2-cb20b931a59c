"""
ML Model Training Interface
Comprehensive interface for managing ML model training with data selection and incremental training
"""

import sys
from pathlib import Path
from typing import List, Dict, Optional, Any, Set
from datetime import datetime
import logging

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QTableWidget, QTableWidgetItem, QHeaderView,
    QCheckBox, QLineEdit, QComboBox, QGroupBox, QProgressBar,
    QTextEdit, QSplitter, QFrame, QMessageBox, QDialog,
    QDialogButtonBox, QSpinBox, QDateEdit, QTabWidget, QApplication
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QDate
from PySide6.QtGui import QFont, QColor, QPalette, QIcon, QPixmap

from ..ml.training_data_manager import TrainingDataManager, TrainingDataStats
from ..ml.data_preparation import UniqueTransaction
from ..ml.model_trainer import ModelTrainer
from ..ml.training_history_manager import Training<PERSON>istoryManager
from ..core.logger import get_logger


class TrainingDataTableWidget(QTableWidget):
    """Enhanced table widget for displaying training data with selection capabilities"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.setup_table()
        
    def setup_table(self):
        """Setup table structure and appearance"""
        # Define columns
        self.columns = [
            "Select", "Status", "Description", "Category", "Sub-Category", 
            "Frequency", "Amount Range", "Labeled By", "Labeled Date", 
            "Training Count", "Last Trained"
        ]
        
        self.setColumnCount(len(self.columns))
        self.setHorizontalHeaderLabels(self.columns)
        
        # Configure table appearance
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSortingEnabled(True)
        
        # Configure column widths
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # Select checkbox
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # Status
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Description
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Category
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Sub-Category
        
        # Set fixed widths for specific columns
        self.setColumnWidth(0, 60)  # Select
        self.setColumnWidth(1, 120)  # Status
        
    def populate_data(self, transactions: List[UniqueTransaction]):
        """Populate table with transaction data"""
        self.setRowCount(len(transactions))
        
        for row, txn in enumerate(transactions):
            # Select checkbox
            checkbox = QCheckBox()
            if txn.is_newly_labeled():
                checkbox.setChecked(True)  # Auto-select newly labeled
            self.setCellWidget(row, 0, checkbox)
            
            # Status with color coding
            status_item = QTableWidgetItem(txn.get_training_status_display())
            if txn.is_newly_labeled():
                status_item.setBackground(QColor(144, 238, 144))  # Light green
                status_item.setForeground(QColor(0, 100, 0))  # Dark green
            elif txn.is_previously_trained():
                status_item.setBackground(QColor(173, 216, 230))  # Light blue
                status_item.setForeground(QColor(0, 0, 139))  # Dark blue
            self.setItem(row, 1, status_item)
            
            # Description
            desc_item = QTableWidgetItem(txn.description[:100] + "..." if len(txn.description) > 100 else txn.description)
            desc_item.setToolTip(txn.description)
            self.setItem(row, 2, desc_item)
            
            # Category and Sub-category
            self.setItem(row, 3, QTableWidgetItem(txn.category or ""))
            self.setItem(row, 4, QTableWidgetItem(txn.sub_category or ""))
            
            # Frequency
            self.setItem(row, 5, QTableWidgetItem(str(txn.frequency)))
            
            # Amount range
            if txn.amount_range:
                amount_text = f"${txn.amount_range[0]:.2f} to ${txn.amount_range[1]:.2f}"
            else:
                amount_text = "N/A"
            self.setItem(row, 6, QTableWidgetItem(amount_text))
            
            # Labeled by and date
            self.setItem(row, 7, QTableWidgetItem(txn.labeled_by))
            labeled_date = txn.labeled_at.strftime("%Y-%m-%d %H:%M") if txn.labeled_at else "N/A"
            self.setItem(row, 8, QTableWidgetItem(labeled_date))
            
            # Training count
            self.setItem(row, 9, QTableWidgetItem(str(txn.training_count)))
            
            # Last trained
            last_trained = txn.last_trained_at.strftime("%Y-%m-%d %H:%M") if txn.last_trained_at else "Never"
            self.setItem(row, 10, QTableWidgetItem(last_trained))
            
            # Store transaction hash_id in row data
            self.setItem(row, 0, QTableWidgetItem())
            self.item(row, 0).setData(Qt.UserRole, txn.hash_id)
    
    def get_selected_transactions(self) -> List[str]:
        """Get hash IDs of selected transactions"""
        selected = []
        for row in range(self.rowCount()):
            checkbox = self.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                hash_id = self.item(row, 0).data(Qt.UserRole)
                if hash_id:
                    selected.append(hash_id)
        return selected
    
    def select_all(self, checked: bool):
        """Select or deselect all transactions"""
        for row in range(self.rowCount()):
            checkbox = self.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(checked)
    
    def select_newly_labeled_only(self):
        """Select only newly labeled transactions"""
        for row in range(self.rowCount()):
            checkbox = self.cellWidget(row, 0)
            status_item = self.item(row, 1)
            if checkbox and status_item:
                is_newly_labeled = "Newly Labeled" in status_item.text()
                checkbox.setChecked(is_newly_labeled)


class TrainingProgressDialog(QDialog):
    """Dialog to show training progress"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Model Training Progress")
        self.setModal(True)
        self.resize(500, 300)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Preparing training data...")
        layout.addWidget(self.status_label)
        
        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # Close button (initially disabled)
        self.close_button = QPushButton("Close")
        self.close_button.setEnabled(False)
        self.close_button.clicked.connect(self.accept)
        layout.addWidget(self.close_button)
    
    def update_progress(self, value: int, message: str):
        """Update progress bar and status"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

        # Force UI update to prevent blank screen
        self.repaint()
        QApplication.processEvents()

        if value >= 100:
            self.close_button.setEnabled(True)


class MLTrainingInterface(QMainWindow):
    """Main ML Training Interface Window"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.setWindowTitle("ML Model Training Interface")
        self.setMinimumSize(1200, 800)
        
        # Initialize managers
        self.training_manager = TrainingDataManager()
        self.model_trainer = ModelTrainer()
        self.history_manager = TrainingHistoryManager()
        
        # Data
        self.all_transactions: List[UniqueTransaction] = []
        self.filtered_transactions: List[UniqueTransaction] = []

        # Initialize UI components to None first
        self.data_table = None
        self.search_box = None
        self.category_filter = None
        self.status_filter = None
        self.selection_info = None
        self.stats_text = None
        self.history_text = None

        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create tabs
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # Training Data Tab
        training_tab = self.create_training_tab()
        tab_widget.addTab(training_tab, "Training Data Selection")
        
        # Statistics Tab
        stats_tab = self.create_statistics_tab()
        tab_widget.addTab(stats_tab, "Training Statistics")

        # Training History Tab
        history_tab = self.create_history_tab()
        tab_widget.addTab(history_tab, "Training History")
        
    def create_training_tab(self) -> QWidget:
        """Create the main training data selection tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Create data table first (needed by controls)
        self.data_table = TrainingDataTableWidget()

        # Top controls (now that data_table exists)
        controls_layout = self.create_controls_section()
        layout.addLayout(controls_layout)

        # Add data table
        layout.addWidget(self.data_table)

        # Bottom action buttons
        actions_layout = self.create_actions_section()
        layout.addLayout(actions_layout)

        return widget

    def create_controls_section(self) -> QHBoxLayout:
        """Create the top controls section"""
        layout = QHBoxLayout()

        # Filter controls
        filter_group = QGroupBox("Filters")
        filter_layout = QHBoxLayout(filter_group)

        # Search box
        filter_layout.addWidget(QLabel("Search:"))
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search descriptions...")
        self.search_box.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.search_box)

        # Category filter
        filter_layout.addWidget(QLabel("Category:"))
        self.category_filter = QComboBox()
        self.category_filter.addItem("All Categories")
        self.category_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.category_filter)

        # Status filter
        filter_layout.addWidget(QLabel("Status:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All", "Newly Labeled", "Previously Trained"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.status_filter)

        layout.addWidget(filter_group)

        # Selection controls
        selection_group = QGroupBox("Selection")
        selection_layout = QHBoxLayout(selection_group)

        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_transactions)
        selection_layout.addWidget(self.select_all_btn)

        self.deselect_all_btn = QPushButton("Deselect All")
        self.deselect_all_btn.clicked.connect(self.deselect_all_transactions)
        selection_layout.addWidget(self.deselect_all_btn)

        self.select_new_btn = QPushButton("Select New Only")
        self.select_new_btn.clicked.connect(self.select_newly_labeled_only)
        selection_layout.addWidget(self.select_new_btn)

        layout.addWidget(selection_group)

        return layout

    def create_actions_section(self) -> QHBoxLayout:
        """Create the bottom actions section"""
        layout = QHBoxLayout()

        # Selection info
        self.selection_info = QLabel("No transactions selected")
        layout.addWidget(self.selection_info)

        layout.addStretch()

        # Action buttons
        self.refresh_btn = QPushButton("Refresh Data")
        self.refresh_btn.clicked.connect(self.load_data)
        layout.addWidget(self.refresh_btn)

        self.export_btn = QPushButton("Export Selected")
        self.export_btn.clicked.connect(self.export_selected_data)
        layout.addWidget(self.export_btn)

        self.train_btn = QPushButton("Start Training")
        self.train_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.train_btn.clicked.connect(self.start_training)
        layout.addWidget(self.train_btn)



        return layout

    def create_statistics_tab(self) -> QWidget:
        """Create the statistics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Statistics display
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        layout.addWidget(self.stats_text)

        # Refresh stats button
        refresh_stats_btn = QPushButton("Refresh Statistics")
        refresh_stats_btn.clicked.connect(self.update_statistics)
        layout.addWidget(refresh_stats_btn)

        return widget

    def create_history_tab(self) -> QWidget:
        """Create the training history tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # History display
        self.history_text = QTextEdit()
        self.history_text.setReadOnly(True)
        layout.addWidget(self.history_text)

        # Controls
        controls_layout = QHBoxLayout()

        refresh_history_btn = QPushButton("Refresh History")
        refresh_history_btn.clicked.connect(self.update_training_history)
        controls_layout.addWidget(refresh_history_btn)

        export_history_btn = QPushButton("Export History")
        export_history_btn.clicked.connect(self.export_training_history)
        controls_layout.addWidget(export_history_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        return widget

    def load_data(self):
        """Load all labeled transaction data"""
        try:
            # Debug: Check if training manager exists
            if not self.training_manager:
                self.logger.error("Training manager is None!")
                QMessageBox.critical(self, "Error", "Training manager not initialized!")
                return

            # Debug: Load unique transactions first to check total count
            unique_transactions = self.training_manager.data_preparator.load_unique_transactions()
            total_count = len(unique_transactions)
            self.logger.info(f"Total unique transactions in system: {total_count}")

            # Count manually labeled transactions
            manually_labeled_count = sum(1 for txn in unique_transactions.values() if txn.is_manually_labeled)
            self.logger.info(f"Manually labeled transactions: {manually_labeled_count}")

            # Load labeled transactions
            self.all_transactions = self.training_manager.get_all_labeled_transactions()
            self.logger.info(f"Loaded {len(self.all_transactions)} labeled transactions for training interface")

            # Show message if no data
            if len(self.all_transactions) == 0:
                if total_count == 0:
                    QMessageBox.information(
                        self, "No Data",
                        "No transactions found in the system.\n\n"
                        "Please import bank statements first:\n"
                        "1. Go to 'File' → 'Import Bank Statement'\n"
                        "2. Process your bank statement files\n"
                        "3. Then return to label and train"
                    )
                else:
                    QMessageBox.information(
                        self, "No Labeled Data",
                        f"Found {total_count} transactions but none are manually labeled.\n\n"
                        "Please label transactions first:\n"
                        "1. Go to 'Machine Learning' → 'Label Transactions'\n"
                        "2. Manually assign categories to at least 10-20 transactions\n"
                        "3. Then return here to train the model"
                    )

            self.update_category_filter()
            self.apply_filters()
            self.update_statistics()
            self.update_training_history()

        except Exception as e:
            self.logger.error(f"Error loading data: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"Failed to load training data: {str(e)}")

    def update_category_filter(self):
        """Update category filter dropdown with available categories"""
        categories = set()
        for txn in self.all_transactions:
            if txn.category:
                categories.add(txn.category)

        self.category_filter.clear()
        self.category_filter.addItem("All Categories")
        for category in sorted(categories):
            self.category_filter.addItem(category)

    def apply_filters(self):
        """Apply current filters to the data"""
        search_text = self.search_box.text().lower()
        category_filter = self.category_filter.currentText()
        status_filter = self.status_filter.currentText()

        self.filtered_transactions = []

        for txn in self.all_transactions:
            # Search filter
            if search_text and search_text not in txn.description.lower():
                continue

            # Category filter
            if category_filter != "All Categories" and txn.category != category_filter:
                continue

            # Status filter
            if status_filter == "Newly Labeled" and not txn.is_newly_labeled():
                continue
            elif status_filter == "Previously Trained" and not txn.is_previously_trained():
                continue

            self.filtered_transactions.append(txn)

        # Update table
        self.data_table.populate_data(self.filtered_transactions)
        self.update_selection_info()

    def update_selection_info(self):
        """Update selection information display"""
        if self.data_table and self.selection_info:
            selected_count = len(self.data_table.get_selected_transactions())
            total_count = len(self.filtered_transactions)
            self.selection_info.setText(f"{selected_count} of {total_count} transactions selected")

    def select_all_transactions(self):
        """Select all transactions"""
        if self.data_table:
            self.data_table.select_all(True)
            self.update_selection_info()

    def deselect_all_transactions(self):
        """Deselect all transactions"""
        if self.data_table:
            self.data_table.select_all(False)
            self.update_selection_info()

    def select_newly_labeled_only(self):
        """Select only newly labeled transactions"""
        if self.data_table:
            self.data_table.select_newly_labeled_only()
            self.update_selection_info()

    def update_statistics(self):
        """Update the statistics display"""
        try:
            stats = self.training_manager.get_training_data_stats()

            stats_text = f"""
<h3>📊 Training Data Statistics</h3>

<b>Overall Status:</b><br>
• Total Labeled Transactions: {stats.total_labeled_transactions}<br>
• Newly Labeled (Ready for Training): {stats.newly_labeled_transactions}<br>
• Previously Trained: {stats.previously_trained_transactions}<br>
• Training Sessions Conducted: {stats.training_sessions_count}<br>

<b>Last Training:</b><br>
• Date: {stats.last_training_date.strftime('%Y-%m-%d %H:%M:%S') if stats.last_training_date else 'Never'}<br>

<b>Category Distribution:</b><br>
"""

            for category, count in sorted(stats.categories_distribution.items()):
                percentage = (count / stats.total_labeled_transactions * 100) if stats.total_labeled_transactions > 0 else 0
                stats_text += f"• {category}: {count} ({percentage:.1f}%)<br>"

            stats_text += f"""
<br><b>Training Readiness:</b><br>
• Ready for Training: {stats.training_ready_count} transactions<br>
• Minimum Required: 10 transactions<br>
• Status: {'✅ Ready' if stats.training_ready_count >= 10 else '⚠️ Need more data'}<br>
"""

            self.stats_text.setHtml(stats_text)

        except Exception as e:
            self.logger.error(f"Error updating statistics: {str(e)}")
            self.stats_text.setPlainText(f"Error loading statistics: {str(e)}")

    def start_training(self):
        """Start the ML model training process"""
        selected_transactions = self.data_table.get_selected_transactions()

        if not selected_transactions:
            QMessageBox.warning(self, "No Selection", "Please select at least one transaction for training.")
            return

        if len(selected_transactions) < 10:
            reply = QMessageBox.question(
                self, "Insufficient Data",
                f"Only {len(selected_transactions)} transactions selected. "
                f"At least 10 transactions are recommended for effective training.\n\n"
                f"Do you want to continue anyway?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return

        # Confirm training
        newly_labeled_count = sum(1 for hash_id in selected_transactions
                                 for txn in self.filtered_transactions
                                 if txn.hash_id == hash_id and txn.is_newly_labeled())

        reply = QMessageBox.question(
            self, "Confirm Training",
            f"Start incremental training with {len(selected_transactions)} selected transactions?\n\n"
            f"• {newly_labeled_count} newly labeled transactions\n"
            f"• {len(selected_transactions) - newly_labeled_count} previously trained transactions\n\n"
            f"This will update the ML model while preserving existing knowledge.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.execute_training(selected_transactions)

    def execute_training(self, selected_hash_ids: List[str]):
        """Execute the actual training process"""
        try:
            # Create training session ID
            training_session_id = f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Count newly labeled vs previously trained
            newly_labeled_count = 0
            previously_trained_count = 0
            for hash_id in selected_hash_ids:
                for txn in self.filtered_transactions:
                    if txn.hash_id == hash_id:
                        if txn.is_newly_labeled():
                            newly_labeled_count += 1
                        elif txn.is_previously_trained():
                            previously_trained_count += 1
                        break

            # Show progress dialog with initial content
            progress_dialog = TrainingProgressDialog(self)
            progress_dialog.update_progress(0, "Initializing training process...")
            progress_dialog.show()

            # Force initial UI update
            QApplication.processEvents()

            # Update progress
            progress_dialog.update_progress(10, "Preparing training data...")

            # Create and start training job with selected transactions
            job_id = self.model_trainer.create_training_job("incremental", selected_hash_ids)

            # Start training session record
            session_record = self.history_manager.start_training_session(
                training_session_id, job_id, "incremental", selected_hash_ids,
                newly_labeled_count, previously_trained_count, "user",
                f"Incremental training with {len(selected_hash_ids)} selected transactions"
            )

            progress_dialog.update_progress(30, "Starting model training...")

            # Mark transactions as trained
            result = self.training_manager.mark_transactions_as_trained(
                selected_hash_ids, training_session_id
            )

            if result["error_count"] > 0:
                QMessageBox.warning(
                    self, "Training Warning",
                    f"Some transactions could not be marked as trained:\n" +
                    "\n".join(result["errors"][:5])  # Show first 5 errors
                )

            progress_dialog.update_progress(50, f"Training job created: {job_id}")

            # Start training (this should be async in real implementation)
            training_success = self.model_trainer.start_training(job_id, async_training=False)

            if training_success:
                progress_dialog.update_progress(90, "Training completed, updating records...")

                # Complete training session record
                self.history_manager.complete_training_session(
                    training_session_id, True, 0.85, "v1.0", ""  # Mock values - should get real metrics
                )

                progress_dialog.update_progress(100, "Training completed successfully!")

                # Refresh data to show updated training status
                self.load_data()

                QMessageBox.information(
                    self, "Training Complete",
                    f"Model training completed successfully!\n\n"
                    f"• Training Session: {training_session_id}\n"
                    f"• Transactions Trained: {result['success_count']}\n"
                    f"• Job ID: {job_id}\n"
                    f"• Newly Labeled: {newly_labeled_count}\n"
                    f"• Previously Trained: {previously_trained_count}"
                )
            else:
                progress_dialog.update_progress(100, "Training failed!")

                # Complete training session record with failure
                self.history_manager.complete_training_session(
                    training_session_id, False, 0.0, "", "Training job failed"
                )

                QMessageBox.critical(
                    self, "Training Failed",
                    "Model training failed. Please check the logs for details."
                )

        except Exception as e:
            self.logger.error(f"Error during training: {str(e)}")
            QMessageBox.critical(self, "Training Error", f"An error occurred during training: {str(e)}")



    def export_selected_data(self):
        """Export selected training data"""
        selected_transactions = self.data_table.get_selected_transactions()

        if not selected_transactions:
            QMessageBox.warning(self, "No Selection", "Please select transactions to export.")
            return

        # This would implement CSV export functionality
        QMessageBox.information(self, "Export", f"Export functionality for {len(selected_transactions)} transactions would be implemented here.")

    def update_training_history(self):
        """Update the training history display"""
        try:
            analytics = self.history_manager.get_training_analytics()
            recent_sessions = self.history_manager.get_recent_sessions(20)

            history_text = f"""
<h3>📈 Training Analytics</h3>

<b>Overall Performance:</b><br>
• Total Training Sessions: {analytics.total_sessions}<br>
• Successful Sessions: {analytics.successful_sessions}<br>
• Failed Sessions: {analytics.failed_sessions}<br>
• Success Rate: {analytics.success_rate:.1f}%<br>
• Total Transactions Trained: {analytics.total_transactions_trained}<br>
• Average Accuracy: {analytics.average_accuracy:.2f}<br>
• Last Training: {analytics.last_training_date.strftime('%Y-%m-%d %H:%M:%S') if analytics.last_training_date else 'Never'}<br>
• Training Frequency: Every {analytics.training_frequency_days:.1f} days<br>

<h3>📋 Recent Training Sessions</h3>

"""

            if recent_sessions:
                for session in recent_sessions:
                    status_icon = "✅" if session.status == "completed" else "❌" if session.status == "failed" else "⏳"
                    duration = ""
                    if session.completed_at and session.started_at:
                        duration_mins = (session.completed_at - session.started_at).total_seconds() / 60
                        duration = f" ({duration_mins:.1f} min)"

                    history_text += f"""
<b>{status_icon} {session.session_id}</b><br>
• Started: {session.started_at.strftime('%Y-%m-%d %H:%M:%S')}<br>
• Type: {session.job_type.title()}<br>
• Status: {session.status.title()}{duration}<br>
• Transactions: {session.total_transactions_selected} (New: {session.newly_labeled_count}, Previous: {session.previously_trained_count})<br>
• Accuracy: {session.training_accuracy:.2f}<br>
• Notes: {session.notes}<br>
<br>
"""
            else:
                history_text += "No training sessions found.<br>"

            self.history_text.setHtml(history_text)

        except Exception as e:
            self.logger.error(f"Error updating training history: {str(e)}")
            self.history_text.setPlainText(f"Error loading training history: {str(e)}")

    def export_training_history(self):
        """Export training history to CSV"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Training History",
                f"training_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                self.history_manager.export_training_history(file_path)
                QMessageBox.information(self, "Export Complete", f"Training history exported to:\n{file_path}")

        except Exception as e:
            self.logger.error(f"Error exporting training history: {str(e)}")
            QMessageBox.critical(self, "Export Error", f"Failed to export training history: {str(e)}")

    def closeEvent(self, event):
        """Handle window close event"""
        event.accept()
