"""
Auto-Label Review Dialog
UI for reviewing and managing auto-labeled transactions
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QLabel, QComboBox,
                           QCheckBox, QGroupBox, QTextEdit, QMessageBox,
                           QHeaderView, QProgressBar, QSplitter, QFrame)
from PyQt5.QtCore import Qt, QThread, Signal, QTimer
from PyQt5.QtGui import QFont, QColor, QBrush
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.logger import get_logger
from ..ml.enhanced_training_data_manager import (EnhancedTrainingDataManager, 
                                               AutoLabeledTransaction, AutoLabelStatus)


class AutoLabelReviewWorker(QThread):
    """Worker thread for batch processing auto-label reviews"""
    
    progress_updated = Signal(int, str)  # progress, status
    review_completed = Signal(dict)  # results
    error_occurred = Signal(str)  # error message
    
    def __init__(self, enhanced_manager: EnhancedTrainingDataManager, 
                 review_actions: List[Dict[str, Any]]):
        super().__init__()
        self.enhanced_manager = enhanced_manager
        self.review_actions = review_actions
        self.logger = get_logger(__name__)
    
    def run(self):
        """Run batch review processing"""
        try:
            total = len(self.review_actions)
            
            for i, action in enumerate(self.review_actions):
                # Update progress
                progress = int((i + 1) * 100 / total)
                self.progress_updated.emit(progress, f"Processing {i + 1}/{total}")
                
                # Small delay to show progress
                self.msleep(50)
            
            # Perform batch review
            results = self.enhanced_manager.batch_review_auto_labeled_transactions(
                self.review_actions, "user"
            )
            
            self.review_completed.emit(results)
            
        except Exception as e:
            self.logger.error(f"Error in batch review worker: {str(e)}", exc_info=True)
            self.error_occurred.emit(str(e))


class AutoLabelReviewDialog(QDialog):
    """Dialog for reviewing auto-labeled transactions"""
    
    def __init__(self, enhanced_manager: EnhancedTrainingDataManager, parent=None):
        super().__init__(parent)
        self.enhanced_manager = enhanced_manager
        self.logger = get_logger(__name__)
        
        # Data
        self.auto_labeled_transactions = []
        self.selected_transactions = []
        self.review_worker = None
        
        self.setup_ui()
        self.load_auto_labeled_transactions()
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_statistics)
        self.refresh_timer.start(5000)  # Refresh every 5 seconds
    
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("Auto-Label Review & Management")
        self.setMinimumSize(1000, 700)
        
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("Review Auto-Labeled Transactions")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(header_label)
        
        # Statistics panel
        self.setup_statistics_panel(layout)
        
        # Main content splitter
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # Transaction table
        self.setup_transaction_table(splitter)
        
        # Review panel
        self.setup_review_panel(splitter)
        
        # Control buttons
        self.setup_control_buttons(layout)
        
        # Progress bar (initially hidden)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
    
    def setup_statistics_panel(self, layout):
        """Setup statistics panel"""
        stats_group = QGroupBox("Auto-Labeling Statistics")
        stats_layout = QHBoxLayout(stats_group)
        
        self.total_label = QLabel("Total: 0")
        self.pending_label = QLabel("Pending: 0")
        self.confirmed_label = QLabel("Confirmed: 0")
        self.modified_label = QLabel("Modified: 0")
        self.rejected_label = QLabel("Rejected: 0")
        self.accuracy_label = QLabel("Accuracy: 0%")
        
        for label in [self.total_label, self.pending_label, self.confirmed_label,
                     self.modified_label, self.rejected_label, self.accuracy_label]:
            label.setStyleSheet("padding: 5px; border: 1px solid gray; border-radius: 3px;")
            stats_layout.addWidget(label)
        
        layout.addWidget(stats_group)
    
    def setup_transaction_table(self, splitter):
        """Setup transaction table"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        
        # Table controls
        controls_layout = QHBoxLayout()
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["All", "Pending Review", "Confirmed", "Modified", "Rejected"])
        self.filter_combo.currentTextChanged.connect(self.filter_transactions)
        controls_layout.addWidget(QLabel("Filter:"))
        controls_layout.addWidget(self.filter_combo)
        
        self.select_all_checkbox = QCheckBox("Select All Visible")
        self.select_all_checkbox.stateChanged.connect(self.toggle_select_all)
        controls_layout.addWidget(self.select_all_checkbox)
        
        controls_layout.addStretch()
        table_layout.addLayout(controls_layout)
        
        # Transaction table
        self.transaction_table = QTableWidget()
        self.transaction_table.setColumnCount(8)
        self.transaction_table.setHorizontalHeaderLabels([
            "Select", "Description", "Amount", "Suggested Category", 
            "Confidence", "Similarity", "Status", "Matched Transaction"
        ])
        
        # Configure table
        header = self.transaction_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Description column
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Category column
        
        self.transaction_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.transaction_table.setAlternatingRowColors(True)
        
        table_layout.addWidget(self.transaction_table)
        splitter.addWidget(table_frame)
    
    def setup_review_panel(self, splitter):
        """Setup review panel"""
        review_frame = QFrame()
        review_layout = QVBoxLayout(review_frame)
        
        review_label = QLabel("Batch Review Actions")
        review_label.setFont(QFont("Arial", 12, QFont.Bold))
        review_layout.addWidget(review_label)
        
        # Quick action buttons
        quick_actions_layout = QHBoxLayout()
        
        self.confirm_selected_btn = QPushButton("✓ Confirm Selected")
        self.confirm_selected_btn.clicked.connect(lambda: self.batch_action("confirm"))
        self.confirm_selected_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        quick_actions_layout.addWidget(self.confirm_selected_btn)
        
        self.reject_selected_btn = QPushButton("✗ Reject Selected")
        self.reject_selected_btn.clicked.connect(lambda: self.batch_action("reject"))
        self.reject_selected_btn.setStyleSheet("background-color: #f44336; color: white;")
        quick_actions_layout.addWidget(self.reject_selected_btn)
        
        quick_actions_layout.addStretch()
        review_layout.addLayout(quick_actions_layout)
        
        # Review log
        self.review_log = QTextEdit()
        self.review_log.setMaximumHeight(150)
        self.review_log.setReadOnly(True)
        review_layout.addWidget(QLabel("Review Log:"))
        review_layout.addWidget(self.review_log)
        
        splitter.addWidget(review_frame)
    
    def setup_control_buttons(self, layout):
        """Setup control buttons"""
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.load_auto_labeled_transactions)
        button_layout.addWidget(self.refresh_btn)
        
        self.clear_completed_btn = QPushButton("🗑️ Clear Completed")
        self.clear_completed_btn.clicked.connect(self.clear_completed_transactions)
        button_layout.addWidget(self.clear_completed_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def load_auto_labeled_transactions(self):
        """Load auto-labeled transactions from the enhanced manager"""
        try:
            self.auto_labeled_transactions = self.enhanced_manager.get_pending_auto_labeled_transactions()
            self.populate_transaction_table()
            self.refresh_statistics()
            
            self.logger.info(f"Loaded {len(self.auto_labeled_transactions)} auto-labeled transactions")
            
        except Exception as e:
            self.logger.error(f"Error loading auto-labeled transactions: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Error", f"Failed to load auto-labeled transactions:\n{str(e)}")
    
    def populate_transaction_table(self):
        """Populate the transaction table"""
        try:
            # Filter transactions based on current filter
            filtered_transactions = self.get_filtered_transactions()
            
            self.transaction_table.setRowCount(len(filtered_transactions))
            
            for row, txn in enumerate(filtered_transactions):
                # Checkbox for selection
                checkbox = QCheckBox()
                self.transaction_table.setCellWidget(row, 0, checkbox)
                
                # Description
                desc_item = QTableWidgetItem(txn.original_description[:50] + "..." if len(txn.original_description) > 50 else txn.original_description)
                self.transaction_table.setItem(row, 1, desc_item)
                
                # Amount
                amount_item = QTableWidgetItem(f"₹{txn.amount:,.2f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.transaction_table.setItem(row, 2, amount_item)
                
                # Suggested category
                category_item = QTableWidgetItem(f"{txn.suggested_category} > {txn.suggested_sub_category}")
                self.transaction_table.setItem(row, 3, category_item)
                
                # Confidence
                confidence_item = QTableWidgetItem(f"{txn.confidence_score:.2%}")
                confidence_item.setTextAlignment(Qt.AlignCenter)
                # Color code based on confidence
                if txn.confidence_score >= 0.9:
                    confidence_item.setBackground(QBrush(QColor(200, 255, 200)))  # Light green
                elif txn.confidence_score >= 0.8:
                    confidence_item.setBackground(QBrush(QColor(255, 255, 200)))  # Light yellow
                else:
                    confidence_item.setBackground(QBrush(QColor(255, 200, 200)))  # Light red
                self.transaction_table.setItem(row, 4, confidence_item)
                
                # Similarity
                similarity_item = QTableWidgetItem(f"{txn.similarity_score:.2%}")
                similarity_item.setTextAlignment(Qt.AlignCenter)
                self.transaction_table.setItem(row, 5, similarity_item)
                
                # Status
                status_item = QTableWidgetItem(txn.status.value.replace("_", " ").title())
                # Color code based on status
                if txn.status == AutoLabelStatus.PENDING_REVIEW:
                    status_item.setBackground(QBrush(QColor(255, 255, 200)))  # Yellow
                elif txn.status == AutoLabelStatus.CONFIRMED:
                    status_item.setBackground(QBrush(QColor(200, 255, 200)))  # Green
                elif txn.status == AutoLabelStatus.MODIFIED:
                    status_item.setBackground(QBrush(QColor(200, 200, 255)))  # Blue
                elif txn.status == AutoLabelStatus.REJECTED:
                    status_item.setBackground(QBrush(QColor(255, 200, 200)))  # Red
                self.transaction_table.setItem(row, 6, status_item)
                
                # Matched transaction (truncated)
                matched_item = QTableWidgetItem(txn.matched_transaction_id[:12] + "...")
                matched_item.setToolTip(f"Matched Transaction ID: {txn.matched_transaction_id}")
                self.transaction_table.setItem(row, 7, matched_item)
            
        except Exception as e:
            self.logger.error(f"Error populating transaction table: {str(e)}", exc_info=True)
    
    def get_filtered_transactions(self) -> List[AutoLabeledTransaction]:
        """Get transactions based on current filter"""
        filter_text = self.filter_combo.currentText()
        
        if filter_text == "All":
            return list(self.enhanced_manager.auto_labeled_transactions.values())
        elif filter_text == "Pending Review":
            return [txn for txn in self.enhanced_manager.auto_labeled_transactions.values() 
                   if txn.status == AutoLabelStatus.PENDING_REVIEW]
        elif filter_text == "Confirmed":
            return [txn for txn in self.enhanced_manager.auto_labeled_transactions.values() 
                   if txn.status == AutoLabelStatus.CONFIRMED]
        elif filter_text == "Modified":
            return [txn for txn in self.enhanced_manager.auto_labeled_transactions.values() 
                   if txn.status == AutoLabelStatus.MODIFIED]
        elif filter_text == "Rejected":
            return [txn for txn in self.enhanced_manager.auto_labeled_transactions.values() 
                   if txn.status == AutoLabelStatus.REJECTED]
        else:
            return []

    def filter_transactions(self):
        """Filter transactions based on selected filter"""
        self.populate_transaction_table()
        self.select_all_checkbox.setChecked(False)

    def toggle_select_all(self, state):
        """Toggle selection of all visible transactions"""
        for row in range(self.transaction_table.rowCount()):
            checkbox = self.transaction_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(state == Qt.Checked)

    def get_selected_transactions(self) -> List[str]:
        """Get hash IDs of selected transactions"""
        selected = []
        filtered_transactions = self.get_filtered_transactions()

        for row in range(self.transaction_table.rowCount()):
            checkbox = self.transaction_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked() and row < len(filtered_transactions):
                selected.append(filtered_transactions[row].hash_id)

        return selected

    def batch_action(self, action: str):
        """Perform batch action on selected transactions"""
        try:
            selected_hash_ids = self.get_selected_transactions()

            if not selected_hash_ids:
                QMessageBox.warning(self, "No Selection", "Please select transactions to process.")
                return

            # Confirm action
            action_text = "confirm" if action == "confirm" else "reject"
            reply = QMessageBox.question(
                self, f"Batch {action_text.title()}",
                f"Are you sure you want to {action_text} {len(selected_hash_ids)} selected transactions?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Prepare review actions
            review_actions = []
            for hash_id in selected_hash_ids:
                review_actions.append({
                    "hash_id": hash_id,
                    "action": action
                })

            # Start batch processing
            self.start_batch_processing(review_actions)

        except Exception as e:
            self.logger.error(f"Error in batch action: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Error", f"Failed to perform batch action:\n{str(e)}")

    def start_batch_processing(self, review_actions: List[Dict[str, Any]]):
        """Start batch processing in worker thread"""
        try:
            # Show progress bar
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # Disable buttons during processing
            self.confirm_selected_btn.setEnabled(False)
            self.reject_selected_btn.setEnabled(False)
            self.refresh_btn.setEnabled(False)

            # Create and start worker
            self.review_worker = AutoLabelReviewWorker(self.enhanced_manager, review_actions)
            self.review_worker.progress_updated.connect(self.update_progress)
            self.review_worker.review_completed.connect(self.on_batch_review_completed)
            self.review_worker.error_occurred.connect(self.on_batch_review_error)
            self.review_worker.start()

        except Exception as e:
            self.logger.error(f"Error starting batch processing: {str(e)}", exc_info=True)
            self.on_batch_review_error(str(e))

    def update_progress(self, progress: int, status: str):
        """Update progress bar"""
        self.progress_bar.setValue(progress)
        self.progress_bar.setFormat(f"{status} - {progress}%")

    def on_batch_review_completed(self, results: Dict[str, Any]):
        """Handle batch review completion"""
        try:
            # Hide progress bar
            self.progress_bar.setVisible(False)

            # Re-enable buttons
            self.confirm_selected_btn.setEnabled(True)
            self.reject_selected_btn.setEnabled(True)
            self.refresh_btn.setEnabled(True)

            # Log results
            log_message = f"Batch review completed: {results['total_reviewed']} transactions processed\n"
            log_message += f"Confirmed: {results['confirmed']}, Modified: {results['modified']}, Rejected: {results['rejected']}\n"

            if results.get('errors'):
                log_message += f"Errors: {len(results['errors'])}\n"
                for error in results['errors'][:5]:  # Show first 5 errors
                    log_message += f"  - {error}\n"

            self.review_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] {log_message}")

            # Refresh data
            self.load_auto_labeled_transactions()

            # Show completion message
            QMessageBox.information(
                self, "Batch Review Complete",
                f"Successfully processed {results['total_reviewed']} transactions.\n\n"
                f"Confirmed: {results['confirmed']}\n"
                f"Modified: {results['modified']}\n"
                f"Rejected: {results['rejected']}\n"
                f"Errors: {len(results.get('errors', []))}"
            )

        except Exception as e:
            self.logger.error(f"Error handling batch review completion: {str(e)}", exc_info=True)

    def on_batch_review_error(self, error_message: str):
        """Handle batch review error"""
        # Hide progress bar
        self.progress_bar.setVisible(False)

        # Re-enable buttons
        self.confirm_selected_btn.setEnabled(True)
        self.reject_selected_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)

        # Log error
        self.review_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] ERROR: {error_message}")

        # Show error message
        QMessageBox.critical(self, "Batch Review Error", f"Batch review failed:\n{error_message}")

    def refresh_statistics(self):
        """Refresh statistics display"""
        try:
            stats = self.enhanced_manager.get_auto_labeling_statistics()

            self.total_label.setText(f"Total: {stats.get('total_auto_labeled', 0)}")
            self.pending_label.setText(f"Pending: {stats.get('pending_review', 0)}")
            self.confirmed_label.setText(f"Confirmed: {stats.get('status_breakdown', {}).get('confirmed', 0)}")
            self.modified_label.setText(f"Modified: {stats.get('status_breakdown', {}).get('modified', 0)}")
            self.rejected_label.setText(f"Rejected: {stats.get('status_breakdown', {}).get('rejected', 0)}")
            self.accuracy_label.setText(f"Accuracy: {stats.get('accuracy', 0):.1%}")

        except Exception as e:
            self.logger.error(f"Error refreshing statistics: {str(e)}", exc_info=True)

    def clear_completed_transactions(self):
        """Clear completed (confirmed/rejected) transactions from the list"""
        try:
            reply = QMessageBox.question(
                self, "Clear Completed",
                "This will remove all confirmed and rejected transactions from the review list.\n"
                "The training data will be preserved.\n\n"
                "Are you sure you want to continue?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # Remove completed transactions
            completed_count = 0
            to_remove = []

            for hash_id, txn in self.enhanced_manager.auto_labeled_transactions.items():
                if txn.status in [AutoLabelStatus.CONFIRMED, AutoLabelStatus.REJECTED]:
                    to_remove.append(hash_id)
                    completed_count += 1

            for hash_id in to_remove:
                del self.enhanced_manager.auto_labeled_transactions[hash_id]

            # Save updated data
            self.enhanced_manager._save_auto_labeled_transactions()

            # Refresh display
            self.load_auto_labeled_transactions()

            QMessageBox.information(
                self, "Clear Complete",
                f"Removed {completed_count} completed transactions from the review list."
            )

        except Exception as e:
            self.logger.error(f"Error clearing completed transactions: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Error", f"Failed to clear completed transactions:\n{str(e)}")

    def closeEvent(self, event):
        """Handle dialog close event"""
        # Stop refresh timer
        if self.refresh_timer:
            self.refresh_timer.stop()

        # Stop worker if running
        if self.review_worker and self.review_worker.isRunning():
            self.review_worker.quit()
            self.review_worker.wait()

        event.accept()
